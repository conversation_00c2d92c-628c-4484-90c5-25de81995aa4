'use server';

/**
 * @fileOverview Paraphrases Arabic text.
 *
 * - paraphraseArabicText - A function that rephrases Arabic text while maintaining the original meaning.
 * - ParaphraseArabicTextInput - The input type for the paraphraseArabicText function.
 * - ParaphraseArabicTextOutput - The return type for the paraphraseArabicText function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const ParaphraseArabicTextInputSchema = z.object({
  text: z.string().describe('The Arabic text to paraphrase.'),
});
export type ParaphraseArabicTextInput = z.infer<typeof ParaphraseArabicTextInputSchema>;

const ParaphraseArabicTextOutputSchema = z.object({
  paraphrasedText: z.string().describe('النص المعاد صياغته باللغة العربية.'),
});
export type ParaphraseArabicTextOutput = z.infer<typeof ParaphraseArabicTextOutputSchema>;

export async function paraphraseArabicText(input: ParaphraseArabicTextInput): Promise<ParaphraseArabicTextOutput> {
  return paraphraseArabicTextFlow(input);
}

const paraphraseArabicTextPrompt = ai.definePrompt({
  name: 'paraphraseArabicTextPrompt',
  input: {schema: ParaphraseArabicTextInputSchema},
  output: {schema: ParaphraseArabicTextOutputSchema},
  prompt: `أعد صياغة النص العربي التالي بأسلوب مختلف مع الحفاظ على المعنى الأصلي بشكل كامل. تجنب إضافة أي معلومات غير موجودة في النص الأصلي. النص:\n\n{{{text}}}`,
});

const paraphraseArabicTextFlow = ai.defineFlow(
  {
    name: 'paraphraseArabicTextFlow',
    inputSchema: ParaphraseArabicTextInputSchema,
    outputSchema: ParaphraseArabicTextOutputSchema,
  },
  async input => {
    const {output} = await paraphraseArabicTextPrompt(input);
    return output!;
  }
);
