
'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Download, Wifi, Link as LinkIcon, MessageSquare, Phone, Mail, MapPin, QrCode as QrCodeIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { Switch } from '../ui/switch';


const QrTypes = {
  URL: 'url',
  TEXT: 'text',
  WIFI: 'wifi',
  PHONE: 'phone',
  EMAIL: 'email',
  SMS: 'sms',
  LOCATION: 'location',
} as const;

// Zod schema for validation
const FormSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal(QrTypes.URL),
    url: z.string().min(1, { message: "الرجاء إدخال رابط." }).transform((val) => {
        if (val && !/^(https?:\/\/|ftp:\/\/)/i.test(val)) {
          return `https://${val}`;
        }
        return val;
      }).refine((val) => {
        try {
          new URL(val);
          return true;
        } catch {
          return false;
        }
    }, { message: "الرجاء إدخال رابط صالح." }),
  }),
  z.object({
    type: z.literal(QrTypes.TEXT),
    text: z.string().min(1, { message: "الرجاء إدخال نص." }),
  }),
  z.object({
    type: z.literal(QrTypes.WIFI),
    wifiSsid: z.string().min(1, { message: "اسم الشبكة مطلوب." }),
    wifiPassword: z.string().optional(),
    wifiSecurity: z.enum(['WPA', 'WEP', 'nopass']),
    wifiHidden: z.boolean().default(false),
  }),
  z.object({
    type: z.literal(QrTypes.PHONE),
    phoneNumber: z.string().min(7, { message: "رقم الهاتف قصير جدًا." }),
  }),
  z.object({
    type: z.literal(QrTypes.EMAIL),
    emailAddress: z.string().email({ message: "الرجاء إدخال بريد إلكتروني صالح." }),
    emailSubject: z.string().optional(),
    emailBody: z.string().optional(),
  }),
  z.object({
    type: z.literal(QrTypes.SMS),
    smsNumber: z.string().min(7, { message: "رقم الهاتف قصير جدًا." }),
    smsMessage: z.string().optional(),
  }),
  z.object({
    type: z.literal(QrTypes.LOCATION),
    latitude: z.string().regex(/^-?([1-8]?[1-9]|[1-9]0)\.{1}\d{1,6}/, 'خط عرض غير صالح'),
    longitude: z.string().regex(/^-?([1-9]|[1-9][0-9]|1[0-7][0-9]|180)\.{1}\d{1,6}/, 'خط طول غير صالح'),
  }),
]).and(z.object({
  size: z.enum(['150', '200', '250', '300']),
}));

type FormValues = z.infer<typeof FormSchema>;

export function QrCodeGeneratorTool() {
  const [generatedImageUrl, setGeneratedImageUrl] = useState('');
  const { toast } = useToast();

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    mode: 'onChange',
    defaultValues: {
      type: QrTypes.URL,
      url: 'adawat.org',
      size: '250',
    },
  });

  const { watch } = form;
  const formValues = watch();

  useEffect(() => {
    const generateQrCode = () => {
        let data = '';
        
        const parseResult = FormSchema.safeParse(form.getValues());
    
        if (!parseResult.success) {
          setGeneratedImageUrl('');
          return;
        }
        
        const validatedData = parseResult.data;
    
        switch (validatedData.type) {
          case QrTypes.URL: data = validatedData.url || ''; break;
          case QrTypes.TEXT: data = validatedData.text || ''; break;
          case QrTypes.WIFI:
            const hidden = validatedData.wifiHidden ? 'H:true' : 'H:false';
            data = `WIFI:S:${validatedData.wifiSsid};T:${validatedData.wifiSecurity};P:${validatedData.wifiPassword || ''};${hidden};;`;
            break;
          case QrTypes.PHONE: data = `tel:${validatedData.phoneNumber}`; break;
          case QrTypes.EMAIL: data = `mailto:${validatedData.emailAddress}?subject=${encodeURIComponent(validatedData.emailSubject || '')}&body=${encodeURIComponent(validatedData.emailBody || '')}`; break;
          case QrTypes.SMS: data = `sms:${validatedData.smsNumber}?body=${encodeURIComponent(validatedData.smsMessage || '')}`; break;
          case QrTypes.LOCATION: data = `geo:${validatedData.latitude},${validatedData.longitude}`; break;
        }
    
        if (data) {
            const sizeParam = `${validatedData.size}x${validatedData.size}`;
            const url = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(data)}&size=${sizeParam}&format=png`;
            setGeneratedImageUrl(url);
        } else {
          setGeneratedImageUrl('');
        }
    }
    generateQrCode();
  }, [formValues, form]);
  
  const handleDownload = async () => {
    if (!generatedImageUrl) return;

    try {
        const response = await fetch(generatedImageUrl);
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `qr-code-${form.getValues('type')}-${new Date().getTime()}.png`;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
    } catch (error) {
        console.error("Error downloading the image:", error);
        toast({
            title: "خطأ في التحميل",
            description: "لم نتمكن من تحميل الصورة. الرجاء المحاولة مرة أخرى.",
            variant: "destructive",
        });
    }
};

  const allQrTypeOptions = [
    { value: QrTypes.URL, label: 'رابط موقع', icon: LinkIcon },
    { value: QrTypes.TEXT, label: 'نص عادي', icon: MessageSquare },
    { value: QrTypes.PHONE, label: 'رقم هاتف', icon: Phone },
    { value: QrTypes.WIFI, label: 'شبكة WiFi', icon: Wifi },
    { value: QrTypes.EMAIL, label: 'بريد إلكتروني', icon: Mail },
    { value: QrTypes.SMS, label: 'رسالة نصية', icon: MessageSquare },
    { value: QrTypes.LOCATION, label: 'موقع جغرافي', icon: MapPin },
  ];
  
  const selectedDataType = form.watch('type');

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2"><QrCodeIcon/> مولد رمز QR</CardTitle>
        <CardDescription>إنشاء رموز QR مخصصة لأنواع مختلفة من البيانات.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>نوع البيانات</FormLabel>
                        <Select onValueChange={(value) => field.onChange(value as typeof QrTypes[keyof typeof QrTypes])} value={field.value}>
                          <FormControl><SelectTrigger><SelectValue placeholder="اختر نوع البيانات" /></SelectTrigger></FormControl>
                          <SelectContent>
                            {allQrTypeOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                <div className="flex items-center gap-2"><option.icon className="h-4 w-4" />{option.label}</div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormItem>
                    )}
                  />
                  <FormField control={form.control} name="size" render={({ field }) => (
                        <FormItem>
                          <FormLabel>الحجم</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="150">صغير (150px)</SelectItem>
                              <SelectItem value="200">متوسط (200px)</SelectItem>
                              <SelectItem value="250">كبير (250px)</SelectItem>
                              <SelectItem value="300">كبير جداً (300px)</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )} />
              </div>

              {selectedDataType === QrTypes.URL && <FormField control={form.control} name="url" render={({ field }) => (<FormItem><FormLabel>رابط الموقع</FormLabel><FormControl><Input placeholder="example.com" {...field} /></FormControl><FormMessage /></FormItem>)} />}
              {selectedDataType === QrTypes.TEXT && <FormField control={form.control} name="text" render={({ field }) => (<FormItem><FormLabel>النص</FormLabel><FormControl><Textarea placeholder="اكتب النص هنا..." className="min-h-[100px]" {...field} /></FormControl><FormMessage /></FormItem>)} />}
              {selectedDataType === QrTypes.WIFI && (
                <div className="space-y-4 rounded-md border p-4">
                  <FormField control={form.control} name="wifiSsid" render={({ field }) => (<FormItem><FormLabel>اسم الشبكة (SSID)</FormLabel><FormControl><Input placeholder="اسم شبكة WiFi" {...field} /></FormControl><FormMessage /></FormItem>)} />
                  <FormField control={form.control} name="wifiPassword" render={({ field }) => (<FormItem><FormLabel>كلمة المرور</FormLabel><FormControl><Input type="password" placeholder={form.watch('wifiSecurity') === 'nopass' ? "لا حاجة لكلمة مرور" : "كلمة مرور الشبكة"} {...field} disabled={form.watch('wifiSecurity') === 'nopass'} /></FormControl><FormMessage /></FormItem>)} />
                  <div className="grid grid-cols-2 gap-4">
                    <FormField control={form.control} name="wifiSecurity" render={({ field }) => (<FormItem><FormLabel>نوع الحماية</FormLabel><Select onValueChange={field.onChange} value={field.value}><FormControl><SelectTrigger><SelectValue /></SelectTrigger></FormControl><SelectContent><SelectItem value="WPA">WPA/WPA2</SelectItem><SelectItem value="WEP">WEP</SelectItem><SelectItem value="nopass">بدون حماية</SelectItem></SelectContent></Select><FormMessage /></FormItem>)} />
                    <FormField control={form.control} name="wifiHidden" render={({ field }) => (<FormItem className="flex flex-col"><FormLabel>شبكة مخفية</FormLabel><div className="flex items-center gap-2 pt-2"><FormControl><Switch checked={field.value} onCheckedChange={field.onChange} /></FormControl><span className="text-sm text-muted-foreground">{field.value ? 'نعم' : 'لا'}</span></div><FormMessage /></FormItem>)} />
                  </div>
                </div>
              )}
              {selectedDataType === QrTypes.PHONE && <FormField control={form.control} name="phoneNumber" render={({ field }) => (<FormItem><FormLabel>رقم الهاتف</FormLabel><FormControl><Input dir="ltr" placeholder="+966501234567" {...field} /></FormControl><FormMessage /></FormItem>)} />}
              {selectedDataType === QrTypes.EMAIL && <FormField control={form.control} name="emailAddress" render={({ field }) => (<FormItem><FormLabel>عنوان البريد الإلكتروني</FormLabel><FormControl><Input placeholder="<EMAIL>" {...field} /></FormControl><FormMessage /></FormItem>)} />}
              {selectedDataType === QrTypes.SMS && <FormField control={form.control} name="smsNumber" render={({ field }) => (<FormItem><FormLabel>رقم الهاتف</FormLabel><FormControl><Input dir="ltr" placeholder="+966501234567" {...field} /></FormControl><FormMessage /></FormItem>)} />}
            </div>

            <div className="flex flex-col items-center gap-6 pt-6 lg:pt-0">
              <div className={cn("bg-muted rounded-lg flex items-center justify-center border-2 border-dashed p-2 transition-all", generatedImageUrl ? 'border-primary/50' : 'border-muted-foreground/50')} 
                style={{ width: `${parseInt(form.getValues('size') || '250', 10) + 16}px`, height: `${parseInt(form.getValues('size') || '250', 10) + 16}px` }}>
                {generatedImageUrl ? (<Image src={generatedImageUrl} alt="Generated Code" width={parseInt(form.getValues('size'), 10)} height={parseInt(form.getValues('size'), 10)} className="rounded-md" />) : (<p className="text-muted-foreground text-center px-4">سيظهر الرمز هنا بعد إدخال البيانات الصحيحة</p>)}
              </div>
              {generatedImageUrl && (
                <div className="flex flex-col gap-3 w-full max-w-xs">
                    <Button className="w-full" onClick={handleDownload}>
                        <Download className="ml-2 h-4 w-4" /> تحميل الصورة (PNG)
                    </Button>
                </div>
              )}
            </div>
          </div>
        </Form>
      </CardContent>
    </Card>
  );
}
