'use client';

import { useState, useTransition } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertTriangle, Sparkles, Globe, Loader2, Weight } from 'lucide-react';
import { COUNTRIES_CURRENCIES } from '@/lib/constants/currencies';
import { getGoldAndSilverPrices } from '@/lib/actions/gold';
import { cn } from '@/lib/utils';

type PriceData = Awaited<ReturnType<typeof getGoldAndSilverPrices>>;

interface GoldPriceToolProps {
  initialData: PriceData;
}

export function GoldPriceTool({ initialData }: GoldPriceToolProps) {
  const [currentData, setCurrentData] = useState<PriceData>(initialData);
  const [isPending, startTransition] = useTransition();
  
  const { success, prices, timestamp, error, source, selectedCurrency, countryInfo } = currentData;

  const handleCurrencyChange = (newCurrency: string) => {
    startTransition(async () => {
      try {
        console.log('Changing currency to:', newCurrency);
        const newData = await getGoldAndSilverPrices(newCurrency);
        setCurrentData(newData);
      } catch (error) {
        console.error('Error changing currency:', error);
      }
    });
  };

  if (!success) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>خطأ في تحميل البيانات</AlertTitle>
        <AlertDescription>
          لم نتمكن من تحميل أسعار الذهب. الرجاء المحاولة مرة أخرى لاحقًا.
          <p className="mt-2 text-xs">تفاصيل الخطأ: {error}</p>
        </AlertDescription>
      </Alert>
    );
  }

  // Convert karat prices object to array for table display
  const karatPricesArray = Object.entries(prices.gold.karats).map(([karat, priceLocal]) => ({
    karat,
    price_gram_local: priceLocal,
    price_gram_usd: priceLocal / (prices.gold.local.perGram / prices.gold.usd.perGram), // Convert back to USD
  }));

  const KiloComponent = ({ isMobile }: { isMobile: boolean }) => (
    <div
      className={
        isMobile
          ? 'p-4 rounded-lg bg-muted/50 border'
          : ''
      }
    >
      <div className={cn(isMobile ? "flex justify-between items-center mb-2" : "flex items-center gap-2")}>
        {isMobile ? (
          <>
            <div className="flex items-center gap-2">
              <Weight className="h-5 w-5 text-muted-foreground" />
              <span className="font-semibold text-base">الكيلو (عيار 24)</span>
            </div>
          </>
        ) : (
          <>
            <Weight className="h-5 w-5 text-muted-foreground" />
            <span className="font-semibold text-base">الكيلو (عيار 24)</span>
          </>
        )}
      </div>
      {isMobile && (
        <>
          <div className={cn("flex justify-between items-baseline")}>
            <span className="text-xs text-muted-foreground">السعر ({countryInfo?.currency})</span>
            <span className={cn("font-mono font-bold text-base")}>
                {prices.gold.local.perKilo.toLocaleString('ar-SA-u-nu-latn', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} {selectedCurrency}
            </span>
          </div>
          <div className={cn("flex justify-between items-baseline mt-1")}>
            <span className="text-xs text-muted-foreground">السعر (دولار أمريكي)</span>
            <span className={cn("font-mono font-bold text-base")}>
                ${prices.gold.usd.perKilo.toLocaleString('ar-SA-u-nu-latn', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </span>
          </div>
        </>
      )}
    </div>
  );


  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="text-yellow-500" />
          أسعار الذهب اليوم
          {isPending && <Loader2 className="h-4 w-4 animate-spin text-blue-500" />}
        </CardTitle>
        <CardDescription>
          آخر تحديث: {new Date(timestamp).toLocaleString('ar-SA-u-nu-latn', { dateStyle: 'medium', timeStyle: 'short' })}
          {source && <span className="mx-2">•</span>}
          {source && <span>المصدر: {source}</span>}
          {error && (
            <span className="block text-amber-600 text-xs mt-1">تنبيه: {error}</span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* اختيار الدولة والعملة */}
        <div className="mb-6">
          <div className="flex items-center gap-4">
            <Globe className="h-5 w-5 text-muted-foreground" />
            <div className="flex-1">
              <label className="text-sm font-medium mb-2 block">اختر الدولة والعملة:</label>
              <Select 
                value={selectedCurrency} 
                onValueChange={handleCurrencyChange}
                disabled={isPending}
              >
                <SelectTrigger className="w-full md:w-80">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(COUNTRIES_CURRENCIES).map(([code, info]) => (
                    <SelectItem key={code} value={code}>
                      <div className="flex flex-col">
                        <span className="font-medium">{info.name}</span>
                        <span className="text-xs text-muted-foreground">{info.currency} ({code})</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {countryInfo && (
            <div className="mt-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-sm text-blue-800">
                <strong>{countryInfo.name}</strong> - العملة: {countryInfo.currency}
              </p>
            </div>
          )}
        </div>

        {/* عرض الأسعار الرئيسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="p-4 bg-yellow-400/10 rounded-lg text-center border border-yellow-400/20">
            <p className="text-sm text-yellow-600">سعر الأونصة (دولار أمريكي)</p>
            <p className="text-3xl font-bold font-mono text-yellow-700">
              ${prices.gold.usd.perOunce.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </p>
          </div>
          <div className="p-4 bg-green-500/10 rounded-lg text-center border border-green-500/20">
            <p className="text-sm text-green-600">سعر الجرام (24K) - {countryInfo?.currency}</p>
            <p className="text-3xl font-bold font-mono text-green-700">
              {prices.gold.karats['24K'].toLocaleString('ar-SA-u-nu-latn', { 
                minimumFractionDigits: 2, 
                maximumFractionDigits: 2,
              })} {selectedCurrency}
            </p>
          </div>
        </div>
        
        {/* جدول العيارات */}
        <div className="md:hidden">
          <div className="space-y-4">
            {/* بطاقات العيارات للجوال */}
            {karatPricesArray.map((price) => (
              <div key={price.karat} className="p-4 rounded-lg border">
                <div className="flex justify-between items-center mb-2">
                  <Badge variant="outline" className="text-lg border-yellow-500 text-yellow-600">{price.karat} قيراط</Badge>
                </div>
                <div className="flex justify-between items-baseline">
                  <span className="text-xs text-muted-foreground">السعر ({countryInfo?.currency})</span>
                  <span className="font-mono text-lg">{price.price_gram_local.toFixed(2)} {selectedCurrency}</span>
                </div>
                 <div className="flex justify-between items-baseline mt-1">
                  <span className="text-xs text-muted-foreground">السعر (دولار أمريكي)</span>
                  <span className="font-mono text-lg">${price.price_gram_usd.toFixed(2)}</span>
                </div>
              </div>
            ))}
             {/* بطاقة سعر الكيلو للجوال */}
            <KiloComponent isMobile={true} />
          </div>
        </div>
        
        <Table className="hidden md:table">
          <TableHeader>
            <TableRow>
              <TableHead>العيار/الوحدة</TableHead>
              <TableHead className="text-left">السعر ({countryInfo?.currency})</TableHead>
              <TableHead className="text-left">السعر (دولار أمريكي)</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {karatPricesArray.map((price) => (
              <TableRow key={price.karat}>
                <TableCell>
                  <Badge variant="outline" className="text-lg border-yellow-500 text-yellow-600">{price.karat} قيراط</Badge>
                </TableCell>
                <TableCell className="text-left font-mono text-lg">
                  {price.price_gram_local.toFixed(2)} {selectedCurrency}
                </TableCell>
                <TableCell className="text-left font-mono text-lg">
                  ${price.price_gram_usd.toFixed(2)}
                </TableCell>
              </TableRow>
            ))}
             {/* سعر الكيلو */}
            <TableRow className="bg-muted/50">
                <TableCell>
                    <KiloComponent isMobile={false} />
                </TableCell>
                 <TableCell className="text-left font-mono font-bold text-base">
                  {prices.gold.local.perKilo.toLocaleString('ar-SA-u-nu-latn', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} {selectedCurrency}
                </TableCell>
                <TableCell className="text-left font-mono font-bold text-base">
                  ${prices.gold.usd.perKilo.toLocaleString('ar-SA-u-nu-latn', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </TableCell>
            </TableRow>
          </TableBody>
        </Table>
        
        <p className="text-xs text-muted-foreground mt-4">
          الأسعار المقدمة محدثة من مصادر موثوقة وهي لأغراض إعلامية فقط. قد تختلف الأسعار الفعلية حسب السوق المحلي والتجار.
        </p>
      </CardContent>
    </Card>
  );
}
