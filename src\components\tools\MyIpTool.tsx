import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Globe } from 'lucide-react';

interface MyIpToolProps {
  initialData: {
    ip: string;
    countryCode?: string;
    countryName?: string;
  };
}

export function MyIpTool({ initialData }: MyIpToolProps) {
  const { ip, countryName, countryCode } = initialData || { ip: 'لا يمكن تحديده' };

  return (
    <Card>
      <CardHeader>
        <CardTitle>عنوان IP الخاص بك</CardTitle>
        <CardDescription>هذا هو عنوان بروتوكول الإنترنت العام الذي تستخدمه حاليًا للاتصال بالإنترنت.</CardDescription>
      </CardHeader>
      <CardContent className="text-center">
        <div className="p-8 bg-muted rounded-lg flex flex-col items-center gap-4">
            <Globe className="w-12 h-12 text-primary" />
          <p className="text-4xl font-bold font-mono text-primary select-all" dir="ltr">{ip}</p>
          {countryName && (
            <p className="text-lg text-muted-foreground">
              محدد من دولة: {countryName} ({countryCode})
            </p>
          )}
        </div>
        <p className="text-xs text-muted-foreground mt-4 text-center">
            قد يختلف هذا العنوان إذا كنت تستخدم شبكة افتراضية خاصة (VPN) أو خادم وكيل (Proxy).
        </p>
      </CardContent>
    </Card>
  );
}
