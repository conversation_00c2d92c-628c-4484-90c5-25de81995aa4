
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  hourlyWage: requiredNumber().positive("أجر الساعة يجب أن يكون رقمًا موجبًا."),
  overtimeHours: requiredNumber().positive("ساعات العمل الإضافي يجب أن تكون رقمًا موجبًا."),
  overtimeRate: requiredNumber().positive("معدل الأجر الإضافي يجب أن يكون رقمًا موجبًا.").default(1.5),
});

interface Result {
  totalOvertimePay: number;
  basePay: number;
  extraPay: number;
}

export function OvertimeCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      overtimeRate: 1.5,
    }
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const { hourlyWage, overtimeHours, overtimeRate } = data;
    const totalOvertimePay = hourlyWage * overtimeHours * overtimeRate;
    const basePay = hourlyWage * overtimeHours;
    const extraPay = totalOvertimePay - basePay;
    
    setResult({ totalOvertimePay, basePay, extraPay });
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حساب الأجر الإضافي (Overtime)</CardTitle>
        <CardDescription>احسب قيمة تعويضك عن ساعات العمل الإضافية.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField name="hourlyWage" control={form.control} render={({ field }) => (
                  <FormItem><FormLabel>أجر الساعة الأساسي</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
              <FormField name="overtimeHours" control={form.control} render={({ field }) => (
                  <FormItem><FormLabel>عدد الساعات الإضافية</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
            </div>
            <FormField name="overtimeRate" control={form.control} render={({ field }) => (
                <FormItem>
                  <FormLabel>معدل الأجر الإضافي (مثال: 1.5 لـ 150%)</FormLabel>
                  <FormControl><Input type="number" step="0.1" {...field} /></FormControl>
                  <FormMessage />
                </FormItem>
            )}/>
            <Button type="submit" className="w-full">احسب الأجر الإضافي</Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-4">مقابل العمل الإضافي</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-secondary rounded-lg">
                <p className="text-2xl font-bold font-mono">{result.basePay.toFixed(2)}</p>
                <p className="text-sm text-muted-foreground">الأجر الأساسي</p>
              </div>
              <div className="p-4 bg-secondary rounded-lg">
                <p className="text-2xl font-bold font-mono text-green-600">+{result.extraPay.toFixed(2)}</p>
                <p className="text-sm text-muted-foreground">العلاوة الإضافية</p>
              </div>
              <div className="p-4 bg-primary/10 rounded-lg">
                <p className="text-4xl font-bold font-mono text-primary">{result.totalOvertimePay.toFixed(2)}</p>
                <p className="text-sm text-muted-foreground">الإجمالي المستحق</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
