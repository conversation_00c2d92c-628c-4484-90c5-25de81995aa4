
'use client';

import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Gem, Repeat, User, UserRound } from 'lucide-react';

type Gender = 'male' | 'female';

const getQuestionText = (question: any, gender: Gender): string => {
  if (typeof question.text === 'string') {
    return question.text;
  }
  return question.text[gender];
};

const getAnswerText = (answer: any, gender: Gender): string => {
    if (typeof answer.text === 'string') {
      return answer.text;
    }
    return answer.text[gender];
};

type Answer = { text: string | { male: string; female: string }; points: number };
type Question = { text: string | { male: string; female: string }; answers: Answer[] };

const questions: Question[] = [
  // Resilience
  {
    text: "عندما تواجه فشلاً أو نكسة كبيرة، ما هو رد فعلك الأولي؟",
    answers: [
      { text: "أعتبره فرصة للتعلم وأبدأ في التخطيط لخطوتي التالية.", points: 4 },
      { text: "أشعر بالإحباط لبعض الوقت، ثم أحاول النهوض والمتابعة.", points: 3 },
      { text: "أجد صعوبة في تجاوز الأمر وألوم نفسي بشدة.", points: 2 },
      { text: "أشعر باليأس وأفكر في الاستسلام.", points: 1 },
    ],
  },
  {
    text: "كيف تتعامل مع التغييرات المفاجئة وغير المتوقعة في حياتك؟",
    answers: [
      { text: "أتكيف بسرعة وأبحث عن الجوانب الإيجابية في الوضع الجديد.", points: 4 },
      { text: "أشعر بالتوتر في البداية، لكني أتكيف تدريجيًا.", points: 3 },
      { text: "أقاوم التغيير وأفضل التمسك بما هو مألوف.", points: 2 },
      { text: "أشعر بالانهيار وأجد صعوبة في التعامل مع المجهول.", points: 1 },
    ],
  },
  // Assertiveness
  {
    text: "يطلب منك زميل أو صديق القيام بمهمة لا ترغب فيها وتتعارض مع جدولك. كيف ترد؟",
    answers: [
      { text: "أرفض بأدب وحزم، مع شرح أن وقتي لا يسمح بذلك.", points: 4 },
      { text: "أتردد، ثم أوافق على مضض لتجنب إحراجه.", points: 3 },
      { text: "أبحث عن أعذار للتهرب من المهمة.", points: 2 },
      { text: "أوافق على الفور، حتى لو كان ذلك على حساب راحتي.", points: 1 },
    ],
  },
  {
    text: "في اجتماع، لديك فكرة تعتقد أنها ممتازة ولكنها تتعارض مع رأي الأغلبية. ماذا تفعل؟",
    answers: [
      { text: "أطرح فكرتي بثقة وأشرح منطقي لدعمها، حتى لو لم يتم قبولها.", points: 4 },
      { text: "أطرح الفكرة بتردد، وإذا واجهت مقاومة أتراجع بسرعة.", points: 3 },
      { text: "أحتفظ بالفكرة لنفسي لتجنب الدخول في جدال.", points: 2 },
      { text: "أوافق على رأي الأغلبية على الفور لتجنب الاختلاف.", points: 1 },
    ],
  },
  // Confidence
  {
    text: "كيف تتعامل مع النقد، حتى لو كان قاسيًا؟",
    answers: [
      { text: "أستمع إليه، وأفصل بين النقد البنّاء والشخصي، وأتعلم منه إن أمكن.", points: 4 },
      { text: "أشعر بالانزعاج، لكني أحاول ألا أظهر ذلك وأفكر في النقد لاحقًا.", points: 3 },
      { text: "أتخذ موقفًا دفاعيًا وأبدأ في تبرير أفعالي.", points: 2 },
      { text: "أشعر بالإهانة وأتأثر بشدة لفترة طويلة.", points: 1 },
    ],
  },
  {
    text: "أنت على وشك البدء في مشروع جديد وصعب. ما هو حديثك الداخلي؟",
    answers: [
      { text: "هذا تحدٍ مثير، وأنا واثق من أنني أستطيع التعامل معه وتعلم ما هو ضروري.", points: 4 },
      { text: "آمل أن أنجح، ولكني قلق بعض الشيء من الصعوبات.", points: 3 },
      { text: "أركز على كل ما يمكن أن يحدث بشكل خاطئ.", points: 2 },
      { text: "أشعر بأنني لست مؤهلاً لهذه المهمة وأفكر في الانسحاب.", points: 1 },
    ],
  },
  // Emotional Regulation
  {
    text: "عندما تشعر بالغضب أو الإحباط الشديد، كيف تدير مشاعرك؟",
    answers: [
      { text: "أعترف بالشعور، وآخذ بعض الوقت للتنفس والتفكير قبل التصرف.", points: 4 },
      { text: "أحاول كبت مشاعري وتجاهلها.", points: 3 },
      { text: "أعبر عن مشاعري بشكل مبالغ فيه، وقد أندم على ذلك لاحقًا.", points: 2 },
      { text: "تسيطر عليّ المشاعر تمامًا وأجد صعوبة في التفكير بوضوح.", points: 1 },
    ],
  },
  {
    text: "بعد تجربة سيئة، كم من الوقت تحتاج لتستعيد توازنك؟",
    answers: [
      { text: "أستطيع التعافي بسرعة نسبيًا والتركيز على الحاضر.", points: 4 },
      { text: "أحتاج إلى يوم أو يومين لمعالجة الأمر والمضي قدمًا.", points: 3 },
      { text: "تظل التجربة تؤثر عليّ لعدة أيام أو أسابيع.", points: 2 },
      { text: "أجد صعوبة بالغة في نسيان التجارب السيئة وتؤثر على مزاجي لفترة طويلة.", points: 1 },
    ],
  },
  // Independence
  {
    text: "إلى أي مدى تعتمد على آراء الآخرين عند اتخاذ قرارات شخصية مهمة؟",
    answers: [
      { text: "أستمع لآراء الآخرين كنصيحة، لكن القرار النهائي يعتمد على قناعتي الشخصية.", points: 4 },
      { text: "أتأثر كثيرًا بآراء المقربين مني.", points: 3 },
      { text: "أجد صعوبة في اتخاذ قرار دون الحصول على موافقة الآخرين.", points: 2 },
      { text: "عادةً ما أترك الآخرين يقررون عني.", points: 1 },
    ],
  },
  {
    text: "كيف تشعر بقضاء الوقت بمفردك؟",
    answers: [
      { text: "أستمتع به وأعتبره وقتًا ضروريًا لإعادة شحن طاقتي والتفكير.", points: 4 },
      { text: "لا بأس به، لكني أفضل أن أكون مع الآخرين.", points: 3 },
      { text: "أشعر بالملل أو الوحدة بسرعة.", points: 2 },
      { text: "أتجنب قضاء الوقت بمفردي قدر الإمكان.", points: 1 },
    ],
  },
];

const getResult = (score: number, gender: Gender) => {
    const results = {
      level1: {
        title: "شخصية صلبة كالألماس",
        text: "تتمتع بقوة شخصية استثنائية. أنت مرن، واثق من نفسك، وقادر على التعامل مع ضغوط الحياة بحكمة وثبات. أنت لا تخشى التحديات بل تراها فرصًا للنمو، وتتخذ قراراتك بناءً على قناعاتك الراسخة. هذه القوة الداخلية هي مصدر إلهام لمن حولك."
      },
      level2: {
        title: "شخصية قوية ومتنامية",
        text: "لديك أساس قوي لشخصية قوية. أنت قادر على مواجهة معظم التحديات وتتمتع بدرجة جيدة من الثقة بالنفس. قد تتردد أحيانًا في المواقف الصعبة أو تتأثر بالنقد، لكنك دائمًا تجد طريقك للنهوض مجددًا. الاستمرار في بناء مرونتك سيزيدك صلابة."
      },
      level3: {
        title: "شخصية حساسة وتحتاج للدعم",
        text: "أنت شخص حساس وتتأثر بشدة بالظروف الخارجية وآراء الآخرين. قد تجد صعوبة في قول 'لا' أو في التعامل مع الفشل. شخصيتك لديها إمكانات كبيرة، والتركيز على بناء الثقة بالنفس وتطوير آليات صحية للتعامل مع النقد سيساعدك على اكتشاف قوتك الكامنة."
      },
      level4: {
        title: "شخصية في مرحلة البحث عن القوة",
        text: "قد تشعر بأنك تتأثر بسهولة بالضغوط وتجد صعوبة في اتخاذ القرارات أو مواجهة المواقف الصعبة. هذه ليست نقطة ضعف، بل هي دعوة للبدء في رحلة بناء الذات. ابدأ بخطوات صغيرة لتعزيز ثقتك، مثل تحديد أهداف صغيرة وتحقيقها، وتعلم كيفية وضع حدود صحية مع الآخرين."
      }
    };
  
    if (score >= 32) {
      return { level: results.level1.title, description: results.level1.text };
    }
    if (score >= 24) {
      return { level: results.level2.title, description: results.level2.text };
    }
    if (score >= 16) {
      return { level: results.level3.title, description: results.level3.text };
    }
    return { level: results.level4.title, description: results.level4.text };
};

type TestStage = 'gender' | 'questions' | 'result';

function shuffleArray<T>(array: T[]): T[] {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

export function PersonalityStrengthTestTool() {
  const [stage, setStage] = useState<TestStage>('gender');
  const [gender, setGender] = useState<Gender | null>(null);
  const [shuffledQuestions, setShuffledQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  
  const isFinished = stage === 'result';
  const totalScore = answers.reduce((sum, score) => sum + score, 0);
  const result = isFinished && gender ? getResult(totalScore, gender) : { level: '', description: '' };

  const startTest = (selectedGender: Gender) => {
    setGender(selectedGender);
    setAnswers([]);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setShuffledQuestions(shuffleArray(questions));
    setStage('questions');
  };

  const handleNextQuestion = () => {
    if (selectedAnswer === null) return;
    
    const newAnswers = [...answers, selectedAnswer];
    setAnswers(newAnswers);
    setSelectedAnswer(null);

    if (currentQuestionIndex < shuffledQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      setStage('result');
    }
  };

  const handleRestart = () => {
    setStage('gender');
    setGender(null);
  };
  
  const progress = isFinished ? 100 : ((currentQuestionIndex) / shuffledQuestions.length) * 100;
  
  const currentQuestion = shuffledQuestions[currentQuestionIndex];
  
  const shuffledAnswers = useMemo(() => {
    if (!currentQuestion) return [];
    return shuffleArray(currentQuestion.answers);
  }, [currentQuestion]);


  const renderContent = () => {
    switch(stage) {
      case 'gender':
        return (
          <div className="text-center space-y-6">
            <h3 className="text-lg font-semibold">قبل أن نبدأ، يرجى تحديد جنسك</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Button variant="outline" className="h-24 text-xl" onClick={() => startTest('male')}>
                <User className="ml-3 h-8 w-8" />
                ذكر
              </Button>
              <Button variant="outline" className="h-24 text-xl" onClick={() => startTest('female')}>
                <UserRound className="ml-3 h-8 w-8" />
                أنثى
              </Button>
            </div>
          </div>
        );
      
      case 'questions':
        if (!gender || !currentQuestion) return null;
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-center">{getQuestionText(currentQuestion, gender)}</h3>
            <RadioGroup
              key={currentQuestionIndex}
              value={selectedAnswer?.toString() || ""}
              onValueChange={(value) => setSelectedAnswer(parseInt(value, 10))}
              className="space-y-3"
            >
              {shuffledAnswers.map((answer, index) => (
                <Label 
                  key={index} 
                  dir="rtl"
                  className="flex items-center justify-between gap-x-3 p-4 border rounded-lg cursor-pointer hover:bg-muted/50 has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary"
                >
                  <span className="flex-1">{getAnswerText(answer, gender)}</span>
                  <RadioGroupItem 
                    value={String(answer.points)} 
                    id={`q${currentQuestionIndex}-a${index}`} 
                  />
                </Label>
              ))}
            </RadioGroup>
            <Button onClick={handleNextQuestion} disabled={selectedAnswer === null} className="w-full">
              {currentQuestionIndex === questions.length - 1 ? 'عرض النتيجة' : 'التالي'}
            </Button>
          </div>
        );
      case 'result':
        return (
          <div className="text-center space-y-4">
            <h3 className="text-2xl font-bold text-primary">{result.level}</h3>
            <p className="text-muted-foreground leading-relaxed">{result.description}</p>
            <p className="text-xs text-muted-foreground pt-4 border-t">إخلاء مسؤولية: هذا الاختبار هو لأغراض الترفيه والتأمل الذاتي، ولا يمثل تشخيصًا نفسيًا.</p>
            <Button onClick={handleRestart} variant="outline" className="w-full">
                <Repeat className="ml-2 h-4 w-4" />
                أعد الاختبار
            </Button>
          </div>
        );
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
            <Gem className="h-6 w-6 text-primary" />
            اختبار قوة الشخصية
        </CardTitle>
        <CardDescription>
            أجب عن الأسئلة التالية بصدق لاكتشاف مدى قوة ومرونة شخصيتك.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {stage !== 'gender' && (
             <div className="mb-6">
                <Progress value={progress} />
                <p className="text-sm text-muted-foreground mt-2 text-center">
                    {isFinished ? 'اكتمل الاختبار!' : `السؤال ${currentQuestionIndex + 1} من ${shuffledQuestions.length}`}
                </p>
            </div>
        )}
       
        {renderContent()}

      </CardContent>
    </Card>
  );
}
