'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { paraphraseArabicText } from '@/ai/flows/paraphrase-arabic-text';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>ader2, <PERSON>rkles, Wand2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const FormSchema = z.object({
  text: z.string().min(10, {
    message: 'الرجاء إدخال نص لا يقل عن 10 أحرف.',
  }),
});

export function ParaphraseTextTool() {
  const [paraphrasedText, setParaphrasedText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      text: '',
    },
  });

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    setIsLoading(true);
    setParaphrasedText('');
    try {
      const result = await paraphraseArabicText({ text: data.text });
      setParaphrasedText(result.paraphrasedText);
    } catch (e) {
      console.error(e);
      toast({
        variant: 'destructive',
        title: 'خطأ في إعادة الصياغة',
        description: 'حدث خطأ أثناء محاولة إعادة صياغة النص. الرجاء المحاولة مرة أخرى.',
      });
    }
    setIsLoading(false);
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <Card>
        <CardHeader>
          <CardTitle>النص الأصلي</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="text"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الصق النص المراد إعادة صياغته هنا</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="ابدأ بكتابة أو لصق النص هنا..."
                        className="resize-y min-h-[300px] lg:min-h-[400px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" disabled={isLoading} className="w-full">
                {isLoading ? (
                  <>
                    <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                    جاري المعالجة...
                  </>
                ) : (
                  <>
                    <Wand2 className="ml-2 h-4 w-4" />
                    أعد صياغة النص
                  </>
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
      <Card className="bg-primary/5">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="text-primary" />
            النص المُعاد صياغته
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="min-h-[300px] lg:min-h-[400px] p-4 rounded-md bg-background relative">
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm rounded-md">
                <div className="flex flex-col items-center gap-2 text-muted-foreground">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span>تتم الآن إعادة صياغة النص...</span>
                </div>
              </div>
            )}
            {paraphrasedText ? (
              <p className="text-foreground whitespace-pre-wrap leading-relaxed">{paraphrasedText}</p>
            ) : (
              <div className="flex items-center justify-center h-full">
                <p className="text-muted-foreground text-center">
                  سيظهر النص المُعاد صياغته بواسطة الذكاء الاصطناعي هنا.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
