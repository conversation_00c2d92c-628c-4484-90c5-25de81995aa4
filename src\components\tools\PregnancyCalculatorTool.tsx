'use client';

import { useState } from 'react';
import { useForm, Control } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Baby } from 'lucide-react';
import { format, addDays, differenceInDays } from 'date-fns';
import { arSA } from 'date-fns/locale';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  day: requiredNumber('اليوم مطلوب'),
  month: requiredNumber('الشهر مطلوب'),
  year: requiredNumber('السنة مطلوبة'),
}).refine(data => {
    if (!data.year || !data.month || !data.day) return true;
    try {
        const date = new Date(data.year, data.month - 1, data.day);
        return date.getFullYear() === data.year && date.getMonth() === data.month - 1 && date.getDate() === data.day;
    } catch {
        return false;
    }
}, {
    message: "التاريخ المدخل غير صالح.",
    path: ['day'],
});

type FormValues = z.infer<typeof FormSchema>;

interface Result {
  dueDate: Date;
  weeks: number;
  daysOfWeek: number;
  months: number;
  daysOfMonth: number;
  remainingTotalDays: number;
  remainingWeeks: number;
  remainingDaysInWeek: number;
  remainingMonths: number;
  remainingDaysInMonth: number;
}


const DateFields = ({ control }: { control: Control<FormValues> }) => {
    const months = Array.from({ length: 12 }, (_, i) => i + 1);
    const days = Array.from({ length: 31 }, (_, i) => i + 1);
    const currentYear = new Date().getFullYear();
    const years = [currentYear, currentYear - 1];

    return (
    <div className="grid grid-cols-3 gap-2">
        <FormField
            control={control}
            name="day"
            render={({ field }) => (
                <FormItem>
                    <Select onValueChange={field.onChange} value={field.value?.toString()}>
                        <FormControl><SelectTrigger><SelectValue placeholder="اليوم" /></SelectTrigger></FormControl>
                        <SelectContent>{days.map(d => <SelectItem key={d} value={String(d)}>{d}</SelectItem>)}</SelectContent>
                    </Select>
                    <FormMessage />
                </FormItem>
            )}
        />
        <FormField
            control={control}
            name="month"
            render={({ field }) => (
                <FormItem>
                    <Select onValueChange={field.onChange} value={field.value?.toString()}>
                        <FormControl><SelectTrigger><SelectValue placeholder="الشهر" /></SelectTrigger></FormControl>
                        <SelectContent>{months.map(m => <SelectItem key={m} value={String(m)}>{m}</SelectItem>)}</SelectContent>
                    </Select>
                </FormItem>
            )}
        />
         <FormField
            control={control}
            name="year"
            render={({ field }) => (
                <FormItem>
                    <Select onValueChange={field.onChange} value={field.value?.toString()}>
                        <FormControl><SelectTrigger><SelectValue placeholder="السنة" /></SelectTrigger></FormControl>
                        <SelectContent>{years.map(y => <SelectItem key={y} value={String(y)}>{y}</SelectItem>)}</SelectContent>
                    </Select>
                </FormItem>
            )}
        />
    </div>
)};


export function PregnancyCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
        day: undefined,
        month: undefined,
        year: undefined,
    }
  });

  function onSubmit(data: FormValues) {
    const now = new Date();
    const lmp = new Date(data.year, data.month - 1, data.day);

    if (isNaN(lmp.getTime()) || lmp > now) {
        form.setError("day", { message: "تاريخ غير صالح أو في المستقبل." });
        return;
    }

    const dueDate = addDays(lmp, 280);
    const remainingTotalDays = differenceInDays(dueDate, new Date());
    
    // Calculate precise gestation age
    const daysSinceLmp = differenceInDays(now, lmp);
    const weeks = Math.floor(daysSinceLmp / 7);
    const daysOfWeek = daysSinceLmp % 7;
    
    // Approximate months and days for gestation age
    const avgDaysInMonth = 30.4375;
    const months = Math.floor(daysSinceLmp / avgDaysInMonth);
    const daysOfMonth = Math.round(daysSinceLmp % avgDaysInMonth);

    // Calculate remaining time in different formats
    const remainingWeeks = Math.floor(remainingTotalDays / 7);
    const remainingDaysInWeek = remainingTotalDays % 7;
    const remainingMonths = Math.floor(remainingTotalDays / avgDaysInMonth);
    const remainingDaysInMonth = Math.round(remainingTotalDays % avgDaysInMonth);

    setResult({ 
        dueDate, 
        weeks, 
        daysOfWeek, 
        months, 
        daysOfMonth, 
        remainingTotalDays,
        remainingWeeks,
        remainingDaysInWeek,
        remainingMonths,
        remainingDaysInMonth,
    });
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حاسبة الحمل والولادة</CardTitle>
        <CardDescription>أدخلي تاريخ أول يوم من آخر دورة شهرية لتوقّع موعد ولادتك.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormLabel>أول يوم من آخر دورة شهرية (LMP)</FormLabel>
            <DateFields control={form.control} />
            <FormMessage>{form.formState.errors.day?.message}</FormMessage>
            <Button type="submit" className="w-full">احسبي موعد الولادة</Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-4">النتائج التقديرية</h3>
             <div className="p-6 bg-primary/10 rounded-lg mb-4">
                <Baby className="w-12 h-12 text-primary mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">موعد الولادة المتوقع</p>
                <p className="text-3xl font-bold font-mono text-primary">
                  {format(result.dueDate, 'EEEE, d MMMM yyyy', { locale: arSA })}
                </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-secondary rounded-lg">
                <p className="text-sm text-muted-foreground">عمر الحمل بالأسابيع</p>
                <p className="text-2xl font-bold font-mono">
                    {result.weeks} أسابيع و {result.daysOfWeek} أيام
                </p>
              </div>
               <div className="p-4 bg-secondary rounded-lg">
                <p className="text-sm text-muted-foreground">عمر الحمل بالأشهر</p>
                <p className="text-2xl font-bold font-mono">
                    {result.months} أشهر و {result.daysOfMonth} أيام
                </p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div className="p-4 bg-muted rounded-lg">
                    <p className="text-sm text-muted-foreground">متبقٍ على الولادة (بالأسابيع)</p>
                    <p className="text-2xl font-bold font-mono">
                        {result.remainingWeeks > 0 ? `${result.remainingWeeks} أسابيع و ${result.remainingDaysInWeek} أيام` : 'أقل من أسبوع'}
                    </p>
                </div>
                <div className="p-4 bg-muted rounded-lg">
                    <p className="text-sm text-muted-foreground">متبقٍ على الولادة (بالأشهر)</p>
                    <p className="text-2xl font-bold font-mono">
                        {result.remainingMonths > 0 ? `${result.remainingMonths} أشهر و ${result.remainingDaysInMonth} أيام` : 'أقل من شهر'}
                    </p>
                </div>
            </div>

            <p className="text-xs text-muted-foreground mt-4">
                هذه مجرد تقديرات. موعد الولادة الفعلي قد يختلف. استشيري طبيبكِ دائمًا.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
