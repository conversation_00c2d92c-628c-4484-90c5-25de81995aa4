
'use client';

import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { PlusCircle, Trash2, Calculator } from 'lucide-react';
import { useState } from 'react';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const subjectSchema = z.object({
  name: z.string().min(1, 'اسم المادة مطلوب.'),
  grade: requiredNumber().min(0, "الدرجة لا يمكن أن تكون سالبة.").max(100, "الدرجة لا يمكن أن تزيد عن 100."),
});

const formSchema = z.object({
  subjects: z.array(subjectSchema).min(1, 'يجب إضافة مادة واحدة على الأقل.'),
});

type FormValues = z.infer<typeof formSchema>;

const initialSubjects = [
  { name: 'التربية الإسلامية' },
  { name: 'اللغة العربية' },
  { name: 'اللغة الإنجليزية' },
  { name: 'تاريخ الأردن' },
  { name: 'الرياضيات' },
  { name: 'الفيزياء' },
  { name: 'الكيمياء' },
];

export function JordanianTawjihiCalculatorTool() {
  const [result, setResult] = useState<{ average: number } | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      subjects: initialSubjects.map(s => ({...s, grade: undefined})),
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "subjects",
  });

  const onSubmit = (data: FormValues) => {
    const totalGrades = data.subjects.reduce((sum, subject) => sum + subject.grade, 0);
    const average = totalGrades / data.subjects.length;
    setResult({ average });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حاسبة معدل التوجيهي الأردني</CardTitle>
        <CardDescription>أدخل درجاتك في المواد لحساب معدلك النهائي في التوجيهي (من 100).</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4">
              <div className="grid grid-cols-[1fr,120px,auto] gap-2 items-center text-sm font-medium text-muted-foreground border-b pb-2">
                <span>اسم المادة</span>
                <span className="text-center">الدرجة (من 100)</span>
                <span className="text-center">حذف</span>
              </div>
              {fields.map((field, index) => (
                <div key={field.id} className="grid grid-cols-[1fr,120px,auto] gap-2 items-start">
                  <FormField
                    name={`subjects.${index}.name`}
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input placeholder={`مادة ${index + 1}`} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    name={`subjects.${index}.grade`}
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input type="number" min="0" max="100" placeholder="مثال 85" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="text-center">
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => remove(index)}
                      className="text-destructive hover:text-destructive"
                      disabled={fields.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            <Button
              type="button"
              variant="outline"
              onClick={() => append({ name: '', grade: undefined })}
              className="w-full border-dashed"
            >
              <PlusCircle className="ml-2 h-4 w-4" />
              إضافة مادة
            </Button>

            <div className="flex flex-col items-center space-y-4 pt-6 border-t">
              <Button type="submit" size="lg" className="w-full sm:w-auto px-12">
                <Calculator className="ml-2 h-4 w-4" />
                احسب المعدل
              </Button>
              
              {result && (
                <div className="text-center p-6 bg-primary/10 rounded-lg border-2 border-primary/20 w-full">
                  <p className="text-sm text-primary/80 mb-2">معدل التوجيهي النهائي</p>
                  <p className="text-5xl font-bold text-primary font-mono">
                    {result.average.toFixed(2)}%
                  </p>
                </div>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
