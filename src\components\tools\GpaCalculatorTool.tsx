
'use client';

import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { PlusCircle, Trash2, Calculator, BookOpen, Clock, Trophy } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useState } from 'react';
import { cn } from '@/lib/utils';

// جداول التحويل للتقديرات
const GRADE_SCALES = {
  '4': { 'A+': 4.0, 'A': 4.0, 'A-': 3.7, 'B+': 3.3, 'B': 3.0, 'B-': 2.7, 'C+': 2.3, 'C': 2.0, 'C-': 1.7, 'D+': 1.3, 'D': 1.0, 'F': 0.0 },
  '5': { 'A+': 5.0, 'A': 4.75, 'A-': 4.5, 'B+': 4.0, 'B': 3.75, 'B-': 3.5, 'C+': 3.0, 'C': 2.75, 'C-': 2.5, 'D+': 2.0, 'D': 1.75, 'F': 0.0 }
};

// تحويل من 100 إلى نظام نقاط
const convertFrom100toScale = (percentage: number, scale: '4' | '5'): number => {
  const scaleMax = parseFloat(scale);
  if (percentage >= 95) return scale === '5' ? 5.0 : 4.0;
  if (percentage >= 90) return scale === '5' ? 4.75 : 4.0;
  if (percentage >= 85) return scale === '5' ? 4.5 : 3.7;
  if (percentage >= 80) return scale === '5' ? 4.0 : 3.3;
  if (percentage >= 75) return scale === '5' ? 3.5 : 3.0;
  if (percentage >= 70) return scale === '5' ? 3.0 : 2.7;
  if (percentage >= 65) return scale === '5' ? 2.5 : 2.3;
  if (percentage >= 60) return scale === '5' ? 2.0 : 2.0;
  if (percentage >= 55) return scale === '5' ? 1.5 : 1.7; // Note: Custom logic
  if (percentage >= 50) return scale === '5' ? 1.0 : 1.0;
  return 0.0;
};

const requiredNumber = (message = "الرجاء إدخال رقم صحيح.") => 
  z.coerce.number({ invalid_type_error: message });

const courseSchema = z.object({
  courseName: z.string().optional(),
  credits: requiredNumber("الساعات يجب أن تكون موجبة.").positive("الساعات يجب أن تكون موجبة."),
  grade: z.string().min(1, "يجب اختيار التقدير."),
  grade_100: requiredNumber().min(0, "الدرجة لا يجب ان تكون سالبة").max(100, "الدرجة لا يجب أن تتجاوز 100").optional(),
});

const formSchema = z.object({
  gpaSystem: z.enum(['4', '5']),
  gradeInputSystem: z.enum(['points', 'percent']).default('points'),
  previousGpa: requiredNumber().min(0).optional(),
  previousCredits: requiredNumber().min(0).optional(),
  courses: z.array(courseSchema),
});

type FormValues = z.infer<typeof formSchema>;

export function GpaCalculatorTool() {
  const [result, setResult] = useState<{ 
    semesterGpa: number; 
    cumulativeGpa: number; 
    totalCredits: number; 
    semesterCredits: number;
    totalPoints: number;
  } | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      gpaSystem: '4',
      gradeInputSystem: 'points',
      previousGpa: 0,
      previousCredits: 0,
      courses: [{ courseName: '', grade: '', credits: 3 }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "courses",
  });

  const watchGpaSystem = form.watch("gpaSystem");
  const watchGradeInputSystem = form.watch("gradeInputSystem");

  const getGradeValue = (course: z.infer<typeof courseSchema>, gpaSystem: '4' | '5', gradeInputSystem: 'points' | 'percent') => {
    if (gradeInputSystem === 'percent') {
        const grade100 = course.grade_100;
        if (typeof grade100 !== 'number') return 0;
        return convertFrom100toScale(grade100, gpaSystem);
    }
    return GRADE_SCALES[gpaSystem][course.grade as keyof typeof GRADE_SCALES['4']] || 0;
  };

  const onSubmit = (data: FormValues) => {
    const { gpaSystem, courses, previousGpa = 0, previousCredits = 0, gradeInputSystem } = data;
    
    let semesterPoints = 0;
    let semesterCredits = 0;

    courses.forEach(course => {
      const gradeValue = getGradeValue(course, gpaSystem, gradeInputSystem);
      semesterPoints += gradeValue * course.credits;
      semesterCredits += course.credits;
    });

    const semesterGpa = semesterCredits > 0 ? semesterPoints / semesterCredits : 0;

    const previousPoints = previousGpa * previousCredits;
    const totalPoints = previousPoints + semesterPoints;
    const totalCredits = previousCredits + semesterCredits;
    const cumulativeGpa = totalCredits > 0 ? totalPoints / totalCredits : 0;

    setResult({ 
      semesterGpa, 
      cumulativeGpa, 
      totalCredits, 
      semesterCredits,
      totalPoints 
    });
  };

  const getGradeOptions = (gpaSystem: string) => {
    return Object.keys(GRADE_SCALES[gpaSystem as keyof typeof GRADE_SCALES]);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حساب المعدل التراكمي (GPA)</CardTitle>
        <CardDescription>أدخل درجاتك والساعات المعتمدة لكل مادة لحساب معدلك.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
               <FormField
                  control={form.control}
                  name="gpaSystem"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>نظام المعدل النهائي</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl><SelectTrigger><SelectValue /></SelectTrigger></FormControl>
                        <SelectContent>
                          <SelectItem value="4">من 4</SelectItem>
                          <SelectItem value="5">من 5</SelectItem>
                        </SelectContent>
                      </Select>
                       <FormMessage />
                    </FormItem>
                  )}
                />
                 <FormField
                  control={form.control}
                  name="gradeInputSystem"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>نظام إدخال الدرجات</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl><SelectTrigger><SelectValue /></SelectTrigger></FormControl>
                        <SelectContent>
                          <SelectItem value="points">حسب التقدير (A+, B, ...)</SelectItem>
                          <SelectItem value="percent">حسب النسبة المئوية (من 100)</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
            </div>

            <Card className="bg-muted/30">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">المعدل التراكمي السابق (اختياري)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="previousGpa"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>المعدل التراكمي السابق</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01" 
                            min="0"
                            placeholder="0"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="previousCredits"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الساعات المكتملة</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0"
                            placeholder="0"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            <div className="space-y-6">
              <h3 className="text-lg font-semibold">المواد الدراسية:</h3>

              {/* Desktop Layout */}
              <div className="hidden md:block">
                <div className={cn("grid gap-2 text-sm font-medium text-muted-foreground border-b pb-2", watchGradeInputSystem === 'points' ? 'grid-cols-4' : 'grid-cols-[1fr,100px,120px,auto]')}>
                  <div>اسم المادة</div>
                  <div>الساعات</div>
                  <div>{watchGradeInputSystem === 'points' ? 'التقدير' : 'الدرجة'}</div>
                  <div className="text-center">حذف</div>
                </div>

                {fields.map((field, index) => (
                  <div key={field.id} className={cn("grid gap-2 items-start mt-2", watchGradeInputSystem === 'points' ? 'grid-cols-4' : 'grid-cols-[1fr,100px,120px,auto]')}>
                    <FormField
                      name={`courses.${index}.courseName`}
                      control={form.control}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input placeholder={`مادة ${index + 1}`} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      name={`courses.${index}.credits`}
                      control={form.control}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input type="number" min="1" placeholder="3" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {watchGradeInputSystem === 'points' ? (
                       <FormField
                          name={`courses.${index}.grade`}
                          control={form.control}
                          render={({ field }) => (
                            <FormItem>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="اختر التقدير" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {getGradeOptions(watchGpaSystem).map(grade => (
                                    <SelectItem key={grade} value={grade}>
                                      {grade} ({GRADE_SCALES[watchGpaSystem as keyof typeof GRADE_SCALES][grade as keyof typeof GRADE_SCALES['4']]})
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                    ) : (
                      <FormField
                        name={`courses.${index}.grade_100`}
                        control={form.control}
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                max="100"
                                placeholder="95"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    <div className="text-center">
                      <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => remove(index)}
                          className="text-destructive hover:text-destructive"
                          disabled={fields.length === 1}
                      >
                          <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Mobile Layout */}
              <div className="md:hidden space-y-4">
                {fields.map((field, index) => (
                  <Card key={field.id} className="p-3 border-l-4 border-l-primary/20">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="font-medium text-base text-primary">مادة {index + 1}</h4>
                      <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => remove(index)}
                          className="text-destructive hover:text-destructive h-8 w-8 p-0"
                          disabled={fields.length === 1}
                      >
                          <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="space-y-4">
                      <FormField
                        name={`courses.${index}.courseName`}
                        control={form.control}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium">اسم المادة</FormLabel>
                            <FormControl>
                              <Input
                                placeholder={`مادة ${index + 1}`}
                                {...field}
                                className="h-11 text-base"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          name={`courses.${index}.credits`}
                          control={form.control}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-medium">الساعات</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="1"
                                  placeholder="3"
                                  {...field}
                                  className="h-11 text-base text-center"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {watchGradeInputSystem === 'points' ? (
                           <FormField
                              name={`courses.${index}.grade`}
                              control={form.control}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-sm font-medium">التقدير</FormLabel>
                                  <Select onValueChange={field.onChange} value={field.value}>
                                    <FormControl>
                                      <SelectTrigger className="h-11 text-base">
                                        <SelectValue placeholder="اختر" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      {getGradeOptions(watchGpaSystem).map(grade => (
                                        <SelectItem key={grade} value={grade} className="text-base">
                                          {grade} ({GRADE_SCALES[watchGpaSystem as keyof typeof GRADE_SCALES][grade as keyof typeof GRADE_SCALES['4']]})
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                        ) : (
                          <FormField
                            name={`courses.${index}.grade_100`}
                            control={form.control}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm font-medium">الدرجة</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="0"
                                    max="100"
                                    placeholder="95"
                                    {...field}
                                    className="h-11 text-base text-center"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            <Button
              type="button"
              variant="outline"
              onClick={() => append({ courseName: '', grade: '', credits: 3 })}
              className="w-full h-12 text-base font-medium border-dashed border-2 hover:border-primary/50 hover:bg-primary/5"
            >
              <PlusCircle className="ml-2 h-5 w-5" /> إضافة مادة جديدة
            </Button>

            <div className="flex flex-col space-y-4 pt-6 border-t">
              <Button type="submit" size="lg" className="w-full h-12 text-lg font-semibold">
                احسب المعدل التراكمي
              </Button>
              
              {result && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-center">النتائج</h3>

                  {/* Mobile Layout for Results */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="bg-primary/10 p-5 rounded-lg text-center border border-primary/20">
                      <div className="flex justify-center mb-2">
                        <Calculator className="h-6 w-6 text-primary" />
                      </div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">معدل الفصل</p>
                      <p className="text-3xl font-bold text-primary">{result.semesterGpa.toFixed(2)}</p>
                    </div>
                    <div className="bg-green-50 p-5 rounded-lg text-center border border-green-200">
                      <div className="flex justify-center mb-2">
                        <Trophy className="h-6 w-6 text-green-700" />
                      </div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">المعدل التراكمي</p>
                      <p className="text-3xl font-bold text-green-700">{result.cumulativeGpa.toFixed(2)}</p>
                    </div>
                    <div className="bg-muted p-5 rounded-lg text-center border">
                      <div className="flex justify-center mb-2">
                        <Clock className="h-6 w-6 text-muted-foreground" />
                      </div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">ساعات الفصل</p>
                      <p className="text-2xl font-semibold">{result.semesterCredits}</p>
                    </div>
                    <div className="bg-muted p-5 rounded-lg text-center border">
                      <div className="flex justify-center mb-2">
                        <BookOpen className="h-6 w-6 text-muted-foreground" />
                      </div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">إجمالي الساعات</p>
                      <p className="text-2xl font-semibold">{result.totalCredits}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

