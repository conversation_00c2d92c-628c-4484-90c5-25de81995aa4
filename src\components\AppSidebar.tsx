
'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Sidebar,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarFooter,
} from '@/components/ui/sidebar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { toolCategories } from '@/lib/tools';
import { Button } from '@/components/ui/button';
import { ChevronDown, Home } from 'lucide-react';
import React, { useState, useEffect } from 'react';

export function AppSidebar() {
  const pathname = usePathname();
  const [openStates, setOpenStates] = useState<Record<string, boolean>>({});

  useEffect(() => {
    const activeCategory = toolCategories.find(category => 
      category.tools.some(tool => pathname === tool.path)
    );
    if (activeCategory) {
      setOpenStates(prev => ({ ...prev, [activeCategory.slug]: true }));
    }
  }, [pathname]);

  const toggleOpen = (slug: string) => {
    setOpenStates(prev => ({ ...prev, [slug]: !prev[slug] }));
  };

  const sortedCategories = React.useMemo(() => {
    const activeCategoryIndex = toolCategories.findIndex(category => 
      category.tools.some(tool => pathname === tool.path)
    );

    if (activeCategoryIndex > -1) {
      const activeCategory = toolCategories[activeCategoryIndex];
      const otherCategories = toolCategories.filter((_, index) => index !== activeCategoryIndex);
      return [activeCategory, ...otherCategories];
    }
    
    return toolCategories;
  }, [pathname]);

  return (
    <Sidebar side="right">
      <SidebarContent className="p-0 pt-4">
        <SidebarMenu>
          {sortedCategories.map((category) => {
            const isActiveCategory = category.tools.some(tool => pathname === tool.path);
            const isOpen = openStates[category.slug] || isActiveCategory;

            return (
              <SidebarMenuItem key={category.name}>
                <Collapsible open={isOpen} onOpenChange={() => toggleOpen(category.slug)}>
                  <CollapsibleTrigger asChild>
                    <Button
                      variant="ghost"
                      className="w-full justify-between h-auto p-2 text-sm font-headline text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground group"
                    >
                      <div className="flex items-center gap-3">
                        <category.icon className="h-5 w-5 text-sidebar-primary" />
                        {category.name}
                      </div>
                      <ChevronDown className="h-4 w-4 transition-transform duration-200 group-data-[state=open]:rotate-180" />
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="px-3 pb-2">
                    <SidebarMenu>
                      {category.tools.filter(t => t.component).map((tool) => {
                        const ToolIcon = tool.icon;
                        return (
                          <SidebarMenuItem key={tool.path}>
                            <SidebarMenuButton
                              asChild
                              className="w-full justify-start gap-2"
                              variant="default"
                              size="sm"
                              isActive={pathname === tool.path}
                            >
                              <Link href={tool.path}>
                                {ToolIcon && <ToolIcon className="text-sidebar-primary/80" />}
                                {tool.name}
                              </Link>
                            </SidebarMenuButton>
                          </SidebarMenuItem>
                        );
                      })}
                    </SidebarMenu>
                  </CollapsibleContent>
                </Collapsible>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter>
        <Link href="/">
          <Button variant="outline" className="w-full bg-transparent border-sidebar-border text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground">
            <Home className="ml-2 h-4 w-4" />
            العودة للصفحة الرئيسية
          </Button>
        </Link>
      </SidebarFooter>
    </Sidebar>
  );
}
