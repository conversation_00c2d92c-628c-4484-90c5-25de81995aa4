
'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowRightLeft } from 'lucide-react';

const units = {
  length: {
    label: 'الطول',
    options: {
      m: { name: 'm', factor: 1 },
      km: { name: 'km', factor: 1000 },
      cm: { name: 'cm', factor: 0.01 },
      mm: { name: 'mm', factor: 0.001 },
      in: { name: 'in', factor: 0.0254 },
      ft: { name: 'ft', factor: 0.3048 },
      yd: { name: 'yd', factor: 0.9144 },
      mi: { name: 'mi', factor: 1609.34 },
    },
  },
  weight: {
    label: 'الوزن',
    options: {
      kg: { name: 'kg', factor: 1 },
      g: { name: 'g', factor: 0.001 },
      mg: { name: 'mg', factor: 0.000001 },
      t: { name: 't', factor: 1000 },
      lb: { name: 'lb', factor: 0.453592 },
      oz: { name: 'oz', factor: 0.0283495 },
    },
  },
  temperature: {
    label: 'الحرارة',
    options: {
      c: { name: '°C', toBase: (val: number) => val, fromBase: (val: number) => val },
      f: { name: '°F', toBase: (val: number) => (val - 32) * 5 / 9, fromBase: (val: number) => (val * 9 / 5) + 32 },
      k: { name: 'K', toBase: (val: number) => val - 273.15, fromBase: (val: number) => val + 273.15 },
    },
  },
};

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  category: z.string(),
  fromUnit: z.string(),
  toUnit: z.string(),
  fromValue: requiredNumber().default(1),
});

type FormValues = z.infer<typeof FormSchema>;

export function UnitConverterTool() {
  const [result, setResult] = useState<number>(0);
  const [activeTab, setActiveTab] = useState('length');

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      category: 'length',
      fromUnit: 'm',
      toUnit: 'ft',
      fromValue: 1,
    },
  });

  const { watch, setValue, getValues } = form;
  const watchedFields = watch();

  useEffect(() => {
    const { category, fromValue, fromUnit, toUnit } = getValues();
    if (!category || !fromUnit || !toUnit || typeof fromValue !== 'number' || isNaN(fromValue)) return;
    
    // @ts-ignore
    const categoryUnits = units[category];
    let toValue;

    if (category === 'temperature') {
      const fromFunc = categoryUnits.options[fromUnit].toBase;
      const toFunc = categoryUnits.options[toUnit].fromBase;
      toValue = toFunc(fromFunc(fromValue));
    } else {
      const fromFactor = categoryUnits.options[fromUnit].factor;
      const toFactor = categoryUnits.options[toUnit].factor;
      const baseValue = fromValue * fromFactor;
      toValue = baseValue / toFactor;
    }
    
    setResult(toValue);
  }, [watchedFields, getValues]);
  
  const handleSwap = () => {
    const { fromUnit, toUnit } = getValues();
    setValue('fromUnit', toUnit);
    setValue('toUnit', fromUnit);
  };
  
  const onTabChange = (newTab: string) => {
    setActiveTab(newTab);
    setValue('category', newTab);
    // @ts-ignore
    const defaultUnits = Object.keys(units[newTab].options);
    setValue('fromUnit', defaultUnits[0]);
    setValue('toUnit', defaultUnits[1] || defaultUnits[0]);
    setValue('fromValue', 1);
  };

  // @ts-ignore
  const currentUnitOptions = units[activeTab].options;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>محول الوحدات</CardTitle>
        <CardDescription>تحويل بين وحدات الطول والوزن والحرارة وغيرها.</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="length">الطول</TabsTrigger>
            <TabsTrigger value="weight">الوزن</TabsTrigger>
            <TabsTrigger value="temperature">الحرارة</TabsTrigger>
          </TabsList>
          
            <div className="pt-6">
                <Form {...form}>
                  <form className="space-y-4">
                     <div className="grid grid-cols-1 md:grid-cols-5 gap-2 items-end">
                        <FormField name="fromValue" control={form.control} render={({ field }) => (
                            <FormItem className="md:col-span-2"><FormLabel>القيمة</FormLabel><FormControl><Input type="number" {...field} /></FormControl></FormItem>
                        )}/>
                        <FormField name="fromUnit" control={form.control} render={({ field }) => (
                            <FormItem><FormLabel>من</FormLabel><Select onValueChange={field.onChange} value={field.value}><FormControl><SelectTrigger><SelectValue /></SelectTrigger></FormControl><SelectContent>{Object.entries(currentUnitOptions).map(([key, u]) => (<SelectItem key={key} value={key}>{(u as any).name}</SelectItem>))}</SelectContent></Select></FormItem>
                        )}/>
                        <Button variant="ghost" size="icon" className="self-end mb-1" onClick={handleSwap} type="button"><ArrowRightLeft className="w-5 h-5 text-muted-foreground" /></Button>
                        <FormField name="toUnit" control={form.control} render={({ field }) => (
                            <FormItem><FormLabel>إلى</FormLabel><Select onValueChange={field.onChange} value={field.value}><FormControl><SelectTrigger><SelectValue /></SelectTrigger></FormControl><SelectContent>{Object.entries(currentUnitOptions).map(([key, u]) => (<SelectItem key={key} value={key}>{(u as any).name}</SelectItem>))}</SelectContent></Select></FormItem>
                        )}/>
                    </div>
                    <div className="pt-4 text-center">
                      <p className="text-sm text-muted-foreground">النتيجة</p>
                      <p className="text-3xl lg:text-4xl font-bold font-mono text-primary">{result.toLocaleString(undefined, { maximumFractionDigits: 5 })}</p>
                    </div>
                  </form>
                </Form>
            </div>
        </Tabs>
      </CardContent>
    </Card>
  );
}
