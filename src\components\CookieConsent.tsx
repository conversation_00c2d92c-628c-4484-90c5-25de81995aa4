
'use client';

import { useState } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { X, Settings, <PERSON>ie } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useCookieConsent, CookiePreferences } from '@/hooks/use-cookie-consent';

export function CookieConsent() {
  const {
    showBanner,
    preferences,
    isSettingsOpen,
    setShowBanner,
    setSettingsOpen,
    updatePreference,
    savePreferences,
    acceptAll,
    acceptNecessary,
  } = useCookieConsent();

  if (!showBanner) {
    return null;
  }

  const handleSave = () => {
    savePreferences(preferences);
  };

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-background/95 backdrop-blur border-t shadow-lg">
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col lg:flex-row items-start lg:items-center gap-4">
              <div className="flex items-start gap-3 flex-1">
                <Cookie className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                <div className="flex-1">
                  <h3 className="font-bold text-sm mb-2">نحن نستخدم ملفات تعريف الارتباط (الكوكيز)</h3>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    نستخدم الكوكيز لتحسين تجربتك وتقديم محتوى مخصص وتحليل حركة المرور.
                    بعض الكوكيز ضرورية لعمل الموقع، بينما أخرى اختيارية.{' '}
                    <Link href="/p/cookies" className="text-primary hover:underline">
                      اعرف المزيد
                    </Link>
                  </p>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-2 w-full lg:w-auto">
                <Button variant="outline" size="sm" className="w-full sm:w-auto" onClick={() => setSettingsOpen(true)}>
                  <Settings className="h-4 w-4 ml-2" />
                  إعدادات الكوكيز
                </Button>
                
                <Button variant="outline" size="sm" onClick={acceptNecessary} className="w-full sm:w-auto">
                  الضرورية فقط
                </Button>
                
                <Button size="sm" onClick={acceptAll} className="w-full sm:w-auto">
                  قبول الكل
                </Button>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowBanner(false)}
                className="absolute top-2 left-2 lg:relative lg:top-0 lg:left-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Dialog open={isSettingsOpen} onOpenChange={setSettingsOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>إعدادات ملفات تعريف الارتباط</DialogTitle>
            <DialogDescription>
              اختر أنواع الكوكيز التي تريد السماح بها
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <CookieSettingRow
              label="الكوكيز الضرورية"
              description="مطلوبة لعمل الموقع بشكل صحيح"
              checked={true}
              disabled={true}
            />
            <CookieSettingRow
              label="كوكيز التحليل"
              description="تساعدنا في فهم كيفية استخدام الموقع"
              checked={preferences.analytics}
              onCheckedChange={(checked) => updatePreference('analytics', checked)}
            />
            <CookieSettingRow
              label="كوكيز الإعلانات"
              description="لعرض إعلانات مناسبة لاهتماماتك"
              checked={preferences.advertising}
              onCheckedChange={(checked) => updatePreference('advertising', checked)}
            />
            <CookieSettingRow
              label="كوكيز وسائل التواصل"
              description="لمشاركة المحتوى على وسائل التواصل"
              checked={preferences.social}
              onCheckedChange={(checked) => updatePreference('social', checked)}
            />
          </div>
          
          <div className="flex gap-2 mt-6">
            <Button onClick={handleSave} className="flex-1">
              حفظ الإعدادات
            </Button>
            <Button variant="outline" onClick={() => setSettingsOpen(false)}>
              إلغاء
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

interface CookieSettingRowProps {
  label: string;
  description: string;
  checked: boolean;
  onCheckedChange?: (checked: boolean) => void;
  disabled?: boolean;
}

function CookieSettingRow({ label, description, checked, onCheckedChange, disabled = false }: CookieSettingRowProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="space-y-1">
        <Label className="font-medium">{label}</Label>
        <p className="text-xs text-muted-foreground">
          {description}
        </p>
      </div>
      <Switch 
        checked={checked}
        onCheckedChange={onCheckedChange}
        disabled={disabled}
      />
    </div>
  );
}
