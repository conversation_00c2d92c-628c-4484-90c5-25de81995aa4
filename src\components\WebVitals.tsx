'use client';

import { useEffect } from 'react';
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

interface WebVitalsProps {
  gaId?: string;
}

export function WebVitals({ gaId }: WebVitalsProps) {
  useEffect(() => {
    if (!gaId) return;

    const sendToGoogleAnalytics = ({ name, delta, value, id }: any) => {
      // Send to Google Analytics 4
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('event', name, {
          event_category: 'Web Vitals',
          event_label: id,
          value: Math.round(name === 'CLS' ? delta * 1000 : delta),
          non_interaction: true,
        });
      }

      // Also log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Web Vitals] ${name}:`, { delta, value, id });
      }
    };

    // Measure Core Web Vitals
    getCLS(sendToGoogleAnalytics);
    getFID(sendToGoogleAnalytics);
    getFCP(sendToGoogleAnalytics);
    getLCP(sendToGoogleAnalytics);
    getTTFB(sendToGoogleAnalytics);
  }, [gaId]);

  return null;
}

// Performance monitoring hook
export function usePerformanceMonitoring() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Monitor page load performance
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming;
          
          // Log performance metrics in development
          if (process.env.NODE_ENV === 'development') {
            console.log('[Performance] Navigation timing:', {
              domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
              loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart,
              firstByte: navEntry.responseStart - navEntry.requestStart,
              domInteractive: navEntry.domInteractive - navEntry.navigationStart,
            });
          }
        }
      }
    });

    observer.observe({ entryTypes: ['navigation'] });

    return () => observer.disconnect();
  }, []);
}

// Resource loading optimization
export function preloadCriticalResources() {
  if (typeof window === 'undefined') return;

  // Preload critical fonts
  const fontLinks = [
    'https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Wafeq:wght@600&display=swap'
  ];

  fontLinks.forEach(href => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'style';
    link.href = href;
    document.head.appendChild(link);
  });

  // Preconnect to external domains
  const preconnectDomains = [
    'https://www.google-analytics.com',
    'https://www.googletagmanager.com',
    'https://pagead2.googlesyndication.com'
  ];

  preconnectDomains.forEach(href => {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = href;
    link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
  });
}

// Image optimization utilities
export function optimizeImages() {
  if (typeof window === 'undefined') return;

  // Lazy load images that are not in viewport
  const images = document.querySelectorAll('img[data-src]');
  
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement;
        img.src = img.dataset.src || '';
        img.classList.remove('lazy');
        observer.unobserve(img);
      }
    });
  });

  images.forEach(img => imageObserver.observe(img));
}

// Service Worker registration for caching
export function registerServiceWorker() {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) return;

  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('[SW] Service Worker registered successfully:', registration);
      })
      .catch((error) => {
        console.log('[SW] Service Worker registration failed:', error);
      });
  });
}

// Critical CSS inlining utility
export function inlineCriticalCSS() {
  if (typeof window === 'undefined') return;

  // This would typically be done at build time, but we can optimize loading
  const criticalStyles = `
    /* Critical CSS for above-the-fold content */
    body { font-family: 'Tajawal', sans-serif; }
    .header { background: #fff; border-bottom: 1px solid #e5e7eb; }
    .loading { opacity: 0.5; pointer-events: none; }
  `;

  const style = document.createElement('style');
  style.textContent = criticalStyles;
  document.head.appendChild(style);
}

// Performance budget monitoring
export function monitorPerformanceBudget() {
  if (typeof window === 'undefined') return;

  const budgets = {
    maxBundleSize: 500 * 1024, // 500KB
    maxImageSize: 200 * 1024,  // 200KB
    maxFontSize: 100 * 1024,   // 100KB
  };

  // Monitor resource sizes
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      const resource = entry as PerformanceResourceTiming;
      
      if (resource.transferSize > budgets.maxBundleSize && resource.name.includes('.js')) {
        console.warn(`[Performance Budget] Large JS bundle detected: ${resource.name} (${resource.transferSize} bytes)`);
      }
      
      if (resource.transferSize > budgets.maxImageSize && /\.(jpg|jpeg|png|webp|avif)/.test(resource.name)) {
        console.warn(`[Performance Budget] Large image detected: ${resource.name} (${resource.transferSize} bytes)`);
      }
      
      if (resource.transferSize > budgets.maxFontSize && /\.(woff|woff2|ttf)/.test(resource.name)) {
        console.warn(`[Performance Budget] Large font detected: ${resource.name} (${resource.transferSize} bytes)`);
      }
    }
  });

  observer.observe({ entryTypes: ['resource'] });
}

// Initialize all performance optimizations
export function initializePerformanceOptimizations() {
  if (typeof window === 'undefined') return;

  preloadCriticalResources();
  optimizeImages();
  registerServiceWorker();
  inlineCriticalCSS();
  
  if (process.env.NODE_ENV === 'development') {
    monitorPerformanceBudget();
  }
}
