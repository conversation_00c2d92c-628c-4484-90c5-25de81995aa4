
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormMessage, FormLabel } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';
import { Info, Calculator } from 'lucide-react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const formSchema = z.object({
  highSchoolGrade: requiredNumber().min(0, "الدرجة لا يمكن أن تكون سالبة.").max(100, "الدرجة لا يمكن أن تزيد عن 100."),
  qudratGrade: requiredNumber().min(0, "الدرجة لا يمكن أن تكون سالبة.").max(100, "الدرجة لا يمكن أن تزيد عن 100."),
  tahsiliGrade: requiredNumber().min(0, "الدرجة لا يمكن أن تكون سالبة.").max(100, "الدرجة لا يمكن أن تزيد عن 100.").optional(),
});

type FormValues = z.infer<typeof formSchema>;

const weightPresets = {
  scientific: { hs: 30, qudrat: 30, tahsili: 40, label: "علمي/هندسي" },
  health: { hs: 30, qudrat: 20, tahsili: 50, label: "صحي" },
  admin: { hs: 50, qudrat: 50, tahsili: 0, label: "إداري/إنساني" },
};

export function WeightedGradeCalculatorTool() {
  const [result, setResult] = useState<{ finalGrade: number } | null>(null);
  const [activePresetKey, setActivePresetKey] = useState('scientific');

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      highSchoolGrade: undefined,
      qudratGrade: undefined,
      tahsiliGrade: undefined,
    },
  });

  const { setValue, watch } = form;

  const handlePresetChange = (presetKey: string) => {
    setActivePresetKey(presetKey);
    // @ts-ignore
    const preset = weightPresets[presetKey];
    if (preset.tahsili === 0) {
      setValue('tahsiliGrade', 0);
    }
  };

  const onSubmit = (data: FormValues) => {
    // @ts-ignore
    const weights = weightPresets[activePresetKey];
    const tahsiliGrade = data.tahsiliGrade || 0;
    
    const weightedSum = 
      (data.highSchoolGrade * weights.hs / 100) +
      (data.qudratGrade * weights.qudrat / 100) +
      (tahsiliGrade * weights.tahsili / 100);
    
    setResult({ finalGrade: weightedSum });
  };
  
  // @ts-ignore
  const isTahsiliDisabled = weightPresets[activePresetKey].tahsili === 0;
  // @ts-ignore
  const activeWeights = weightPresets[activePresetKey];

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>حاسبة النسبة الموزونة</CardTitle>
        <CardDescription>احسب نسبتك الموزونة للقبول الجامعي بناءً على المسار الذي تختاره.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            
             <Tabs defaultValue={activePresetKey} onValueChange={handlePresetChange} className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="scientific">{weightPresets.scientific.label}</TabsTrigger>
                    <TabsTrigger value="health">{weightPresets.health.label}</TabsTrigger>
                    <TabsTrigger value="admin">{weightPresets.admin.label}</TabsTrigger>
                </TabsList>
            </Tabs>

             <Alert variant="default" className="bg-muted/50">
                <Info className="h-4 w-4" />
                <AlertTitle>معادلة المسار المحدد</AlertTitle>
                <AlertDescription className="font-mono text-sm" dir="ltr">
                  ({activeWeights.hs}% * ثانوية) + ({activeWeights.qudrat}% * قدرات)
                  {!isTahsiliDisabled && ` + (${activeWeights.tahsili}% * تحصيلي)`}
                </AlertDescription>
            </Alert>


            <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField control={form.control} name="highSchoolGrade" render={({ field }) => (<FormItem><FormLabel>درجة الثانوية التراكمي (من 100)</FormLabel><FormControl><Input type="number" step="0.01" min="0" max="100" placeholder="e.g. 95.5" {...field} /></FormControl><FormMessage /></FormItem>)} />
                  <FormField control={form.control} name="qudratGrade" render={({ field }) => (<FormItem><FormLabel>درجة القدرات العامة (من 100)</FormLabel><FormControl><Input type="number" step="0.01" min="0" max="100" placeholder="e.g. 85" {...field} /></FormControl><FormMessage /></FormItem>)} />
                </div>
                 <div className={cn("transition-opacity", isTahsiliDisabled && "opacity-50")}>
                    <FormField control={form.control} name="tahsiliGrade" render={({ field }) => (<FormItem><FormLabel>درجة الاختبار التحصيلي (من 100)</FormLabel><FormControl><Input type="number" step="0.01" min="0" max="100" placeholder={isTahsiliDisabled ? "غير مطلوب لهذا المسار" : "e.g. 88"} {...field} disabled={isTahsiliDisabled} /></FormControl><FormMessage /></FormItem>)} />
                 </div>
            </div>

            <div className="flex flex-col items-center space-y-4 pt-6">
              <Button type="submit" size="lg" className="w-full sm:w-auto px-12">
                <Calculator className="ml-2 h-4 w-4" />
                احسب
              </Button>
              
              {result && (
                <div className="text-center p-6 bg-primary/10 rounded-lg border-2 border-primary/20 w-full">
                  <p className="text-sm text-primary/80 mb-2">النسبة الموزونة النهائية</p>
                  <p className="text-5xl font-bold text-primary font-mono">
                    {result.finalGrade.toFixed(2)}%
                  </p>
                </div>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
