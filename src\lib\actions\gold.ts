
'use server';

import { COUNTRIES_CURRENCIES, COUNTRY_CODE_TO_CURRENCY } from '@/lib/constants/currencies';
import { getIpInfo } from './ip';

interface PriceData {
    perOunce: number;
    perGram: number;
    perKilo: number;
}

interface FullPriceData {
    success: boolean;
    timestamp: string;
    selectedCurrency: string;
    countryInfo: (typeof COUNTRIES_CURRENCIES)[keyof typeof COUNTRIES_CURRENCIES];
    prices: {
        gold: {
            usd: PriceData;
            local: PriceData;
            karats: Record<string, number>;
        };
        silver: {
            usd: PriceData;
            local: PriceData;
        }
    };
    source: string;
    error?: string;
}

// Function to get real exchange rates
async function getExchangeRate(currency: string): Promise<number> {
  // If it's USD, return 1 (base currency)
  if (currency === 'USD') {
    return 1;
  }

  try {
    const response = await fetch(
      `https://api.exchangerate-api.com/v4/latest/USD`,
      {
        next: { revalidate: 3600 }, // Revalidate every hour
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; Gold Price Tool)',
        },
      }
    );

    if (response.ok) {
      const data = await response.json();
      if (data && data.rates && data.rates[currency]) {
        return parseFloat(data.rates[currency]);
      }
    }
  } catch (error) {
    console.error('Error fetching exchange rate from ExchangeRate-API:', error);
  }

  // Fallback to approximate current rates
  const fallbackRates: Record<string, number> = {
    'SAR': 3.75, 'AED': 3.67, 'KWD': 0.31, 'BHD': 0.38, 'OMR': 0.38,
    'QAR': 3.64, 'EGP': 49.50, 'JOD': 0.71, 'LBP': 15000, 'SYP': 13000,
    'IQD': 1300, 'YER': 250, 'SDG': 600, 'LYD': 4.85, 'TND': 3.15,
    'MAD': 10.20, 'DZD': 134.50, 'MRU': 39.70, 'SOS': 570, 'DJF': 177,
    'KMF': 465, 'EUR': 0.92, 'USD': 1.0
  };
  
  return fallbackRates[currency] || 1;
}

async function fetchMetalPrice(ticker: 'GC=F' | 'SI=F') {
    try {
        const response = await fetch(`https://query1.finance.yahoo.com/v8/finance/chart/${ticker}`, {
            next: { revalidate: 900 }, // Revalidate every 15 minutes
            headers: { 'User-Agent': 'Mozilla/5.0 (compatible; Gold Price Tool)' }
        });
        if (response.ok) {
            const data = await response.json();
            const meta = data?.chart?.result?.[0]?.meta;
            if (meta?.regularMarketPrice) {
                return parseFloat(meta.regularMarketPrice);
            }
        }
    } catch (e) {
        console.error(`Error fetching from Yahoo Finance for ${ticker}:`, e);
    }
    return null;
}

export async function getGoldAndSilverPrices(requestedCurrency?: string): Promise<FullPriceData> {
  let selectedCurrency = 'SAR'; // Default
  let error;

  if (requestedCurrency) {
    selectedCurrency = requestedCurrency;
  } else {
    // Detect currency from IP
    const ipInfo = await getIpInfo();
    error = ipInfo.error;
    if (ipInfo.countryCode && COUNTRY_CODE_TO_CURRENCY[ipInfo.countryCode]) {
      selectedCurrency = COUNTRY_CODE_TO_CURRENCY[ipInfo.countryCode];
    }
  }
  
  // التحقق من أن العملة مدعومة
  if (!COUNTRIES_CURRENCIES[selectedCurrency as keyof typeof COUNTRIES_CURRENCIES]) {
    selectedCurrency = 'SAR';
  }
  
  const exchangeRate = await getExchangeRate(selectedCurrency);
  
  const [goldPriceUSD, silverPriceUSD] = await Promise.all([
      fetchMetalPrice('GC=F'),
      fetchMetalPrice('SI=F')
  ]);

  const finalGoldPriceUSD = goldPriceUSD || 2658.75; // Fallback gold price
  const finalSilverPriceUSD = silverPriceUSD || 29.50; // Fallback silver price

  const source = goldPriceUSD && silverPriceUSD ? 'Yahoo Finance' : 'مصادر محدثة (Fallback)';
  if (!goldPriceUSD || !silverPriceUSD) {
    error = (error ? error + '; ' : '') + 'الأسعار تقديرية حديثة - قد تختلف عن الأسعار اللحظية';
  }

  const processMetal = (priceUSD: number) => {
    const priceLocal = priceUSD * exchangeRate;
    const pricePerGramUSD = priceUSD / 31.1035;
    const pricePerGramLocal = pricePerGramUSD * exchangeRate;
    const pricePerKiloUSD = pricePerGramUSD * 1000;
    const pricePerKiloLocal = pricePerGramLocal * 1000;
    return {
      usd: { perOunce: priceUSD, perGram: pricePerGramUSD, perKilo: pricePerKiloUSD },
      local: { perOunce: priceLocal, perGram: pricePerGramLocal, perKilo: pricePerKiloLocal },
    };
  };

  const goldData = processMetal(finalGoldPriceUSD);
  const silverData = processMetal(finalSilverPriceUSD);

  const karatPrices = {
    '24K': goldData.local.perGram, '22K': goldData.local.perGram * (22/24),
    '21K': goldData.local.perGram * (21/24), '18K': goldData.local.perGram * (18/24),
  };

  return {
    success: true,
    timestamp: new Date().toISOString(),
    selectedCurrency,
    countryInfo: COUNTRIES_CURRENCIES[selectedCurrency as keyof typeof COUNTRIES_CURRENCIES],
    prices: {
        gold: { ...goldData, karats: karatPrices },
        silver: silverData,
    },
    source,
    error,
  };
}
