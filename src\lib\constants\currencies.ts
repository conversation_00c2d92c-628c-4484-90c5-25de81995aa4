
// معدلات التحويل التقديرية للعملات العربية مقابل الدولار
export const EXCHANGE_RATES = {
  SAR: 3.75,    // الريال السعودي
  AED: 3.67,    // الدرهم الإماراتي
  KWD: 0.31,    // الدينار الكويتي
  QAR: 3.64,    // الريال القطري
  OMR: 0.38,    // الريال العماني
  BHD: 0.38,    // الدينار البحريني
  JOD: 0.71,    // الدينار الأردني
  LBP: 15000,   // الليرة اللبنانية
  SYP: 13000,   // الليرة السورية
  IQD: 1470,    // الدينار العراقي
  EGP: 49.00,   // الجنيه المصري
  LYD: 4.80,    // الدينار الليبي
  TND: 3.20,    // الدينار التونسي
  DZD: 135,     // الدينار الجزائري
  MAD: 10.00,   // الدرهم المغربي
  MRU: 40.00,   // الأوقية الموريتانية
  SDG: 500,     // الجنيه السوداني
  SOS: 570,     // الشلن الصومالي
  DJF: 177,     // الفرنك الجيبوتي
  KMF: 460,     // الفرنك القمري
  YER: 1500,    // الريال اليمني
  USD: 1.00,    // الدولار الأمريكي (مرجع)
  EUR: 0.92,    // اليورو
};

// أسماء العملات والدول
export const COUNTRIES_CURRENCIES = {
  SAR: { name: 'المملكة العربية السعودية', currency: 'ريال سعودي', countryCode: 'SA' },
  AED: { name: 'دولة الإمارات العربية المتحدة', currency: 'درهم إماراتي', countryCode: 'AE' },
  KWD: { name: 'دولة الكويت', currency: 'دينار كويتي', countryCode: 'KW' },
  QAR: { name: 'دولة قطر', currency: 'ريال قطري', countryCode: 'QA' },
  OMR: { name: 'سلطنة عُمان', currency: 'ريال عماني', countryCode: 'OM' },
  BHD: { name: 'مملكة البحرين', currency: 'دينار بحريني', countryCode: 'BH' },
  EGP: { name: 'جمهورية مصر العربية', currency: 'جنيه مصري', countryCode: 'EG' },
  JOD: { name: 'المملكة الأردنية الهاشمية', currency: 'دينار أردني', countryCode: 'JO' },
  LBP: { name: 'الجمهورية اللبنانية', currency: 'ليرة لبنانية', countryCode: 'LB' },
  SYP: { name: 'الجمهورية العربية السورية', currency: 'ليرة سورية', countryCode: 'SY' },
  IQD: { name: 'جمهورية العراق', currency: 'دينار عراقي', countryCode: 'IQ' },
  LYD: { name: 'دولة ليبيا', currency: 'دينار ليبي', countryCode: 'LY' },
  TND: { name: 'الجمهورية التونسية', currency: 'دينار تونسي', countryCode: 'TN' },
  DZD: { name: 'الجمهورية الجزائرية', currency: 'دينار جزائري', countryCode: 'DZ' },
  MAD: { name: 'المملكة المغربية', currency: 'درهم مغربي', countryCode: 'MA' },
  MRU: { name: 'الجمهورية الإسلامية الموريتانية', currency: 'أوقية موريتانية', countryCode: 'MR' },
  SDG: { name: 'جمهورية السودان', currency: 'جنيه سوداني', countryCode: 'SD' },
  SOS: { name: 'جمهورية الصومال', currency: 'شلن صومالي', countryCode: 'SO' },
  DJF: { name: 'جمهورية جيبوتي', currency: 'فرنك جيبوتي', countryCode: 'DJ' },
  KMF: { name: 'الاتحاد القمري', currency: 'فرنك قمري', countryCode: 'KM' },
  YER: { name: 'الجمهورية اليمنية', currency: 'ريال يمني', countryCode: 'YE' },
  USD: { name: 'الولايات المتحدة الأمريكية', currency: 'دولار أمريكي', countryCode: 'US' },
  EUR: { name: 'الاتحاد الأوروبي', currency: 'يورو', countryCode: 'EU' },
};

export const COUNTRY_CODE_TO_CURRENCY: Record<string, string> = Object.entries(COUNTRIES_CURRENCIES)
    .reduce((acc, [currency, details]) => {
        if(details.countryCode) {
            acc[details.countryCode] = currency;
        }
        return acc;
}, {} as Record<string, string>);


export const ARAB_COUNTRIES = [
  { code: '966', name: 'المملكة العربية السعودية', flag: '🇸🇦', countryCode: 'SA' },
  { code: '971', name: 'الإمارات العربية المتحدة', flag: '🇦🇪', countryCode: 'AE' },
  { code: '20', name: 'مصر', flag: '🇪🇬', countryCode: 'EG' },
  { code: '965', name: 'الكويت', flag: '🇰🇼', countryCode: 'KW' },
  { code: '974', name: 'قطر', flag: '🇶🇦', countryCode: 'QA' },
  { code: '973', name: 'البحرين', flag: '🇧🇭', countryCode: 'BH' },
  { code: '968', name: 'عُمان', flag: '🇴🇲', countryCode: 'OM' },
  { code: '962', name: 'الأردن', flag: '🇯🇴', countryCode: 'JO' },
  { code: '961', name: 'لبنان', flag: '🇱🇧', countryCode: 'LB' },
  { code: '963', name: 'سوريا', flag: '🇸🇾', countryCode: 'SY' },
  { code: '964', name: 'العراق', flag: '🇮🇶', countryCode: 'IQ' },
  { code: '218', name: 'ليبيا', flag: '🇱🇾', countryCode: 'LY' },
  { code: '216', name: 'تونس', flag: '🇹🇳', countryCode: 'TN' },
  { code: '213', name: 'الجزائر', flag: '🇩🇿', countryCode: 'DZ' },
  { code: '212', name: 'المغرب', flag: '🇲🇦', countryCode: 'MA' },
  { code: '222', name: 'موريتانيا', flag: '🇲🇷', countryCode: 'MR' },
  { code: '249', name: 'السودان', flag: '🇸🇩', countryCode: 'SD' },
  { code: '252', name: 'الصومال', flag: '🇸🇴', countryCode: 'SO' },
  { code: '253', name: 'جيبوتي', flag: '🇩🇯', countryCode: 'DJ' },
  { code: '269', name: 'جزر القمر', flag: '🇰🇲', countryCode: 'KM' },
  { code: '967', name: 'اليمن', flag: '🇾🇪', countryCode: 'YE' },
];

// This is not used for currency, just for mapping ip geo location to phone codes
export const COUNTRY_PHONE_CODES: Record<string, string> = {
  SA: '966', AE: '971', EG: '20', KW: '965', QA: '974', BH: '973', OM: '968',
  JO: '962', LB: '961', SY: '963', IQ: '964', LY: '218', TN: '216', DZ: '213',
  MA: '212', MR: '222', SD: '249', SO: '252', DJ: '253', KM: '269', YE: '967'
};
