import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { PageHeader } from '@/components/PageHeader';
import { Calendar, Clock, ArrowRight, ArrowLeft } from 'lucide-react';

const articles = {
  'how-to-calculate-zakat': {
    title: 'كيفية حساب الزكاة بطريقة صحيحة',
    description: 'دليل شامل لحساب زكاة المال والذهب والفضة وفقاً للأحكام الشرعية',
    date: '2024-12-15',
    readTime: '5 دقائق',
    category: 'إسلامية',
    content: `
      <h2 class="text-xl font-bold mt-6 mb-4">مقدمة عن الزكاة</h2>
      <p>الزكاة هي الركن الثالث من أركان الإسلام، وهي فريضة مالية على كل مسلم بالغ عاقل يملك النصاب. تُعتبر الزكاة حقاً للفقراء والمساكين في أموال الأغنياء.</p>
      
      <h3 class="text-lg font-bold mt-6 mb-3">شروط وجوب الزكاة</h3>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>الإسلام</li>
        <li>البلوغ والعقل</li>
        <li>الحرية</li>
        <li>ملك النصاب</li>
        <li>حولان الحول (مرور سنة هجرية كاملة)</li>
      </ul>
      
      <h3 class="text-lg font-bold mt-6 mb-3">أنواع الأموال التي تجب فيها الزكاة</h3>
      
      <h4 class="font-bold mt-4 mb-2">1. زكاة النقود والأوراق المالية</h4>
      <p><strong>النصاب:</strong> ما يعادل 85 جراماً من الذهب الخالص</p>
      <p><strong>المقدار:</strong> 2.5% من المبلغ</p>
      <p><strong>مثال:</strong> إذا كان لديك 100,000 ريال، فزكاتها = 100,000 × 2.5% = 2,500 ريال</p>
      
      <h4 class="font-bold mt-4 mb-2">2. زكاة الذهب والفضة</h4>
      <p><strong>نصاب الذهب:</strong> 85 جراماً من الذهب الخالص</p>
      <p><strong>نصاب الفضة:</strong> 595 جراماً من الفضة الخالصة</p>
      <p><strong>المقدار:</strong> 2.5% من القيمة</p>
      
      <h4 class="font-bold mt-4 mb-2">3. زكاة عروض التجارة</h4>
      <p>تُقدر قيمة البضائع في نهاية الحول وتُخرج منها 2.5%</p>
      
      <h3 class="text-lg font-bold mt-6 mb-3">كيفية حساب الزكاة خطوة بخطوة</h3>
      <ol class="list-decimal mr-6 mt-2 space-y-2">
        <li><strong>حدد نوع المال:</strong> نقود، ذهب، فضة، أو عروض تجارة</li>
        <li><strong>تأكد من بلوغ النصاب:</strong> قارن المبلغ بالنصاب المحدد شرعاً</li>
        <li><strong>تأكد من حولان الحول:</strong> مرور سنة هجرية كاملة على ملكية المال</li>
        <li><strong>احسب المقدار:</strong> اضرب المبلغ في 2.5%</li>
        <li><strong>أخرج الزكاة:</strong> ادفع المبلغ المحسوب للمستحقين</li>
      </ol>
      
      <h3 class="text-lg font-bold mt-6 mb-3">مستحقو الزكاة</h3>
      <p>حدد الله تعالى ثمانية أصناف لمستحقي الزكاة في القرآن الكريم:</p>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>الفقراء</li>
        <li>المساكين</li>
        <li>العاملون عليها</li>
        <li>المؤلفة قلوبهم</li>
        <li>في الرقاب</li>
        <li>الغارمون</li>
        <li>في سبيل الله</li>
        <li>ابن السبيل</li>
      </ul>
      
      <h3 class="text-lg font-bold mt-6 mb-3">نصائح مهمة</h3>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>احتفظ بسجل دقيق لأموالك وتواريخ حولان الحول</li>
        <li>استخدم <a href="/tools/zakat-calculator" class="text-primary hover:underline">حاسبة الزكاة</a> لحساب دقيق</li>
        <li>استشر عالماً مختصاً في الحالات المعقدة</li>
        <li>أخرج الزكاة في وقتها المحدد</li>
        <li>تأكد من وصول الزكاة للمستحقين الحقيقيين</li>
      </ul>
      
      <div class="bg-blue-50 border border-blue-200 p-4 rounded-lg mt-6">
        <h4 class="font-bold text-blue-800 mb-2">💡 نصيحة</h4>
        <p class="text-blue-700">يمكنك استخدام حاسبة الزكاة المتوفرة على موقعنا لحساب زكاتك بدقة وسهولة. الحاسبة تأخذ في الاعتبار جميع الأحكام الشرعية وتوفر لك النتيجة الصحيحة.</p>
      </div>
    `,
  },
  'hijri-calendar-guide': {
    title: 'دليل شامل للتقويم الهجري والميلادي',
    description: 'تعرف على الفروق بين التقويمين وكيفية التحويل بينهما بدقة',
    date: '2024-12-10',
    readTime: '7 دقائق',
    category: 'تعليمية',
    content: `
      <h2 class="text-xl font-bold mt-6 mb-4">مقدمة عن التقويمين</h2>
      <p>يستخدم العالم اليوم تقويمين رئيسيين: التقويم الهجري والتقويم الميلادي. لكل منهما تاريخه وخصائصه المميزة.</p>
      
      <h3 class="text-lg font-bold mt-6 mb-3">التقويم الهجري</h3>
      <p>بدأ التقويم الهجري مع هجرة النبي محمد صلى الله عليه وسلم من مكة إلى المدينة عام 622 ميلادي.</p>
      
      <h4 class="font-bold mt-4 mb-2">خصائص التقويم الهجري:</h4>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>يعتمد على دورة القمر</li>
        <li>السنة الهجرية = 354 أو 355 يوماً</li>
        <li>12 شهراً قمرياً</li>
        <li>الشهر = 29 أو 30 يوماً</li>
      </ul>
      
      <h4 class="font-bold mt-4 mb-2">الأشهر الهجرية:</h4>
      <ol class="list-decimal mr-6 mt-2 space-y-1">
        <li>محرم</li>
        <li>صفر</li>
        <li>ربيع الأول</li>
        <li>ربيع الآخر</li>
        <li>جمادى الأولى</li>
        <li>جمادى الآخرة</li>
        <li>رجب</li>
        <li>شعبان</li>
        <li>رمضان</li>
        <li>شوال</li>
        <li>ذو القعدة</li>
        <li>ذو الحجة</li>
      </ol>
      
      <h3 class="text-lg font-bold mt-6 mb-3">التقويم الميلادي</h3>
      <p>يُعرف أيضاً بالتقويم الغريغوري، ويبدأ من ميلاد المسيح عليه السلام.</p>
      
      <h4 class="font-bold mt-4 mb-2">خصائص التقويم الميلادي:</h4>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>يعتمد على دورة الشمس</li>
        <li>السنة الميلادية = 365 أو 366 يوماً</li>
        <li>12 شهراً شمسياً</li>
        <li>الشهر = 28-31 يوماً</li>
      </ul>
      
      <h3 class="text-lg font-bold mt-6 mb-3">الفروق الأساسية</h3>
      <table class="w-full border-collapse border border-gray-300 mt-4">
        <thead>
          <tr class="bg-gray-50">
            <th class="border border-gray-300 p-2">الخاصية</th>
            <th class="border border-gray-300 p-2">التقويم الهجري</th>
            <th class="border border-gray-300 p-2">التقويم الميلادي</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="border border-gray-300 p-2">الأساس</td>
            <td class="border border-gray-300 p-2">دورة القمر</td>
            <td class="border border-gray-300 p-2">دورة الشمس</td>
          </tr>
          <tr>
            <td class="border border-gray-300 p-2">طول السنة</td>
            <td class="border border-gray-300 p-2">354-355 يوم</td>
            <td class="border border-gray-300 p-2">365-366 يوم</td>
          </tr>
          <tr>
            <td class="border border-gray-300 p-2">بداية التقويم</td>
            <td class="border border-gray-300 p-2">هجرة النبي (622م)</td>
            <td class="border border-gray-300 p-2">ميلاد المسيح</td>
          </tr>
        </tbody>
      </table>
      
      <h3 class="text-lg font-bold mt-6 mb-3">كيفية التحويل بين التقويمين</h3>
      <p>التحويل بين التقويمين معقد بسبب اختلاف طول السنة. يمكن استخدام الصيغ التقريبية:</p>
      
      <h4 class="font-bold mt-4 mb-2">من الهجري إلى الميلادي:</h4>
      <p class="bg-gray-100 p-3 rounded font-mono">السنة الميلادية = السنة الهجرية × 0.97 + 622</p>
      
      <h4 class="font-bold mt-4 mb-2">من الميلادي إلى الهجري:</h4>
      <p class="bg-gray-100 p-3 rounded font-mono">السنة الهجرية = (السنة الميلادية - 622) × 1.03</p>
      
      <div class="bg-yellow-50 border border-yellow-200 p-4 rounded-lg mt-6">
        <h4 class="font-bold text-yellow-800 mb-2">⚠️ تنبيه</h4>
        <p class="text-yellow-700">هذه الصيغ تعطي تقديراً تقريبياً فقط. للحصول على تحويل دقيق، استخدم <a href="/tools/date-converter" class="text-primary hover:underline">محول التاريخ</a> المتوفر على موقعنا.</p>
      </div>
      
      <h3 class="text-lg font-bold mt-6 mb-3">أهمية كل تقويم</h3>
      
      <h4 class="font-bold mt-4 mb-2">التقويم الهجري:</h4>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>تحديد المناسبات الإسلامية (رمضان، الحج، العيدين)</li>
        <li>التوثيق الشرعي والتاريخي</li>
        <li>الهوية الإسلامية والثقافية</li>
      </ul>
      
      <h4 class="font-bold mt-4 mb-2">التقويم الميلادي:</h4>
      <ul class="list-disc mr-6 mt-2 space-y-1">
        <li>المعاملات التجارية والدولية</li>
        <li>التعليم والعمل</li>
        <li>التخطيط الزراعي والموسمي</li>
      </ul>
    `,
  },
};

type Props = {
  params: { slug: string };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const article = articles[params.slug as keyof typeof articles];
  if (!article) {
    return {};
  }
  return {
    title: article.title,
    description: article.description,
    keywords: [article.category, 'مقالات عربية', 'نصائح', 'شروحات'],
  };
}

export default function ArticlePage({ params }: Props) {
  const article = articles[params.slug as keyof typeof articles];

  if (!article) {
    notFound();
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-4 sm:p-6 md:p-8">
      {/* Breadcrumb */}
      <nav className="mb-6">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Link href="/" className="hover:text-primary">الرئيسية</Link>
          <ArrowLeft className="h-3 w-3" />
          <Link href="/articles" className="hover:text-primary">المقالات</Link>
          <ArrowLeft className="h-3 w-3" />
          <span>{article.title}</span>
        </div>
      </nav>

      {/* Article Header */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <span className="text-xs bg-primary/10 text-primary px-3 py-1 rounded-full">
            {article.category}
          </span>
        </div>
        
        <h1 className="text-3xl md:text-4xl font-headline font-bold mb-4">
          {article.title}
        </h1>
        
        <p className="text-lg text-muted-foreground mb-6">
          {article.description}
        </p>
        
        <div className="flex items-center gap-6 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            <span>{new Date(article.date).toLocaleDateString('ar-SA-u-nu-latn')}</span>
          </div>
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            <span>{article.readTime}</span>
          </div>
        </div>
      </div>

      {/* Article Content */}
      <Card>
        <CardContent className="p-6">
          <div
            className="prose prose-lg max-w-none space-y-4"
            dangerouslySetInnerHTML={{ __html: article.content }}
          />
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="mt-8 flex justify-between">
        <Link
          href="/articles"
          className="inline-flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowRight className="h-4 w-4" />
          العودة للمقالات
        </Link>
      </div>
    </div>
  );
}

export async function generateStaticParams() {
  return Object.keys(articles).map((slug) => ({
    slug,
  }));
}
