'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

const FormSchema = z.object({
  numbers: z.string().min(1, 'الرجاء إدخال رقم واحد على الأقل.'),
});

interface Result {
  average: number;
  count: number;
  sum: number;
}

export function AverageCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const numbers = data.numbers
      .split(/[\s,]+/)
      .map(s => s.trim())
      .filter(s => s !== '')
      .map(Number)
      .filter(n => !isNaN(n));

    if (numbers.length === 0) {
      form.setError('numbers', {
        type: 'manual',
        message: 'لم يتم العثور على أرقام صالحة.',
      });
      setResult(null);
      return;
    }

    const sum = numbers.reduce((acc, val) => acc + val, 0);
    const count = numbers.length;
    const average = sum / count;

    setResult({ average, count, sum });
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حساب الوسط الحسابي</CardTitle>
        <CardDescription>أدخل الأرقام مفصولة بمسافة أو فاصلة أو سطر جديد.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="numbers"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>الأرقام</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="مثال: 10 20 30 40"
                      className="min-h-[150px] text-left"
                      dir="ltr"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full">
              احسب الوسط الحسابي
            </Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-4 text-center">النتيجة</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-primary/10 rounded-lg">
                <p className="text-4xl font-bold font-mono text-primary">{result.average.toLocaleString('en-US')}</p>
                <p className="text-sm text-muted-foreground">الوسط الحسابي</p>
              </div>
              <div className="p-4 bg-secondary rounded-lg">
                <p className="text-2xl font-bold font-mono">{result.sum.toLocaleString('en-US')}</p>
                <p className="text-sm text-muted-foreground">المجموع</p>
              </div>
              <div className="p-4 bg-secondary rounded-lg">
                <p className="text-2xl font-bold font-mono">{result.count}</p>
                <p className="text-sm text-muted-foreground">عدد الأرقام</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
