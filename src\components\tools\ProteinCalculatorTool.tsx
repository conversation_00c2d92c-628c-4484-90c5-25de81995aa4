
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Beef } from 'lucide-react';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  weight: requiredNumber().positive("الوزن يجب أن يكون رقمًا موجبًا."),
  activityLevel: z.string().min(1, 'الرجاء اختيار مستوى النشاط.'),
});

interface Result {
  proteinIntake: number;
}

const activityFactors: { [key: string]: { factor: number; label: string } } = {
  sedentary: { factor: 0.8, label: 'خامل (قليل أو بدون رياضة)' },
  light: { factor: 1.2, label: 'نشاط خفيف (رياضة 1-3 أيام/أسبوع)' },
  moderate: { factor: 1.5, label: 'نشاط متوسط (رياضة 3-5 أيام/أسبوع)' },
  active: { factor: 1.8, label: 'نشيط (رياضة 6-7 أيام/أسبوع)' },
  veryActive: { factor: 2.2, label: 'نشيط جدًا (رياضي أو كمال أجسام)' },
};

export function ProteinCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const { weight, activityLevel } = data;
    const factor = activityFactors[activityLevel].factor;
    const proteinIntake = weight * factor;

    setResult({
      proteinIntake: Math.round(proteinIntake),
    });
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حاسبة احتياج البروتين</CardTitle>
        <CardDescription>احسب احتياجك اليومي من البروتين بناءً على وزنك ومستوى نشاطك.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
               <FormField name="weight" control={form.control} render={({ field }) => (
                  <FormItem><FormLabel>الوزن (كجم)</FormLabel><FormControl><Input type="number" placeholder="مثال: 75" {...field} /></FormControl><FormMessage /></FormItem>
                )} />
              <FormField
                control={form.control}
                name="activityLevel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>مستوى النشاط</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl><SelectTrigger><SelectValue placeholder="اختر مستوى نشاطك" /></SelectTrigger></FormControl>
                      <SelectContent>
                        {Object.entries(activityFactors).map(([key, value]) => (
                          <SelectItem key={key} value={key}>{value.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Button type="submit" className="w-full">احسب احتياجك من البروتين</Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center space-y-4">
             <h3 className="text-xl font-headline font-semibold">النتيجة</h3>
              <div className="p-6 bg-primary/10 rounded-lg flex items-center justify-center gap-4">
                <Beef className="h-10 w-10 text-primary" />
                <div className="flex items-baseline gap-2">
                    <p className="text-5xl font-bold font-mono text-primary">
                        {result.proteinIntake}
                    </p>
                    <span className="text-xl font-medium text-primary/80">جرام/يوم</span>
                </div>
            </div>
             <p className="text-muted-foreground text-sm">هذه القيمة تقديرية. قد تختلف الاحتياجات الفردية. استشر مختصًا للحصول على توصيات شخصية.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
