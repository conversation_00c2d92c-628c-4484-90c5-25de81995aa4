
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  salary: requiredNumber().positive("الراتب يجب أن يكون رقمًا موجبًا."),
  daysPerWeek: requiredNumber().int().min(1).max(7).default(5),
  hoursPerDay: requiredNumber().positive("ساعات العمل يجب أن تكون رقمًا موجبًا."),
});

export function HourlyWageCalculatorTool() {
  const [hourlyWage, setHourlyWage] = useState<number | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const { salary, daysPerWeek, hoursPerDay } = data;
    // Assuming 4.33 weeks per month on average
    const totalHoursPerMonth = daysPerWeek * hoursPerDay * 4.33;
    const wage = salary / totalHoursPerMonth;
    setHourlyWage(wage);
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حساب أجر الساعة</CardTitle>
        <CardDescription>اكتشف قيمة ساعة عملك بناءً على راتبك الشهري وساعات العمل.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField name="salary" control={form.control} render={({ field }) => (
                <FormItem><FormLabel>الراتب الشهري (بالعملة المحلية)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
            )}/>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField name="daysPerWeek" control={form.control} render={({ field }) => (
                    <FormItem><FormLabel>أيام العمل في الأسبوع</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
                )}/>
                <FormField name="hoursPerDay" control={form.control} render={({ field }) => (
                    <FormItem><FormLabel>ساعات العمل في اليوم</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
                )}/>
            </div>
            <Button type="submit" className="w-full">احسب أجر الساعة</Button>
          </form>
        </Form>
        {hourlyWage !== null && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-2">أجر الساعة الخاص بك هو</h3>
            <div className="p-6 bg-primary/10 rounded-lg">
                <p className="text-5xl font-bold font-mono text-primary">
                    {hourlyWage.toFixed(2)}
                </p>
                 <p className="text-sm text-muted-foreground mt-2">لكل ساعة عمل</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
