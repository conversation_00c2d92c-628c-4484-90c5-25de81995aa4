
'use client';

import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Repeat, User, UserRound, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';

type Gender = 'male' | 'female';

const getQuestionText = (question: any, gender: Gender): string => {
  if (typeof question.text === 'string') {
    return question.text;
  }
  return question.text[gender];
};

const getAnswerText = (answer: any, gender: Gender): string => {
    if (typeof answer.text === 'string') {
      return answer.text;
    }
    return answer.text[gender];
};

type Answer = { text: string | { male: string; female: string }; points: number };
type Question = { text: string | { male: string; female: string }; answers: Answer[] };


const questions: Question[] = [
  // Patience
  {
    text: "أنت في طابور طويل يتحرك ببطء شديد. كيف تتصرف؟",
    answers: [
      { text: "أنتظر بصبر وأتصفح هاتفي أو أقرأ شيئًا.", points: 1 },
      { text: "أشعر بالملل وقد أتململ قليلاً، لكن أبقى في مكاني.", points: 2 },
      { text: "أبدأ بالتنهد بصوت مسموع وأنظر إلى ساعتي باستمرار.", points: 3 },
      { text: "أبحث عن طابور آخر أو أفكر في مغادرة المكان تمامًا.", points: 4 },
    ],
  },
  {
    text: "عندما تقود في زحام مروري، ما هو شعورك المعتاد؟",
    answers: [
      { text: "أستغل الوقت في الاستماع إلى بودكاست أو موسيقى هادئة.", points: 1 },
      { text: "أشعر ببعض الضيق، لكن أتقبل الأمر كجزء من القيادة في المدينة.", points: 2 },
      { text: "أشعر بالتوتر وأستخدم بوق السيارة بشكل متكرر.", points: 3 },
      { text: "أغضب بشدة، وأبدأ بالصراخ أو القيام بمناورات حادة.", points: 4 },
    ],
  },
  {
    text: "يشرح لك شخص ما شيئًا ما ببطء شديد. ماذا تفعل؟",
    answers: [
      { text: "أستمع بانتباه وأتركه يأخذ وقته.", points: 1 },
      { text: "أحاول مساعدته بأسئلة لتسريع الشرح قليلاً.", points: 2 },
      { text: "أقاطعه لأكمل الجملة نيابة عنه.", points: 3 },
      { text: "أظهر عدم صبري وأخبره أن يختصر.", points: 4 },
    ],
  },
  // Reaction to Criticism
  {
    text: "كيف تتعامل مع النقد البنّاء في العمل أو الدراسة؟",
    answers: [
      { text: "أرحب به وأعتبره فرصة للتحسين.", points: 1 },
      { text: "أستمع إليه، وقد أشعر ببعض الانزعاج لكن أفكر فيه لاحقًا.", points: 2 },
      { text: "أتخذ موقفًا دفاعيًا وأبدأ في تبرير أفعالي.", points: 3 },
      { text: "أشعر بالإهانة وأغضب من الشخص الذي انتقدني.", points: 4 },
    ],
  },
  {
    text: "إذا وجه لك شخص اتهامًا غير صحيح، ما هو رد فعلك الأول؟",
    answers: [
      { text: "أوضح موقفي بهدوء وأقدم الأدلة إذا لزم الأمر.", points: 1 },
      { text: "أشعر بالصدمة وأحاول فهم سبب الاتهام.", points: 2 },
      { text: "أرد بانفعال وأدافع عن نفسي بحدة.", points: 3 },
      { text: "أهاجم الشخص بالمقابل وأتهمه بالكذب.", points: 4 },
    ],
  },
  // Handling Mistakes
  {
    text: "عندما ترتكب خطأً، كيف يكون تصرفك عادةً؟",
    answers: [
      { text: "أعترف بالخطأ على الفور وأبحث عن حل.", points: 1 },
      { text: "أشعر بالسوء وأحاول إصلاحه بهدوء.", points: 2 },
      { text: "أشعر بالارتباك وأحاول إلقاء اللوم على الظروف.", points: 3 },
      { text: "أغضب من نفسي أو من الآخرين وأجد صعوبة في الاعتراف بالخطأ.", points: 4 },
    ],
  },
  {
    text: "أسقطت كوبًا من الماء عن طريق الخطأ. ماذا تفعل؟",
    answers: [
      { text: 'أقول "لا بأس" وأحضر منشفة لتجفيفه.', points: 1 },
      { text: "أتنهد وأشعر بالانزعاج من نفسي قليلاً أثناء التنظيف.", points: 2 },
      { text: "أصرخ أو ألوم نفسي بصوت عالٍ على عدم الانتباه.", points: 3 },
      { text: "أغضب وأترك الفوضى لشخص آخر لينظفها.", points: 4 },
    ],
  },
  // Social Interactions
  {
    text: "شخص ما لديه رأي سياسي أو اجتماعي يعارض رأيك بشدة. كيف تتفاعل؟",
    answers: [
      { text: "أستمع لوجهة نظره باحترام حتى لو لم أوافق عليها.", points: 1 },
      { text: "أحاول تجنب الدخول في نقاش حاد.", points: 2 },
      { text: "أدخل في نقاش حاد وأحاول إثبات أن رأيي هو الصحيح.", points: 3 },
      { text: "أغضب وأعتبره شخصًا جاهلاً أو سيئًا.", points: 4 },
    ],
  },
  {
    text: "يتأخر صديقك عن موعدكما. كيف تتصرف؟",
    answers: [
      { text: "أتفهم أن شيئًا ما قد يكون قد حدث وأنتظر بهدوء.", points: 1 },
      { text: "أتصل به للاطمئنان عليه ومعرفة سبب التأخير.", points: 2 },
      { text: "أرسل له رسائل غاضبة وأعبر عن استيائي.", points: 3 },
      { text: "أفكر في المغادرة وأعاقبه بتجاهل مكالماته لاحقًا.", points: 4 },
    ],
  },
  // Daily Stressors
  {
    text: "الإنترنت في منزلك بطيء جدًا. ما هو رد فعلك؟",
    answers: [
      { text: "أحاول إعادة تشغيل جهاز التوجيه وأبحث عن حلول بهدوء.", points: 1 },
      { text: "أشعر بالانزعاج وأؤجل ما كنت أفعله.", points: 2 },
      { text: "أضرب على المكتب أو أصرخ من الإحباط.", points: 3 },
      { text: "أتصل بخدمة العملاء وأتحدث معهم بغضب شديد.", points: 4 },
    ],
  },
  {
    text: "لا يمكنك العثور على مفاتيحك وأنت على وشك مغادرة المنزل. ماذا تفعل؟",
    answers: [
      { text: "أتوقف للحظة، وأتنفس بعمق، وأفكر في آخر مكان رأيتها فيه.", points: 1 },
      { text: "أبحث عنها في كل مكان وأنا أشعر بالتوتر يتزايد.", points: 2 },
      { text: "أبدأ بإلقاء الأشياء جانبًا وأنا أبحث بشكل فوضوي.", points: 3 },
      { text: "أغضب وأصرخ وألوم أي شخص آخر في المنزل.", points: 4 },
    ],
  },
   {
    text: "شخص ما يقاطعك أثناء حديثك. كيف ترد؟",
    answers: [
      { text: "أتركه ينهي فكرته ثم أستأنف حديثي بهدوء.", points: 1 },
      { text: "أنتظر حتى يتوقف ثم أقول 'كما كنت أقول...'.", points: 2 },
      { text: "أقاطعه بدوري وأرفع صوتي قليلاً لأستعيد انتباهه.", points: 3 },
      { text: "أغضب وأتوقف عن الحديث تمامًا.", points: 4 },
    ],
  },
  // Physical Reactions
  {
    text: "عندما تشعر بالغضب، هل تلاحظ أي أعراض جسدية (مثل تسارع دقات القلب، شد العضلات)؟",
    answers: [
      { text: "نادرًا، يمكنني التحكم في ردود أفعالي الجسدية.", points: 1 },
      { text: "أحيانًا، أشعر ببعض التوتر الجسدي.", points: 2 },
      { text: "غالبًا، أشعر بتوتر واضح وتسارع في دقات القلب.", points: 3 },
      { text: "دائمًا، أشعر بأن جسدي كله متوتر وقد أرتجف من الغضب.", points: 4 },
    ],
  },
  {
    text: "بعد تجربة محبطة، كم من الوقت تحتاج لتهدأ وتعود إلى طبيعتك؟",
    answers: [
      { text: "بضع دقائق فقط.", points: 1 },
      { text: "قد أحتاج إلى نصف ساعة أو ساعة لأهدأ تمامًا.", points: 2 },
      { text: "يستمر شعوري بالغضب أو الإحباط لعدة ساعات.", points: 3 },
      { text: "قد يفسد الأمر يومي بأكمله.", points: 4 },
    ],
  },
  // General Outlook
  {
    text: "بشكل عام، هل تميل إلى التركيز على الجوانب السلبية في المواقف؟",
    answers: [
      { text: "لا، أحاول دائمًا رؤية الجانب الإيجابي.", points: 1 },
      { text: "أحيانًا، لكن أحاول موازنة الأمور.", points: 2 },
      { text: "غالبًا، أجد نفسي أركز على ما قد يسوء.", points: 3 },
      { text: "دائمًا، أتوقع الأسوأ في معظم المواقف.", points: 4 },
    ],
  },
   {
    text: "عندما لا تسير الأمور كما خططت لها، رد فعلك الأولي هو:",
    answers: [
      { text: "البحث عن خطة بديلة أو طريقة للتكيف.", points: 1 },
      { text: "الشعور بالإحباط للحظة ثم محاولة إيجاد حل.", points: 2 },
      { text: "الشعور بالغضب والبحث عن شخص أو شيء لإلقاء اللوم عليه.", points: 3 },
      { text: "الشعور بالانهزام والاستسلام للإحباط.", points: 4 },
    ],
  },
  {
    text: "هل يصفك أصدقاؤك أو عائلتك بأنك شخص عصبي؟",
    answers: [
      { text: {
          male: "أبدًا، يصفونني دائمًا بالهادئ.",
          female: "أبدًا، يصفونني دائمًا بالهادئة."
        }, points: 1 },
      { text: "نادرًا، ربما في المواقف الشديدة فقط.", points: 2 },
      { text: "أحيانًا، يقولون إن لدي 'فتيل قصير'.", points: 3 },
      { text: "نعم، هذا وصف شائع لي.", points: 4 },
    ],
  },
  {
    text: "هل تجد صعوبة في مسامحة الآخرين على أخطائهم؟",
    answers: [
      { text: "لا، أؤمن بأن الجميع يخطئ وأميل إلى المسامحة بسهولة.", points: 1 },
      { text: "أحتاج إلى بعض الوقت، لكن يمكنني المسامحة في النهاية.", points: 2 },
      { text: "أجد صعوبة في ذلك وأحمل الضغينة لبعض الوقت.", points: 3 },
      { text: "من الصعب جدًا أن أسامح أو أنسى الإساءة.", points: 4 },
    ],
  },
  {
    text: "عندما تواجه مشكلة، هل تميل إلى الانفجار أولاً ثم التفكير لاحقًا؟",
    answers: [
      { text: "لا، أفضل التفكير بهدوء قبل التصرف.", points: 1 },
      { text: "نادرًا، أحاول التحكم في ردود أفعالي الأولية.", points: 2 },
      { text: "أحيانًا، قد أتفوه بكلمات أندم عليها لاحقًا.", points: 3 },
      { text: "نعم، رد فعلي الأولي هو الغضب والانفعال.", points: 4 },
    ],
  },
  {
    text: "هل تشعر أن الأمور الصغيرة تزعجك بسهولة (مثل صوت مضغ الطعام، شخص يمشي ببطء)؟",
    answers: [
      { text: "لا، نادرًا ما ألاحظ مثل هذه الأمور.", points: 1 },
      { text: "أحيانًا، قد تثير أعصابي قليلاً لكن أتجاهلها.", points: 2 },
      { text: "نعم، تزعجني هذه الأمور بشكل ملحوظ.", points: 3 },
      { text: "نعم، وتثير غضبي لدرجة أنني قد أعلق عليها بصوت عالٍ.", points: 4 },
    ],
  },
];

const getResult = (score: number, gender: Gender) => {
    const results = {
      level1: {
        title: "شخصية هادئة",
        text: "أنت شخص يتمتع بهدوء أعصاب ملحوظ وقدرة عالية على التحكم في ردود أفعالك. تواجه الضغوط والمواقف الصعبة بصبر وحكمة، وتفضل التفكير المنطقي على الانفعال. هذه السمة تجعلك شخصًا موثوقًا ومصدر راحة لمن حولك."
      },
      level2: {
        title: "شخصية متوازنة",
        text: "لديك مستوى طبيعي من الاستجابة للضغوط. تشعر بالانزعاج أو التوتر في بعض الأحيان، لكنك قادر على استعادة هدوئك والتعامل مع المواقف بشكل عام. هذا التوازن صحي ويساعدك على التكيف مع تحديات الحياة اليومية."
      },
      level3: {
        title: "شخصية تميل للعصبية",
        text: "تميل إلى الشعور بالتوتر والغضب بسرعة أكبر من المتوسط. قد تجد أن المواقف الصغيرة تثير إحباطك، وردود أفعالك قد تكون أحيانًا أكثر حدة مما يتطلبه الموقف. قد يكون من المفيد تعلم تقنيات إدارة الغضب والاسترخاء."
      },
      level4: {
        title: "شخصية شديدة العصبية",
        text: "أنت شخص حساس جدًا للضغوط، والغضب هو رد فعل شائع لديك. قد يؤثر هذا على علاقاتك وصحتك العامة. من المهم جدًا البحث عن الأسباب الجذرية لهذه العصبية والعمل على تطوير استراتيجيات صحية للتعامل معها، وربما التفكير في استشارة متخصص."
      }
    };
  
    if (score <= 30) {
      return { level: results.level1.title, description: results.level1.text };
    }
    if (score <= 50) {
      return { level: results.level2.title, description: results.level2.text };
    }
    if (score <= 70) {
      return { level: results.level3.title, description: results.level3.text };
    }
    return { level: results.level4.title, description: results.level4.text };
  };

type TestStage = 'gender' | 'questions' | 'result';

// Helper to shuffle an array
function shuffleArray<T>(array: T[]): T[] {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}


export function AngerTestTool() {
  const [stage, setStage] = useState<TestStage>('gender');
  const [gender, setGender] = useState<Gender | null>(null);
  const [shuffledQuestions, setShuffledQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  
  const isFinished = stage === 'result';
  const totalScore = answers.reduce((sum, score) => sum + score, 0);
  const result = isFinished && gender ? getResult(totalScore, gender) : { level: '', description: '' };

  const startTest = (selectedGender: Gender) => {
    setGender(selectedGender);
    setAnswers([]);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setShuffledQuestions(shuffleArray(questions));
    setStage('questions');
  };

  const handleNextQuestion = () => {
    if (selectedAnswer === null) return;
    
    const newAnswers = [...answers, selectedAnswer];
    setAnswers(newAnswers);
    setSelectedAnswer(null);

    if (currentQuestionIndex < shuffledQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      setStage('result');
    }
  };

  const handleRestart = () => {
    setStage('gender');
    setGender(null);
  };
  
  const progress = isFinished ? 100 : ((currentQuestionIndex) / shuffledQuestions.length) * 100;
  
  const currentQuestion = shuffledQuestions[currentQuestionIndex];
  
  const shuffledAnswers = useMemo(() => {
    if (!currentQuestion) return [];
    return shuffleArray(currentQuestion.answers);
  }, [currentQuestion]);


  const renderContent = () => {
    switch(stage) {
      case 'gender':
        return (
          <div className="text-center space-y-6">
            <h3 className="text-lg font-semibold">قبل أن نبدأ، يرجى تحديد جنسك</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Button variant="outline" className="h-24 text-xl" onClick={() => startTest('male')}>
                <User className="ml-3 h-8 w-8" />
                ذكر
              </Button>
              <Button variant="outline" className="h-24 text-xl" onClick={() => startTest('female')}>
                <UserRound className="ml-3 h-8 w-8" />
                أنثى
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">هذا يساعدنا على تقديم تجربة أفضل في المستقبل.</p>
          </div>
        );
      
      case 'questions':
        if (!gender || !currentQuestion) return null;
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-center">{getQuestionText(currentQuestion, gender)}</h3>
            <RadioGroup
              key={currentQuestionIndex}
              value={selectedAnswer?.toString() || ""}
              onValueChange={(value) => setSelectedAnswer(parseInt(value, 10))}
              className="space-y-3"
            >
              {shuffledAnswers.map((answer, index) => (
                <Label 
                  key={index} 
                  dir="rtl"
                  className="flex items-center justify-between gap-x-3 p-4 border rounded-lg cursor-pointer hover:bg-muted/50 has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary"
                >
                  <span className="flex-1 text-right">{getAnswerText(answer, gender)}</span>
                  <RadioGroupItem 
                    value={String(answer.points)} 
                    id={`q${currentQuestionIndex}-a${index}`} 
                  />
                </Label>
              ))}
            </RadioGroup>
            <Button onClick={handleNextQuestion} disabled={selectedAnswer === null} className="w-full">
              {currentQuestionIndex === questions.length - 1 ? 'عرض النتيجة' : 'التالي'}
            </Button>
          </div>
        );
      case 'result':
        return (
          <div className="text-center space-y-4">
            <h3 className="text-2xl font-bold text-primary">{result.level}</h3>
            <p className="text-muted-foreground leading-relaxed">{result.description}</p>
            <p className="text-xs text-muted-foreground pt-4 border-t">إخلاء مسؤولية: هذا الاختبار هو لأغراض الترفيه والتثقيف الذاتي فقط ولا يمثل تشخيصًا نفسيًا.</p>
            <Button onClick={handleRestart} variant="outline" className="w-full">
                <Repeat className="ml-2 h-4 w-4" />
                أعد الاختبار
            </Button>
          </div>
        );
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
            <Zap className="h-6 w-6 text-primary" />
            اختبار العصبية
        </CardTitle>
        <CardDescription>
            أجب عن الأسئلة التالية بصدق لاكتشاف مستوى العصبية في شخصيتك.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {stage !== 'gender' && (
             <div className="mb-6">
                <Progress value={progress} />
                <p className="text-sm text-muted-foreground mt-2 text-center">
                    {isFinished ? 'اكتمل الاختبار!' : `السؤال ${currentQuestionIndex + 1} من ${shuffledQuestions.length}`}
                </p>
            </div>
        )}
       
        {renderContent()}

      </CardContent>
    </Card>
  );
}
