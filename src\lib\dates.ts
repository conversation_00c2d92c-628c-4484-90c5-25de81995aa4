import { set, getYear, addMonths, subDays, addDays, getDay, startOfDay, differenceInDays } from 'date-fns';

// --- أنواع البيانات ---
export interface NationalEvent {
  id: string;
  name: string;
  nextDate: Date | null;
  isDate?: boolean;
  hijriDate?: string;
  description: string;
}

// --- بيانات ثابتة ---
// تواريخ الأعياد المتوقعة بالتقويم الميلادي لضمان الدقة
// المصدر: تقويم أم القرى المتوقع - تواريخ تقديرية قابلة للتعديل
// ملاحظة: التواريخ الهجرية تعتمد على رؤية الهلال وقد تختلف بيوم أو يومين
const EID_DATES = {
  fitr: [
    new Date('2025-03-30'), // 1 شوال 1446هـ (تقديري)
    new Date('2026-03-20'), // 1 شوال 1447هـ (تقديري)
    new Date('2027-03-09'), // 1 شوال 1448هـ (تقديري)
    new Date('2028-02-26'), // 1 شوال 1449هـ (تقديري)
  ],
  adha: [
    new Date('2025-06-06'), // 10 ذو الحجة 1446هـ (تقديري)
    new Date('2026-05-27'), // 10 ذو الحجة 1447هـ (تقديري)
    new Date('2027-05-16'), // 10 ذو الحجة 1448هـ (تقديري)
    new Date('2028-05-04'), // 10 ذو الحجة 1449هـ (تقديري)
  ],
  arafah: [
    new Date('2025-06-05'), // 9 ذو الحجة 1446هـ (تقديري)
    new Date('2026-05-26'), // 9 ذو الحجة 1447هـ (تقديري)
    new Date('2027-05-15'), // 9 ذو الحجة 1448هـ (تقديري)
    new Date('2028-05-03'), // 9 ذو الحجة 1449هـ (تقديري)
  ],
  ramadan: [
    new Date('2025-02-28'), // 1 رمضان 1446هـ (تقديري)
    new Date('2026-02-18'), // 1 رمضان 1447هـ (تقديري)
    new Date('2027-02-07'), // 1 رمضان 1448هـ (تقديري)
  ]
};

const FOUNDING_DAY = { month: 1, day: 22 }; // 22 فبراير (شهر 1 = فبراير في JavaScript)
const NATIONAL_DAY = { month: 8, day: 23 }; // 23 سبتمبر (شهر 8 = سبتمبر في JavaScript)
const WINTER_START = { month: 11, day: 22 }; // 22 ديسمبر (شهر 11 = ديسمبر في JavaScript)
const SUMMER_END = { month: 8, day: 21 }; // 21 سبتمبر (شهر 8 = سبتمبر في JavaScript) - الاعتدال الخريفي

// التقويم الدراسي للعام 1446-1447هـ (2024-2025م)
const STUDY_CALENDAR_2024_2025 = {
  schoolStart: '2024-08-18',
  longWeekend1: '2024-11-17',
  firstSemesterEnd: '2024-11-08',
  secondSemesterStart: '2024-11-17',
  longWeekend2: '2025-01-02',
  midSemesterBreak: '2025-01-03',
  foundingDayBreakStart: '2025-02-23',
  eidFitrBreakStart: '2025-03-20',
  schoolEnd: '2025-05-29',
};

// التقويم الدراسي للعام 1447-1448هـ (2025-2026م)
const STUDY_CALENDAR_2025_2026 = {
  schoolStart: '2025-08-24', // تاريخ متوقع
  longWeekend1: '2025-11-16', // تاريخ متوقع
  firstSemesterEnd: '2025-11-07', // تاريخ متوقع
  secondSemesterStart: '2025-11-16', // تاريخ متوقع
  longWeekend2: '2026-01-01', // تاريخ متوقع
  midSemesterBreak: '2026-01-02', // تاريخ متوقع
  foundingDayBreakStart: '2026-02-22', // تاريخ متوقع
  eidFitrBreakStart: '2026-03-10', // تاريخ متوقع (تقديري)
  schoolEnd: '2026-05-28', // تاريخ متوقع
};


// --- دوال مساعدة ---

/**
 * دالة عامة لحساب موعد الدفع الشهري مع الأخذ في الاعتبار عطلة نهاية الأسبوع
 * @param dayOfMonth يوم الدفع القياسي في الشهر
 * @returns تاريخ الدفع القادم
 */
function getNextPaymentDate(dayOfMonth: number): Date {
  const now = startOfDay(new Date());
  let paymentDate = set(now, { date: dayOfMonth, hours: 0, minutes: 0, seconds: 0, milliseconds: 0 });

  if (now > paymentDate) {
    paymentDate = addMonths(paymentDate, 1);
  }

  let dayOfWeek = getDay(paymentDate); // 0 = Sunday, 5 = Friday, 6 = Saturday

  if (dayOfWeek === 5) { // إذا كان الجمعة
    paymentDate = subDays(paymentDate, 1); // يقدم إلى الخميس
  } else if (dayOfWeek === 6) { // إذا كان السبت
    paymentDate = addDays(paymentDate, 1); // يؤخر إلى الأحد
  }
  
  return paymentDate;
}

/**
 * دالة لحساب موعد قادم لتاريخ سنوي ثابت
 * @param dateInfo { month, day } (0-indexed month)
 * @returns تاريخ الحدث القادم
 */
function getNextAnnualDate(dateInfo: { month: number; day: number }): Date {
    const now = startOfDay(new Date());
    const currentYear = getYear(now);
    let eventDate = set(now, { year: currentYear, month: dateInfo.month, date: dateInfo.day, hours: 0, minutes: 0, seconds: 0, milliseconds: 0 });

    // إذا كان التاريخ في الماضي (مع هامش يوم واحد للأحداث الجارية)
    if (differenceInDays(now, eventDate) > 1) {
        eventDate = set(eventDate, { year: currentYear + 1 });
    }

    return eventDate;
}

/**
 * دالة للحصول على أقرب تاريخ مستقبلي من قائمة
 * @param dates قائمة التواريخ
 * @returns أقرب تاريخ قادم أو null
 */
function getNextDateFromList(dates: Date[]): Date | null {
    const now = startOfDay(new Date());
    const futureDates = dates.filter(d => differenceInDays(now, d) <= 3).sort((a, b) => a.getTime() - b.getTime());
    if (futureDates.length > 0) {
        return futureDates[0];
    }
    
    // إذا لم يكن هناك تاريخ قادم هذا العام (أو خلال 3 أيام ماضية)، ابحث في السنوات القادمة
    const futureYearsDates = dates.map(d => {
        let futureDate = d;
        while (futureDate < now) {
            futureDate = set(futureDate, { year: getYear(futureDate) + 1 });
        }
        return futureDate;
    }).sort((a,b) => a.getTime() - b.getTime());

    return futureYearsDates.length > 0 ? futureYearsDates[0] : null;
}

function getHijriDate(gregorianDate: Date): string {
    return new Intl.DateTimeFormat('ar-SA-u-ca-islamic-nu-latn', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }).format(gregorianDate);
}


// --- دوال حساب الأحداث ---

export function getEidAlFitr(): NationalEvent {
  const nextDate = getNextDateFromList(EID_DATES.fitr);
  const year = nextDate ? nextDate.getFullYear() : new Date().getFullYear();

  return {
    id: 'eid-fitr',
    name: `عداد عيد الفطر المبارك ${year}`,
    description: `الوقت المتبقي حتى أول أيام عيد الفطر السعيد ${year}.`,
    nextDate,
    isDate: nextDate ? differenceInDays(startOfDay(new Date()), nextDate) >= 0 : false,
    hijriDate: nextDate ? getHijriDate(nextDate) : undefined,
  };
}

export function getEidAlAdha(): NationalEvent {
  const nextDate = getNextDateFromList(EID_DATES.adha);
  const year = nextDate ? nextDate.getFullYear() : new Date().getFullYear();

  return {
    id: 'eid-adha',
    name: `عداد عيد الأضحى المبارك ${year}`,
    description: `الوقت المتبقي حتى أول أيام عيد الأضحى المبارك ${year}.`,
    nextDate,
    isDate: nextDate ? differenceInDays(startOfDay(new Date()), nextDate) >= 0 : false,
    hijriDate: nextDate ? getHijriDate(nextDate) : undefined,
  };
}

export function getArafahDay(): NationalEvent {
    const nextDate = getNextDateFromList(EID_DATES.arafah);
    const year = nextDate ? nextDate.getFullYear() : new Date().getFullYear();

    return {
      id: 'arafah-day',
      name: `عداد يوم عرفة ${year}`,
      description: `الوقت المتبقي حتى يوم عرفة، ركن الحج الأعظم ${year}.`,
      nextDate,
      isDate: nextDate ? differenceInDays(startOfDay(new Date()), nextDate) >= 0 : false,
      hijriDate: nextDate ? getHijriDate(nextDate) : undefined,
    };
}

export function getRamadanCountdown(): NationalEvent {
    const nextDate = getNextDateFromList(EID_DATES.ramadan);
    const year = nextDate ? nextDate.getFullYear() : new Date().getFullYear();

    return {
      id: 'ramadan-countdown',
      name: `عداد رمضان ${year}`,
      description: `الوقت المتبقي حتى بداية شهر رمضان المبارك ${year}.`,
      nextDate,
      isDate: nextDate ? differenceInDays(startOfDay(new Date()), nextDate) >= 0 : false,
      hijriDate: nextDate ? getHijriDate(nextDate) : undefined,
    };
}

export function getCitizenAccount(): NationalEvent {
  const nextDate = getNextPaymentDate(10);
  const monthYear = nextDate ? nextDate.toLocaleDateString('ar-SA-u-nu-latn', { month: 'long', year: 'numeric' }) : '';

  return {
    id: 'citizen-account',
    name: `عداد حساب المواطن ${monthYear}`,
    description: `الوقت المتبقي حتى إيداع دفعة حساب المواطن ${monthYear}.`,
    nextDate,
  };
}

export function getRetirementPension(): NationalEvent {
  const nextDate = getNextPaymentDate(25);
  const monthYear = nextDate ? nextDate.toLocaleDateString('ar-SA-u-nu-latn', { month: 'long', year: 'numeric' }) : '';

  return {
    id: 'retirement-pension',
    name: `عداد راتب التقاعد ${monthYear}`,
    description: `الوقت المتبقي حتى إيداع رواتب المتقاعدين ${monthYear}.`,
    nextDate,
  };
}

export function getHousingSupport(): NationalEvent {
  const nextDate = getNextPaymentDate(24);
  const monthYear = nextDate ? nextDate.toLocaleDateString('ar-SA-u-nu-latn', { month: 'long', year: 'numeric' }) : '';

  return {
    id: 'housing-support',
    name: `عداد الدعم السكني ${monthYear}`,
    description: `الوقت المتبقي حتى إيداع الدعم السكني للمستفيدين ${monthYear}.`,
    nextDate,
  };
}

export function getFoundingDay(): NationalEvent {
  const nextDate = getNextAnnualDate(FOUNDING_DAY);
  const year = nextDate ? nextDate.getFullYear() : new Date().getFullYear();

  return {
    id: 'founding-day',
    name: `عداد يوم التأسيس السعودي ${year}`,
    description: `الوقت المتبقي حتى الاحتفال بيوم التأسيس للمملكة ${year}.`,
    nextDate,
    isDate: nextDate ? differenceInDays(startOfDay(new Date()), nextDate) >= 0 : false,
  };
}

export function getNationalDay(): NationalEvent {
    const nextDate = getNextAnnualDate(NATIONAL_DAY);
    const year = nextDate ? nextDate.getFullYear() : new Date().getFullYear();

    return {
      id: 'saudi-national-day',
      name: `عداد اليوم الوطني السعودي ${year}`,
      description: `الوقت المتبقي حتى الاحتفال باليوم الوطني للمملكة ${year}.`,
      nextDate,
      isDate: nextDate ? differenceInDays(startOfDay(new Date()), nextDate) >= 0 : false,
    };
  }

export function getWinterStart(): NationalEvent {
    const nextDate = getNextAnnualDate(WINTER_START);
    const year = nextDate ? nextDate.getFullYear() : new Date().getFullYear();

    return {
      id: 'winter-start',
      name: `عداد بداية الشتاء ${year}`,
      description: `الوقت المتبقي حتى بداية فصل الشتاء فلكيًا ${year}.`,
      nextDate,
      isDate: nextDate ? differenceInDays(startOfDay(new Date()), nextDate) >= 0 : false,
    };
}

export function getSummerEnd(): NationalEvent {
    const nextDate = getNextAnnualDate(SUMMER_END);
    const year = nextDate ? nextDate.getFullYear() : new Date().getFullYear();

    return {
      id: 'summer-end',
      name: `عداد نهاية الصيف ${year}`,
      description: `الوقت المتبقي حتى نهاية فصل الصيف فلكيًا ${year}.`,
      nextDate,
      isDate: nextDate ? differenceInDays(startOfDay(new Date()), nextDate) >= 0 : false,
    };
}


export function getNextVacation(): NationalEvent {
    const potentialEvents = [
        // العام الدراسي 2024-2025
        { id: 'long-weekend-1-2025', name: 'إجازة نهاية أسبوع مطولة', date: STUDY_CALENDAR_2024_2025.longWeekend1 },
        { id: 'mid-semester-break-2025', name: 'إجازة منتصف الفصل الدراسي الثاني', date: STUDY_CALENDAR_2024_2025.midSemesterBreak },
        { id: 'long-weekend-2-2025', name: 'إجازة نهاية أسبوع مطولة', date: STUDY_CALENDAR_2024_2025.longWeekend2 },
        { id: 'founding-day-break-2025', name: 'بداية إجازة يوم التأسيس', date: STUDY_CALENDAR_2024_2025.foundingDayBreakStart },
        { id: 'eid-al-fitr-break-2025', name: 'بداية إجازة عيد الفطر', date: STUDY_CALENDAR_2024_2025.eidFitrBreakStart },
        { id: 'summer-vacation-2025', name: 'بداية الإجازة الصيفية', date: STUDY_CALENDAR_2024_2025.schoolEnd },

        // العام الدراسي 2025-2026
        { id: 'long-weekend-1-2026', name: 'إجازة نهاية أسبوع مطولة', date: STUDY_CALENDAR_2025_2026.longWeekend1 },
        { id: 'mid-semester-break-2026', name: 'إجازة منتصف الفصل الدراسي الثاني', date: STUDY_CALENDAR_2025_2026.midSemesterBreak },
        { id: 'long-weekend-2-2026', name: 'إجازة نهاية أسبوع مطولة', date: STUDY_CALENDAR_2025_2026.longWeekend2 },
        { id: 'founding-day-break-2026', name: 'بداية إجازة يوم التأسيس', date: STUDY_CALENDAR_2025_2026.foundingDayBreakStart },
        { id: 'eid-al-fitr-break-2026', name: 'بداية إجازة عيد الفطر', date: STUDY_CALENDAR_2025_2026.eidFitrBreakStart },
        { id: 'summer-vacation-2026', name: 'بداية الإجازة الصيفية', date: STUDY_CALENDAR_2025_2026.schoolEnd },
    ];

    const nextVacationDate = getNextDateFromList(potentialEvents.map(p => new Date(p.date)));
    const eventDetails = potentialEvents.find(p => p.date === nextVacationDate?.toISOString().substring(0, 10));

    return {
        id: 'next-vacation',
        name: `عداد ${eventDetails?.name || 'الإجازة القادمة'}`,
        description: 'الوقت المتبقي حتى أقرب إجازة دراسية قادمة.',
        nextDate: nextVacationDate,
        isDate: nextVacationDate ? differenceInDays(startOfDay(new Date()), nextVacationDate) >= 0 : false,
    };
}

export function getNextLongWeekend(): NationalEvent {
    const longWeekends = [
        STUDY_CALENDAR_2024_2025.longWeekend1,
        STUDY_CALENDAR_2024_2025.longWeekend2,
        STUDY_CALENDAR_2025_2026.longWeekend1,
        STUDY_CALENDAR_2025_2026.longWeekend2
    ];
    const nextLongWeekend = getNextDateFromList(longWeekends.map(d => new Date(d)));

    return {
        id: 'next-long-weekend',
        name: 'عداد الإجازة المطولة القادمة',
        description: 'الوقت المتبقي حتى أقرب إجازة نهاية أسبوع مطولة.',
        nextDate: nextLongWeekend,
        isDate: nextLongWeekend ? differenceInDays(startOfDay(new Date()), nextLongWeekend) >= 0 : false,
    };
}

export function getSaudiCalendar(): NationalEvent {
  const schoolStartDates = [
    STUDY_CALENDAR_2024_2025.schoolStart,
    STUDY_CALENDAR_2025_2026.schoolStart
  ];

  const nextDate = getNextDateFromList(schoolStartDates.map(d => new Date(d)));
  const isCurrentYear = nextDate?.toISOString().substring(0, 10) === STUDY_CALENDAR_2024_2025.schoolStart;
  const academicYear = isCurrentYear ? '1446-1447هـ' : '1447-1448هـ';

  return {
    id: 'study-calendar',
    name: 'كم باقي على المدرسة السعودية​',
    description: `موعد بداية العام الدراسي القادم ${academicYear}.`,
    nextDate: nextDate,
    isDate: nextDate ? differenceInDays(startOfDay(new Date()), nextDate) >= 0 : false,
    hijriDate: nextDate ? `بداية الدراسة: ${getHijriDate(nextDate)}` : undefined
  };
}

export function getSalaryCountdown(): NationalEvent {
  const nextDate = getNextPaymentDate(27);
  const monthYear = nextDate ? nextDate.toLocaleDateString('ar-SA-u-nu-latn', { month: 'long', year: 'numeric' }) : '';

  return {
    id: 'saudi-salary',
    name: `كم باقي على الراتب؟ ${monthYear}`,
    description: `عداد تنازلي لموعد صرف رواتب الموظفين في السعودية ${monthYear} (يوم 27).`,
    nextDate,
  };
}
