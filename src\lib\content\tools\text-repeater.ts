const content = {
  seoDescription: `
      <h2>أداة تكرار النص: كرر أي نص بسهولة وكفاءة</h2>
      <p>في بعض الأحيان، قد تحتاج إلى تكرار كلمة أو جملة أو حتى فقرة كاملة لعدد معين من المرات. قد تكون هذه المهمة مملة وتستغرق وقتًا طويلاً إذا تم القيام بها يدويًا. تقدم <strong>أداة تكرار النص</strong> حلاً بسيطًا وقويًا لهذه المشكلة، حيث تتيح لك إنشاء نسخ متعددة من أي نص تريده بضغطة زر. مع خيارات متقدمة للتحكم في الفواصل والترقيم، تصبح هذه الأداة مفيدة لمجموعة متنوعة من الأغراض، من اختبار البرامج إلى إنشاء المحتوى، أو حتى للمرح.</p>

      <h3>لماذا قد تحتاج إلى تكرار النص؟</h3>
      <p>قد تبدو مهمة تكرار النص بسيطة، ولكن لها تطبيقات عملية مفيدة:</p>
      <ul>
        <li><strong>للمطورين والمختبرين:</strong> يمكن استخدام الأداة لإنشاء كميات كبيرة من البيانات الوهمية (dummy data) بسرعة لاختبار أداء التطبيقات، أو ملء قواعد البيانات، أو اختبار كيفية عرض الواجهات لكميات كبيرة من المحتوى.</li>
        <li><strong>لمعلمي اللغة:</strong> يمكن استخدامها لإنشاء أوراق عمل تحتوي على جمل أو كلمات مكررة لممارسة الكتابة أو الإملاء.</li>
        <li><strong>للتسويق الرقمي:</strong> لإنشاء قائمة من الوسوم (hashtags) أو الكلمات المفتاحية بنمط معين.</li>
        <li><strong>للتصميم الجرافيكي:</strong> لإنشاء أنماط نصية (text patterns) لاستخدامها كخلفيات أو عناصر تصميم.</li>
        <li><strong>للاستخدام الشخصي:</strong> لإنشاء رسائل متكررة أو ببساطة للمرح والتسلية.</li>
      </ul>

      <h3>خيارات متقدمة لتخصيص الإخراج</h3>
      <p>تتجاوز أداتنا مجرد النسخ واللصق المتكرر. يمكنك تخصيص النص الناتج بدقة باستخدام الخيارات التالية:</p>
      <ul>
        <li><strong>عدد مرات التكرار:</strong> حدد بالضبط عدد المرات التي تريد تكرار النص فيها، حتى 10,000 مرة.</li>
        <li><strong>الفاصل بين التكرارات:</strong> اختر كيف تريد فصل كل نسخة من النص عن التي تليها:
          <ul>
            <li><strong>سطر جديد:</strong> كل تكرار يظهر في سطر منفصل.</li>
            <li><strong>مسافة:</strong> يتم فصل التكرارات بمسافة واحدة.</li>
            <li><strong>فاصلة:</strong> يتم فصل التكرارات بفاصلة ثم مسافة (، ).</li>
            <li><strong>فاصل مخصص:</strong> أدخل أي حرف أو سلسلة من الأحرف لاستخدامها كفاصل (مثل --- أو | ).</li>
          </ul>
        </li>
        <li><strong>إضافة ترقيم:</strong> يمكنك اختيار إضافة ترقيم تسلسلي تلقائي قبل كل تكرار (مثل "1. نص"، "2. نص"، ...)، مما يجعلها مثالية لإنشاء القوائم المرقمة.</li>
      </ul>

      <h3>كيفية استخدام أداة تكرار النص</h3>
      <p>استخدام الأداة سهل للغاية ولا يتطلب أي خبرة فنية:</p>
      <ol>
        <li><strong>أدخل النص:</strong> في الحقل الأول، اكتب أو الصق النص الذي تريد تكراره.</li>
        <li><strong>حدد عدد التكرارات:</strong> اضبط عدد المرات التي تريد تكرار النص فيها.</li>
        <li><strong>اختر الفاصل:</strong> حدد نوع الفاصل من القائمة المنسدلة. إذا اخترت "فاصل مخصص"، فأدخل الفاصل الذي تريده.</li>
        <li><strong>حدد خيار الترقيم:</strong> قم بتفعيل خيار "إضافة ترقيم" إذا كنت تريد قائمة مرقمة.</li>
        <li><strong>انقر على "تكرار النص":</strong> ستقوم الأداة بإنشاء النص المكرر على الفور وعرضه في مربع النتائج.</li>
        <li><strong>نسخ النتيجة:</strong> استخدم زر "نسخ" لنقل النص الناتج بسهولة إلى الحافظة الخاصة بك لاستخدامه في أي مكان آخر.</li>
      </ol>
      <p>هذه الأداة البسيطة والفعالة توفر الوقت والجهد وتجعل المهام المتكررة أمرًا من الماضي، مما يتيح لك التركيز على جوانب عملك الأكثر إبداعًا.</p>
    `,
  faq: [
    { question: 'ما هو الحد الأقصى لعدد التكرارات المسموح به؟', answer: 'يمكنك تكرار النص حتى 10,000 مرة. تم وضع هذا الحد لمنع إنشاء نصوص كبيرة جدًا قد تؤدي إلى إبطاء أو تعليق متصفحك.' },
    { question: 'هل يمكنني استخدام رموز تعبيرية (emojis) في النص المراد تكراره؟', answer: 'نعم، يمكنك إدخال أي نوع من النصوص، بما في ذلك الحروف، الأرقام، الرموز، والرموز التعبيرية، وستقوم الأداة بتكرارها كما هي.' },
    { question: 'ماذا يحدث إذا تركت حقل "الفاصل المخصص" فارغًا؟', answer: 'إذا اخترت "فاصل مخصص" وتركت الحقل فارغًا، فسيتم لصق التكرارات ببعضها البعض دون أي فاصل بينها.' },
    { question: 'هل يتم تخزين النصوص التي أقوم بتكرارها؟', answer: 'لا، نحن نحترم خصوصيتك. جميع العمليات تتم مباشرة في متصفحك، ولا يتم إرسال أو تخزين أي من بياناتك على خوادمنا.' },
    { question: 'كيف يعمل خيار "إضافة ترقيم"؟', answer: 'عند تفعيل هذا الخيار، ستقوم الأداة بإضافة رقم تسلسلي يتبعه نقطة ثم مسافة (مثل "1. ") قبل كل نسخة من النص المكرر، مما ينتج عنه قائمة مرقمة بشكل مثالي.' }
  ]
};
export default content;
