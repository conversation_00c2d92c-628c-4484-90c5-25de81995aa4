
'use client';

import { useState } from 'react';
import { useForm, Control } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { differenceInYears, subYears, differenceInMonths, subMonths, differenceInDays } from 'date-fns';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  day: requiredNumber('اليوم مطلوب'),
  month: requiredNumber('الشهر مطلوب'),
  year: requiredNumber('السنة مطلوبة'),
}).refine(data => {
    try {
        const date = new Date(data.year, data.month - 1, data.day);
        return date.getFullYear() === data.year && date.getMonth() === data.month - 1 && date.getDate() === data.day;
    } catch {
        return false;
    }
}, {
    message: 'التاريخ المدخل غير صالح.',
    path: ['day'],
});

type FormValues = z.infer<typeof FormSchema>;

interface Result {
  years: number;
  months: number;
  days: number;
}

const DateFields = ({ control }: { control: Control<FormValues> }) => {
  const years = Array.from({ length: 121 }, (_, i) => new Date().getFullYear() - i);
  const months = Array.from({ length: 12 }, (_, i) => i + 1);
  const days = Array.from({ length: 31 }, (_, i) => i + 1);

  return (
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <FormField
            control={control}
            name="day"
            render={({ field }) => (
                <FormItem>
                    <Select onValueChange={field.onChange} value={field.value?.toString()}>
                        <FormControl><SelectTrigger><SelectValue placeholder="اليوم" /></SelectTrigger></FormControl>
                        <SelectContent>
                            {days.map(day => <SelectItem key={day} value={String(day)}>{day}</SelectItem>)}
                        </SelectContent>
                    </Select>
                    <FormMessage />
                </FormItem>
            )}
        />
        <FormField
            control={control}
            name="month"
            render={({ field }) => (
                <FormItem>
                    <Select onValueChange={field.onChange} value={field.value?.toString()}>
                        <FormControl><SelectTrigger><SelectValue placeholder="الشهر" /></SelectTrigger></FormControl>
                        <SelectContent>
                            {months.map(month => <SelectItem key={month} value={String(month)}>{month}</SelectItem>)}
                        </SelectContent>
                    </Select>
                    <FormMessage />
                </FormItem>
            )}
        />
        <FormField
            control={control}
            name="year"
            render={({ field }) => (
                <FormItem>
                    <Select onValueChange={field.onChange} value={field.value?.toString()}>
                        <FormControl><SelectTrigger><SelectValue placeholder="السنة" /></SelectTrigger></FormControl>
                        <SelectContent>
                            {years.map(year => <SelectItem key={year} value={String(year)}>{year}</SelectItem>)}
                        </SelectContent>
                    </Select>
                    <FormMessage />
                </FormItem>
            )}
        />
    </div>
  );
};


export function AgeCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      day: undefined,
      month: undefined,
      year: undefined,
    }
  });

  function onSubmit(data: FormValues) {
    const today = new Date();
    const birthDate = new Date(data.year, data.month - 1, data.day);

    if (birthDate > today) {
      form.setError('year', { message: 'تاريخ الميلاد لا يمكن أن يكون في المستقبل' });
      return;
    }

    const years = differenceInYears(today, birthDate);
    const pastYearDate = subYears(today, years);
    const months = differenceInMonths(pastYearDate, birthDate);
    const pastMonthDate = subMonths(pastYearDate, months);
    const days = differenceInDays(pastMonthDate, birthDate);

    setResult({ years, months, days });
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>حساب العمر</CardTitle>
        <CardDescription>أدخل تاريخ ميلادك لحساب عمرك بدقة.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <DateFields control={form.control} />
            <Button type="submit" className="w-full">
              احسب العمر
            </Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-4 text-center">عمرك هو</h3>
            <div className="grid grid-cols-3 gap-4">
              <div className="p-4 bg-primary/10 rounded-lg">
                <p className="text-4xl font-bold font-mono text-primary">{result.years}</p>
                <p className="text-sm text-muted-foreground">سنوات</p>
              </div>
              <div className="p-4 bg-primary/10 rounded-lg">
                <p className="text-4xl font-bold font-mono text-primary">{result.months}</p>
                <p className="text-sm text-muted-foreground">أشهر</p>
              </div>
              <div className="p-4 bg-primary/10 rounded-lg">
                <p className="text-4xl font-bold font-mono text-primary">{result.days}</p>
                <p className="text-sm text-muted-foreground">أيام</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
