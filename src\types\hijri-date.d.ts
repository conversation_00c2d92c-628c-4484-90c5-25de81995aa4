declare module 'hijri-date' {
  /**
   * HijriDate class for working with Hijri (Islamic) calendar dates
   * Compatible with the built-in Date class but for Hijri calendar
   */
  class HijriDate {
    /**
     * Creates a new HijriDate instance
     * @param year - Hijri year (optional)
     * @param month - Hijri month (1-12, optional)
     * @param day - Hijri day (1-30, optional)
     */
    constructor();
    constructor(date: Date);
    constructor(year: number);
    constructor(year: number, month: number);
    constructor(year: number, month: number, day: number);

    /**
     * Gets the Hijri year
     * @returns The Hijri year
     */
    getFullYear(): number;

    /**
     * Gets the Hijri month (1-12)
     * @returns The Hijri month
     */
    getMonth(): number;

    /**
     * Gets the Hijri day of the month (1-30)
     * @returns The Hijri day
     */
    getDate(): number;

    /**
     * Gets the day of the week (0-6, where 0 is Sunday)
     * @returns The day of the week
     */
    getDay(): number;

    /**
     * Sets the Hijri year
     * @param year - The Hijri year to set
     */
    setFullYear(year: number): void;

    /**
     * Sets the Hijri month
     * @param month - The Hijri month to set (1-12)
     */
    setMonth(month: number): void;

    /**
     * Sets the Hijri day
     * @param day - The Hijri day to set (1-30)
     */
    setDate(day: number): void;

    /**
     * Converts the Hijri date to a Gregorian Date object
     * @returns The equivalent Gregorian Date
     */
    toGregorian(): Date;

    /**
     * Returns a string representation of the Hijri date
     * @returns String representation of the date
     */
    toString(): string;

    /**
     * Returns the primitive value of the Hijri date
     * @returns The primitive value
     */
    valueOf(): number;
  }

  export = HijriDate;
}
