'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { toolCategories, Tool } from '@/lib/tools';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, ExternalLink } from 'lucide-react';

interface RelatedToolsProps {
  currentTool: Tool;
  maxSuggestions?: number;
}

export function RelatedTools({ currentTool, maxSuggestions = 6 }: RelatedToolsProps) {
  const allTools = toolCategories.flatMap(c => c.tools);
  const currentCategory = toolCategories.find(cat => 
    cat.tools.some(t => t.slug === currentTool.slug)
  );

  // Get related tools based on multiple criteria
  const getRelatedTools = (): Tool[] => {
    const relatedTools: Tool[] = [];
    const addedSlugs = new Set<string>([currentTool.slug]);

    // 1. Manually specified related tools (highest priority)
    if (currentTool.relatedSlugs) {
      currentTool.relatedSlugs.forEach(slug => {
        const tool = allTools.find(t => t.slug === slug);
        if (tool && tool.component && !addedSlugs.has(slug)) {
          relatedTools.push(tool);
          addedSlugs.add(slug);
        }
      });
    }

    // 2. Tools from the same category
    if (currentCategory && relatedTools.length < maxSuggestions) {
      const categoryTools = currentCategory.tools
        .filter(t => t.component && !addedSlugs.has(t.slug))
        .slice(0, maxSuggestions - relatedTools.length);
      
      relatedTools.push(...categoryTools);
      categoryTools.forEach(t => addedSlugs.add(t.slug));
    }

    // 3. Popular tools if we still need more
    if (relatedTools.length < maxSuggestions) {
      const popularSlugs = [
        'zakat-calculator',
        'date-converter',
        'age-calculator',
        'currency-converter',
        'percentage-calculator',
        'qr-code-generator'
      ];

      popularSlugs.forEach(slug => {
        if (relatedTools.length >= maxSuggestions) return;
        const tool = allTools.find(t => t.slug === slug);
        if (tool && tool.component && !addedSlugs.has(slug)) {
          relatedTools.push(tool);
          addedSlugs.add(slug);
        }
      });
    }

    return relatedTools.slice(0, maxSuggestions);
  };

  const relatedTools = getRelatedTools();

  if (relatedTools.length === 0) return null;

  return (
    <section className="mt-12">
      <h2 className="text-2xl font-headline font-bold text-center mb-6">
        أدوات ذات صلة
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {relatedTools.map(tool => (
          <Link key={tool.slug} href={tool.path} className="group">
            <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-primary">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {tool.icon && (
                      <div className="p-2 rounded-full bg-primary/10 text-primary">
                        <tool.icon className="w-4 h-4" />
                      </div>
                    )}
                    <CardTitle className="text-sm font-medium group-hover:text-primary transition-colors">
                      {tool.name}
                    </CardTitle>
                  </div>
                  <ArrowLeft className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity text-primary" />
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-muted-foreground line-clamp-2">
                  {tool.description}
                </p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </section>
  );
}

interface CategoryNavigationProps {
  currentCategory?: string;
}

export function CategoryNavigation({ currentCategory }: CategoryNavigationProps) {
  return (
    <nav className="mb-8" aria-label="تصفح الأقسام">
      <h2 className="text-lg font-semibold mb-4">تصفح حسب القسم</h2>
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
        {toolCategories.map(category => (
          <Link
            key={category.slug}
            href={`/categories/${category.slug}`}
            className={`
              flex items-center gap-2 p-3 rounded-lg border transition-all duration-200
              ${currentCategory === category.slug 
                ? 'bg-primary text-primary-foreground border-primary' 
                : 'hover:bg-muted hover:border-primary/50'
              }
            `}
          >
            <category.icon className="w-4 h-4" />
            <span className="text-sm font-medium">{category.name}</span>
          </Link>
        ))}
      </div>
    </nav>
  );
}

interface BreadcrumbNavigationProps {
  items: Array<{
    name: string;
    href?: string;
  }>;
}

export function BreadcrumbNavigation({ items }: BreadcrumbNavigationProps) {
  return (
    <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-6 rtl:space-x-reverse" aria-label="مسار التنقل">
      {items.map((item, index) => (
        <div key={index} className="flex items-center">
          {index > 0 && (
            <span className="mx-2 text-muted-foreground/60">‹</span>
          )}
          {item.href ? (
            <Link 
              href={item.href} 
              className="hover:text-primary transition-colors"
            >
              {item.name}
            </Link>
          ) : (
            <span className="text-foreground font-medium">{item.name}</span>
          )}
        </div>
      ))}
    </nav>
  );
}

interface PopularToolsProps {
  excludeSlug?: string;
  maxItems?: number;
}

export function PopularTools({ excludeSlug, maxItems = 8 }: PopularToolsProps) {
  const popularSlugs = [
    'zakat-calculator',
    'date-converter',
    'age-calculator',
    'currency-converter',
    'percentage-calculator',
    'qr-code-generator',
    'word-count',
    'bmi-calculator',
    'vat-calculator',
    'discount-calculator'
  ];

  const allTools = toolCategories.flatMap(c => c.tools);
  const popularTools = popularSlugs
    .map(slug => allTools.find(t => t.slug === slug))
    .filter((tool): tool is Tool => 
      tool !== undefined && 
      tool.component !== undefined && 
      tool.slug !== excludeSlug
    )
    .slice(0, maxItems);

  return (
    <section className="mt-12">
      <h2 className="text-2xl font-headline font-bold text-center mb-6">
        الأدوات الأكثر استخداماً
      </h2>
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
        {popularTools.map(tool => (
          <Link key={tool.slug} href={tool.path} className="group">
            <Card className="h-full transition-all duration-300 hover:shadow-md hover:border-primary">
              <CardContent className="p-4">
                <div className="flex items-center gap-3 mb-2">
                  {tool.icon && (
                    <div className="p-1.5 rounded-full bg-primary/10 text-primary">
                      <tool.icon className="w-3 h-3" />
                    </div>
                  )}
                  <h3 className="text-sm font-medium group-hover:text-primary transition-colors line-clamp-1">
                    {tool.name}
                  </h3>
                </div>
                <p className="text-xs text-muted-foreground line-clamp-2">
                  {tool.description}
                </p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </section>
  );
}

interface ToolCategoryLinksProps {
  category: {
    name: string;
    slug: string;
    tools: Tool[];
  };
}

export function ToolCategoryLinks({ category }: ToolCategoryLinksProps) {
  const availableTools = category.tools.filter(tool => tool.component);

  return (
    <div className="mt-8">
      <h3 className="text-lg font-semibold mb-4">
        جميع أدوات {category.name}
      </h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
        {availableTools.map(tool => (
          <Link
            key={tool.slug}
            href={tool.path}
            className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted hover:border-primary/50 transition-all duration-200 group"
          >
            <div className="flex items-center gap-3">
              {tool.icon && (
                <tool.icon className="w-4 h-4 text-primary" />
              )}
              <span className="text-sm font-medium">{tool.name}</span>
            </div>
            <ExternalLink className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity text-muted-foreground" />
          </Link>
        ))}
      </div>
    </div>
  );
}

// Hook for tracking internal link clicks for analytics
export function useInternalLinkTracking() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const trackInternalLink = (href: string, linkText: string) => {
      // Send to Google Analytics if available
      if ((window as any).gtag) {
        (window as any).gtag('event', 'internal_link_click', {
          event_category: 'Navigation',
          event_label: href,
          link_text: linkText,
          value: 1
        });
      }

      // Log for development
      if (process.env.NODE_ENV === 'development') {
        console.log('[Internal Link Tracking]', { href, linkText });
      }
    };

    // Add click tracking to internal links
    const handleClick = (event: Event) => {
      const target = event.target as HTMLElement;
      const link = target.closest('a[href^="/"]') as HTMLAnchorElement;

      if (link) {
        trackInternalLink(link.href, link.textContent || '');
      }
    };

    document.addEventListener('click', handleClick);

    return () => {
      document.removeEventListener('click', handleClick);
    };
  }, []);
}
