
'use client';

import { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { PlusCircle, Trash2, Box } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const packageSchema = z.object({
  length: requiredNumber().positive("يجب أن يكون الطول رقمًا موجبًا."),
  width: requiredNumber().positive("يجب أن يكون العرض رقمًا موجبًا."),
  height: requiredNumber().positive("يجب أن يكون الارتفاع رقمًا موجبًا."),
  quantity: requiredNumber().int().positive("يجب أن تكون الكمية رقمًا صحيحًا موجبًا."),
});

const formSchema = z.object({
  unit: z.enum(['cm', 'm', 'in']).default('cm'),
  packages: z.array(packageSchema),
});

type Package = z.infer<typeof packageSchema>;

export function CbmCalculatorTool() {
  const [totalCBM, setTotalCBM] = useState<number>(0);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      unit: 'cm',
      packages: [{ length: 100, width: 100, height: 100, quantity: 1 }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'packages',
  });

  const calculateCBM = (pkg: Package, unit: 'cm' | 'm' | 'in') => {
    let { length, width, height, quantity } = pkg;
    
    // Convert all to meters
    if (unit === 'cm') {
      length /= 100;
      width /= 100;
      height /= 100;
    } else if (unit === 'in') {
      length *= 0.0254;
      width *= 0.0254;
      height *= 0.0254;
    }
    
    return length * width * height * quantity;
  };
  
  const onSubmit = (data: z.infer<typeof formSchema>) => {
    const total = data.packages.reduce((acc, pkg) => acc + calculateCBM(pkg, data.unit), 0);
    setTotalCBM(total);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حساب CBM للشحن</CardTitle>
        <CardDescription>احسب حجم شحناتك بالمتر المكعب (CBM).</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="unit"
              render={({ field }) => (
                <FormItem className="w-full max-w-xs">
                  <FormLabel>وحدة القياس</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl><SelectTrigger><SelectValue /></SelectTrigger></FormControl>
                    <SelectContent>
                      <SelectItem value="cm">سنتيمتر (cm)</SelectItem>
                      <SelectItem value="m">متر (m)</SelectItem>
                      <SelectItem value="in">بوصة (in)</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>الطول</TableHead>
                            <TableHead>العرض</TableHead>
                            <TableHead>الارتفاع</TableHead>
                            <TableHead>الكمية</TableHead>
                            <TableHead className="w-[50px]"></TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {fields.map((field, index) => (
                            <TableRow key={field.id}>
                                <TableCell><Input type="number" {...form.register(`packages.${index}.length`)} /></TableCell>
                                <TableCell><Input type="number" {...form.register(`packages.${index}.width`)} /></TableCell>
                                <TableCell><Input type="number" {...form.register(`packages.${index}.height`)} /></TableCell>
                                <TableCell><Input type="number" {...form.register(`packages.${index}.quantity`)} /></TableCell>
                                <TableCell>
                                    <Button type="button" variant="ghost" size="icon" onClick={() => remove(index)}>
                                        <Trash2 className="h-4 w-4 text-destructive" />
                                    </Button>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            <Button type="button" variant="outline" onClick={() => append({ length: 0, width: 0, height: 0, quantity: 1 })}>
                <PlusCircle className="ml-2 h-4 w-4" />
                إضافة طرد
            </Button>
            
            <div className="flex items-center justify-between pt-4 border-t">
                 <Button type="submit" className="w-full max-w-sm">
                    <Box className="ml-2 h-4 w-4" />
                    احسب الحجم الإجمالي
                </Button>
                {totalCBM > 0 && (
                     <div className="text-center">
                        <p className="text-sm text-muted-foreground">الحجم الإجمالي (CBM)</p>
                        <p className="text-3xl font-bold font-mono text-primary">{totalCBM.toFixed(4)}</p>
                    </div>
                )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
