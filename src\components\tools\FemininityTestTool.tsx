
'use client';

import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Repeat, Heart, Sparkles } from 'lucide-react';

type Answer = { text: string; points: number };
type Question = { text: string; answers: Answer[] };

const questions: Question[] = [
  // Empathy & Nurturing
  {
    text: "عندما يكون صديق لك منزعجًا، ما هو رد فعلك الطبيعي؟",
    answers: [
      { text: "أجلس معه وأستمع له بصبر، وأحاول أن أجعله يشعر بأنه مسموع ومفهوم.", points: 4 },
      { text: "أقدم له نصائح عملية وحلولًا مباشرة لمشكلته.", points: 3 },
      { text: "أحاول تشتيت انتباهه بأمور مضحكة أو ممتعة.", points: 2 },
      { text: "أفضل منحه مساحته الخاصة ليتعامل مع مشاعره بمفرده.", points: 1 },
    ],
  },
  {
    text: "كيف تعبرين عن اهتمامك بالآخرين في حياتك اليومية؟",
    answers: [
      { text: "من خلال تذكر التفاصيل الصغيرة عنهم، والسؤال عن أحوالهم، وتقديم لفتات لطيفة.", points: 4 },
      { text: "من خلال تقديم الدعم والمساعدة عندما يطلبون ذلك مني.", points: 3 },
      { text: "أعبر عن اهتمامي بشكل مباشر وصريح في المناسبات.", points: 2 },
      { text: "أفترض أنهم يعرفون أنني أهتم بهم دون الحاجة للتعبير المستمر.", points: 1 },
    ],
  },
  // Intuition & Creativity
  {
    text: "عند اتخاذ قرار مهم، على ماذا تعتمدين بشكل أكبر؟",
    answers: [
      { text: "أوازن بين حدسي ومشاعري الداخلية والتحليل المنطقي للحقائق.", points: 4 },
      { text: "أعتمد بشكل أساسي على التحليل المنطقي والبيانات المتاحة.", points: 3 },
      { text: "أتبع حدسي ومشاعري الأولية في الغالب.", points: 2 },
      { text: "أطلب آراء الآخرين وأتبع النصيحة التي تبدو الأكثر إقناعًا.", points: 1 },
    ],
  },
  {
    text: "كيف تعبرين عن إبداعك؟",
    answers: [
      { text: "أجد طرقًا إبداعية في كل شيء تقريبًا، من طريقة لبسي إلى تزيين منزلي أو حتى في حل المشكلات.", points: 4 },
      { text: "أستمتع بممارسة هواية إبداعية محددة مثل الرسم أو الكتابة أو الطهي.", points: 3 },
      { text: "أقدر الفن والإبداع ولكني لا أعتبر نفسي شخصًا مبدعًا بشكل خاص.", points: 2 },
      { text: "أفضل التركيز على المهام العملية والمنطقية.", points: 1 },
    ],
  },
  // Resilience & Soft Strength
  {
    text: "عندما تواجهين انتقادًا غير عادل، كيف تتعاملين معه؟",
    answers: [
      { text: "أعبر عن رأيي بهدوء وثقة، مع الحفاظ على احترامي لنفسي وللآخر.", points: 4 },
      { text: "أتجاهل النقد إذا شعرت أنه لا يستحق طاقتي.", points: 3 },
      { text: "أشعر بالأذى وقد أدخل في جدال للدفاع عن نفسي.", points: 2 },
      { text: "أتأثر بشدة وأفكر في النقد لفترة طويلة.", points: 1 },
    ],
  },
  {
    text: "ماذا تمثل لكِ القوة؟",
    answers: [
      { text: "القدرة على أن تكوني لطيفة ومتعاطفة حتى في الأوقات الصعبة (القوة الناعمة).", points: 4 },
      { text: "الاستقلال والقدرة على الاعتماد على الذات بشكل كامل.", points: 3 },
      { text: "القدرة على السيطرة على المواقف وفرض رأيك عند الحاجة.", points: 2 },
      { text: "عدم إظهار أي ضعف أو مشاعر سلبية للآخرين.", points: 1 },
    ],
  },
  // Collaboration & Communication
  {
    text: "في مشروع جماعي، ما هو الدور الذي تفضلينه؟",
    answers: [
      { text: "دور المنسق الذي يجمع الفريق ويضمن أن صوت الجميع مسموع.", points: 4 },
      { text: "دور المنفذ الذي يركز على إنجاز المهام الموكلة إليه بكفاءة.", points: 3 },
      { text: "دور القائد الذي يضع الرؤية ويوزع المهام.", points: 2 },
      { text: "أفضل العمل بشكل مستقل ضمن الفريق.", points: 1 },
    ],
  },
  {
    text: "كيف تصفين أسلوبك في التواصل؟",
    answers: [
      { text: "أميل إلى الاستماع أكثر من التحدث، وأختار كلماتي بعناية.", points: 4 },
      { text: "مباشرة وصريحة، أقول ما أعنيه بوضوح.", points: 3 },
      { text: "ثرثارة واجتماعية، أحب المشاركة في الأحاديث.", points: 2 },
      { text: "متحفظة، لا أتحدث كثيرًا إلا إذا كان الأمر ضروريًا.", points: 1 },
    ],
  },
  // Self-Care & Aesthetics
  {
    text: "ما هو مفهومك للعناية بالنفس؟",
    answers: [
      { text: "هو وقت مخصص لتغذية روحي وجسدي، مثل أخذ حمام دافئ، أو القراءة، أو ممارسة التأمل.", points: 4 },
      { text: "الحفاظ على صحتي من خلال الرياضة والأكل الصحي.", points: 3 },
      { text: "الحصول على قسط كافٍ من النوم والراحة.", points: 2 },
      { text: "لا أخصص وقتًا محددًا لذلك، بل أعتني بنفسي عند الحاجة.", points: 1 },
    ],
  },
  {
    text: "إلى أي مدى تهتمين بجمال وتناغم البيئة المحيطة بك (منزلك، مكتبك)؟",
    answers: [
      { text: "بشكل كبير، أحب أن أكون محاطة بأشياء جميلة ومنظمة، فهذا يؤثر على مزاجي.", points: 4 },
      { text: "أهتم بالنظافة والترتيب بشكل أساسي.", points: 3 },
      { text: "أهتم به إلى حد ما، لكني لا أقضي وقتًا طويلاً في التزيين.", points: 2 },
      { text: "لا أهتم كثيرًا، الأهم هو أن تكون الأشياء عملية.", points: 1 },
    ],
  },
];

const getResult = (score: number) => {
    const results = {
      level1: {
        title: "روح متوازنة وقوية",
        text: "لديكِ توازن رائع بين القوة الناعمة والصلابة العملية. أنتِ تعرفين متى تكونين متعاطفة وداعمة، ومتى تكونين حازمة ومنطقية. هذه القدرة على التكيف تجعلكِ شخصية متكاملة وقادرة على التعامل مع مختلف جوانب الحياة بمرونة وحكمة."
      },
      level2: {
        title: "طاقة العطاء والحدس",
        text: "تتمتعين بحدس قوي وطاقة عطاء كبيرة. أنتِ مصدر للدعم العاطفي لمن حولك، وتجدين الجمال في التفاصيل الصغيرة. أنوثتك تكمن في قدرتك على الشعور العميق والتواصل الروحي مع الآخرين. استمري في الثقة بقلبك."
      },
      level3: {
        title: "الأنوثة المبدعة والحرة",
        text: "تتجلى أنوثتك في روحك الحرة وقدرتك على الإبداع. أنتِ لا تخافين من التعبير عن نفسك بطرق فريدة، سواء كان ذلك من خلال الفن، أو الأفكار، أو أسلوب حياتك. أنتِ مصدر إلهام للآخرين ليكونوا على طبيعتهم."
      },
      level4: {
        title: "القوة الهادئة والحكيمة",
        text: "أنوثتك هي أنوثة القوة الهادئة. أنتِ تتمتعين بصلابة داخلية ورؤية واضحة. قد لا تكونين الأكثر تعبيرًا عن مشاعرك، لكن أفعالك تتحدث بصوت أعلى. قوتك تكمن في ثباتك وقدرتك على أن تكوني صخرة يعتمد عليها الآخرون."
      }
    };
  
    if (score <= 18) {
      return { level: results.level1.title, description: results.level1.text };
    }
    if (score <= 26) {
      return { level: results.level2.title, description: results.level2.text };
    }
    if (score <= 34) {
      return { level: results.level3.title, description: results.level3.text };
    }
    return { level: results.level4.title, description: results.level4.text };
};

type TestStage = 'questions' | 'result';

function shuffleArray<T>(array: T[]): T[] {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

export function FemininityTestTool() {
  const [stage, setStage] = useState<TestStage>('questions');
  const [shuffledQuestions, setShuffledQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  
  useEffect(() => {
    setShuffledQuestions(shuffleArray(questions));
  }, []);

  const isFinished = stage === 'result';
  const totalScore = answers.reduce((sum, score) => sum + score, 0);
  const result = isFinished ? getResult(totalScore) : { level: '', description: '' };

  const startTest = () => {
    setAnswers([]);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setShuffledQuestions(shuffleArray(questions));
    setStage('questions');
  };

  const handleNextQuestion = () => {
    if (selectedAnswer === null) return;
    
    const newAnswers = [...answers, selectedAnswer];
    setAnswers(newAnswers);
    setSelectedAnswer(null);

    if (currentQuestionIndex < shuffledQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      setStage('result');
    }
  };
  
  const progress = isFinished ? 100 : ((currentQuestionIndex) / shuffledQuestions.length) * 100;
  
  const currentQuestion = shuffledQuestions[currentQuestionIndex];
  
  const shuffledAnswers = useMemo(() => {
    if (!currentQuestion) return [];
    return shuffleArray(currentQuestion.answers);
  }, [currentQuestion]);


  const renderContent = () => {
    switch(stage) {
      case 'questions':
        if (!currentQuestion) return null;
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-center">{currentQuestion.text}</h3>
            <RadioGroup
              key={currentQuestionIndex}
              value={selectedAnswer?.toString() || ""}
              onValueChange={(value) => setSelectedAnswer(parseInt(value, 10))}
              className="space-y-3"
            >
              {shuffledAnswers.map((answer, index) => (
                <Label 
                  key={index} 
                  dir="rtl"
                  className="flex items-center gap-x-3 p-4 border rounded-lg cursor-pointer hover:bg-muted/50 has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary"
                >
                  
                  <RadioGroupItem 
                    value={String(answer.points)} 
                    id={`q${currentQuestionIndex}-a${index}`} 
                  />
                  <span className="flex-1">{answer.text}</span>
                </Label>
                
              ))}
            </RadioGroup>
            <Button onClick={handleNextQuestion} disabled={selectedAnswer === null} className="w-full">
              {currentQuestionIndex === questions.length - 1 ? 'عرض النتيجة' : 'التالي'}
            </Button>
          </div>
        );
      case 'result':
        return (
          <div className="text-center space-y-4">
            <h3 className="text-2xl font-bold text-primary">{result.level}</h3>
            <p className="text-muted-foreground leading-relaxed">{result.description}</p>
            <p className="text-xs text-muted-foreground pt-4 border-t">إخلاء مسؤولية: هذا الاختبار هو لأغراض الترفيه والتأمل الذاتي، ومفهوم الأنوثة أعمق وأشمل من أي اختبار.</p>
            <Button onClick={startTest} variant="outline" className="w-full">
                <Repeat className="ml-2 h-4 w-4" />
                أعد الاختبار
            </Button>
          </div>
        );
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-6 w-6 text-primary" />
            اختبار الأنوثة
        </CardTitle>
        <CardDescription>
            استكشفي جوانب شخصيتك من خلال هذا الاختبار البسيط.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6">
            <Progress value={progress} />
            <p className="text-sm text-muted-foreground mt-2 text-center">
                {isFinished ? 'اكتمل الاختبار!' : `السؤال ${currentQuestionIndex + 1} من ${shuffledQuestions.length}`}
            </p>
        </div>
       
        {renderContent()}

      </CardContent>
    </Card>
  );
}

