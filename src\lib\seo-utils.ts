/**
 * SEO Utilities for dynamic content generation
 */

export function getCurrentYear(): number {
  return new Date().getFullYear();
}

export function getCurrentHijriYear(): number {
  // Approximate Hijri year calculation
  const gregorianYear = getCurrentYear();
  return Math.floor(gregorianYear - 622 + (gregorianYear - 622) / 32.5);
}

export function generateDynamicTitle(baseTitle: string, includeYear: boolean = true): string {
  if (includeYear) {
    return `${baseTitle} ${getCurrentYear()}`;
  }
  return baseTitle;
}

export function generateDynamicDescription(baseDescription: string, includeYear: boolean = true): string {
  if (includeYear) {
    const year = getCurrentYear();
    return `${baseDescription} - أداة مجانية ومحدثة ${year}`;
  }
  return baseDescription;
}

export function generateKeywords(toolName: string, category?: string, additionalKeywords: string[] = []): string[] {
  const currentYear = getCurrentYear();
  const baseKeywords = [
    toolName,
    'أدوات عربية',
    'حاسبة مجانية',
    'أدوات مجانية',
    `أدوات ${currentYear}`,
    'Arabic tools',
    'free calculator',
    'online tools'
  ];

  if (category) {
    baseKeywords.push(category);
  }

  // Add tool-specific keywords
  if (toolName.includes('زكاة')) {
    baseKeywords.push('حاسبة زكاة', 'زكاة المال', 'حساب الزكاة', 'أدوات إسلامية');
  }
  
  if (toolName.includes('تاريخ') || toolName.includes('محول')) {
    baseKeywords.push('محول التاريخ', 'تقويم هجري', 'تقويم ميلادي', 'تحويل التاريخ');
  }

  if (toolName.includes('عمر') || toolName.includes('العمر')) {
    baseKeywords.push('حساب العمر', 'حاسبة العمر', 'معرفة العمر');
  }

  if (toolName.includes('نسبة') || toolName.includes('النسبة')) {
    baseKeywords.push('حساب النسبة المئوية', 'نسبة مئوية', 'حاسبة النسبة');
  }

  return [...baseKeywords, ...additionalKeywords].filter(Boolean);
}

export function generateStructuredData(tool: {
  name: string;
  description: string;
  slug: string;
  category?: string;
}, siteUrl?: string) {
  if (!siteUrl) return null;

  return {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: tool.name,
    description: tool.description,
    url: `${siteUrl}/tools/${tool.slug}`,
    applicationCategory: 'UtilityApplication',
    operatingSystem: 'Web Browser',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock'
    },
    provider: {
      '@type': 'Organization',
      name: 'أدوات بالعربي',
      url: siteUrl,
      logo: {
        '@type': 'ImageObject',
        url: `${siteUrl}/logo.png`
      }
    },
    inLanguage: ['ar', 'ar-SA'],
    isAccessibleForFree: true,
    keywords: generateKeywords(tool.name, tool.category).join(', '),
    dateModified: new Date().toISOString(),
    datePublished: new Date().toISOString(),
    audience: {
      '@type': 'Audience',
      audienceType: 'Arabic speakers, Muslims, General public'
    }
  };
}

export function generateBreadcrumbStructuredData(
  items: Array<{ name: string; url: string }>,
  siteUrl?: string
) {
  if (!siteUrl) return null;

  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url
    }))
  };
}

export function generateFAQStructuredData(faq: Array<{ question: string; answer: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faq.map(item => ({
      '@type': 'Question',
      name: item.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: item.answer
      }
    }))
  };
}

export function generateOrganizationStructuredData(siteUrl?: string) {
  if (!siteUrl) return null;

  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'أدوات بالعربي',
    alternateName: 'Arabic Tools',
    url: siteUrl,
    logo: {
      '@type': 'ImageObject',
      url: `${siteUrl}/logo.png`,
      width: '512',
      height: '512'
    },
    description: `مجموعة شاملة من الأدوات والحاسبات والمحولات المجانية باللغة العربية ${getCurrentYear()}`,
    foundingDate: '2024',
    sameAs: [
      'https://twitter.com/adawat_org',
      'https://facebook.com/adawat.org'
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'customer service',
      availableLanguage: ['Arabic', 'English']
    }
  };
}

export function optimizeImageAlt(toolName: string, context?: string): string {
  const year = getCurrentYear();
  if (context) {
    return `${toolName} - ${context} ${year}`;
  }
  return `${toolName} - أداة مجانية ${year}`;
}

export function generateCanonicalUrl(path: string, siteUrl?: string): string {
  if (!siteUrl) return path;
  return `${siteUrl}${path}`;
}

export function generateHreflangTags(path: string, siteUrl?: string) {
  if (!siteUrl) return [];
  
  return [
    { hreflang: 'ar', href: `${siteUrl}${path}` },
    { hreflang: 'ar-SA', href: `${siteUrl}${path}` },
    { hreflang: 'x-default', href: `${siteUrl}${path}` }
  ];
}

export function generateSocialMediaTags(
  title: string,
  description: string,
  url: string,
  imageUrl?: string
) {
  const year = getCurrentYear();
  const enhancedTitle = `${title} ${year}`;
  const enhancedDescription = `${description} - أداة مجانية ${year}`;

  return {
    openGraph: {
      title: enhancedTitle,
      description: enhancedDescription,
      url,
      type: 'article',
      locale: 'ar_SA',
      siteName: 'أدوات بالعربي',
      images: imageUrl ? [{ url: imageUrl, width: 1200, height: 630, alt: enhancedTitle }] : []
    },
    twitter: {
      card: 'summary_large_image',
      title: enhancedTitle,
      description: enhancedDescription,
      site: '@adawat_org',
      creator: '@adawat_org',
      images: imageUrl ? [imageUrl] : []
    }
  };
}
