
'use client';

import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Repeat, User, UserRound, Heart } from 'lucide-react';
import { cn } from '@/lib/utils';

type Gender = 'male' | 'female';

const getQuestionText = (question: any, gender: Gender): string => {
  if (typeof question.text === 'string') {
    return question.text;
  }
  return question.text[gender];
};

const getAnswerText = (answer: any, gender: Gender): string => {
    if (typeof answer.text === 'string') {
      return answer.text;
    }
    return answer.text[gender];
  };

type Answer = { text: string | { male: string; female: string }; points: number };
type Question = { text: string | { male: string; female: string }; answers: Answer[] };


const questions: Question[] = [
  // Communication
  {
    text: "عندما تكون منزعجًا من شريكك، كيف تتصرف؟",
    answers: [
      { text: "أنتظر حتى أهدأ ثم أفتح نقاشًا بنّاءً حول المشكلة.", points: 4 },
      { text: "أعبر عن انزعاجي في الحال، وقد يكون بحدة أحيانًا.", points: 3 },
      { text: "أكتم مشاعري وأتصرف ببرود، منتظرًا أن يلاحظ شريكي.", points: 2 },
      { text: "أتجاهل المشكلة تمامًا وأتظاهر بأن كل شيء على ما يرام.", points: 1 },
    ],
  },
  {
    text: "كيف تصف طريقة استماعك لشريكك عندما يتحدث عن مشاكله؟",
    answers: [
      { text: "أستمع بكل انتباه، وأقدم الدعم العاطفي، وأركز على مشاعره.", points: 4 },
      { text: "أستمع وأحاول على الفور تقديم حلول عملية للمشكلة.", points: 3 },
      { text: "أستمع ولكن قد أفقد تركيزي أو أقاطعه أحيانًا.", points: 2 },
      { text: "أجد صعوبة في الاستماع للمشاكل وأفضل تغيير الموضوع.", points: 1 },
    ],
  },
  // Affection & Appreciation
  {
    text: "كم مرة تعبر عن حبك أو تقديرك لشريكك بالكلمات؟",
    answers: [
      { text: "يوميًا، أحرص على تذكيره بمدى أهميته لي.", points: 4 },
      { text: "عدة مرات في الأسبوع.", points: 3 },
      { text: "في المناسبات الخاصة فقط.", points: 2 },
      { text: "نادرًا، أعتقد أن الأفعال أهم من الكلمات.", points: 1 },
    ],
  },
  {
    text: "قام شريكك بلفتة لطيفة وصغيرة من أجلك. ما هو رد فعلك؟",
    answers: [
      { text: "أعبر عن امتناني بحرارة وأقوم بلفتة لطيفة في المقابل.", points: 4 },
      { text: "أشكره بصدق وأقدر مجهوده.", points: 3 },
      { text: "أبتسم وأقول شكرًا بشكل عابر.", points: 2 },
      { text: "قد لا ألاحظ الأمر أو أعتبره شيئًا عاديًا.", points: 1 },
    ],
  },
  // Support & Partnership
  {
    text: "شريكك لديه حلم أو هدف كبير يسعى لتحقيقه. ما هو دورك؟",
    answers: [
      { text: "أكون أكبر داعم ومشجع له، وأساعده بكل الطرق الممكنة.", points: 4 },
      { text: "أشجعه وأقدم له النصيحة إذا طلبها.", points: 3 },
      { text: "أدعمه، لكنني أقلق من أن هذا الهدف قد يؤثر على علاقتنا.", points: 2 },
      { text: "أعتقد أن حلمه غير واقعي وأحاول توجيهه لشيء آخر.", points: 1 },
    ],
  },
  {
    text: "عندما تواجهان قرارًا كبيرًا معًا (مثل الانتقال أو شراء شيء باهظ)، كيف يتم اتخاذه؟",
    answers: [
      { text: "نتناقش بعمق، ونوازن الإيجابيات والسلبيات، ونتوصل إلى قرار مشترك يرضي الطرفين.", points: 4 },
      { text: "نتناقش، ولكن في النهاية أحدنا يتخذ القرار النهائي.", points: 3 },
      { text: "غالبًا ما يكون هناك جدال، وقد يتخذ أحدنا القرار من جانب واحد.", points: 2 },
      { text: "أتجنب اتخاذ القرارات الكبيرة وأتركها للشريك.", points: 1 },
    ],
  },
  // Quality Time
  {
    text: "ماذا يعني لك 'قضاء وقت نوعي' مع شريكك؟",
    answers: [
      { text: "فعل أي نشاط معًا مع التركيز الكامل على بعضنا البعض، بدون هواتف أو مشتتات.", points: 4 },
      { text: "الخروج لتناول العشاء أو مشاهدة فيلم معًا.", points: 3 },
      { text: "التواجد في نفس الغرفة، حتى لو كان كل منا يفعل شيئًا مختلفًا.", points: 2 },
      { text: "لا أفكر كثيرًا في هذا المفهوم.", points: 1 },
    ],
  },
  {
    text: "هل تبادر بالتخطيط لمفاجآت أو مواعيد رومانسية لشريكك؟",
    answers: [
      { text: "نعم، أستمتع بالتخطيط لأشياء خاصة ومفاجئة بانتظام.", points: 4 },
      { text: "أفعل ذلك أحيانًا في المناسبات الخاصة.", points: 3 },
      { text: "نادرًا جدًا، أنا لست جيدًا في التخطيط.", points: 2 },
      { text: "لا، لا أعتقد أن هذه الأمور ضرورية.", points: 1 },
    ],
  },
  // Conflict Resolution
  {
    text: "بعد شجار، من الذي يبادر عادةً بالمصالحة؟",
    answers: [
      { text: "لا يهم من المخطئ، المهم أن نتصالح بسرعة، لذلك قد أبادر أنا.", points: 4 },
      { text: "عادةً ما يبادر الطرف الذي يشعر أنه كان مخطئًا.", points: 3 },
      { text: "أنتظر دائمًا أن يبادر شريكي بالاعتذار.", points: 2 },
      { text: "قد تمر أيام من الصمت قبل أن يتحدث أحدنا مع الآخر.", points: 1 },
    ],
  },
  {
    text: "هل أنت على استعداد للاعتذار حتى لو كنت تعتقد أنك على صواب جزئيًا؟",
    answers: [
      { text: "نعم، من أجل الحفاظ على سلام العلاقة، أنا مستعد للاعتذار عن دوري في الخلاف.", points: 4 },
      { text: "قد أعتذر، لكنني سأوضح أنني لم أكن المخطئ الوحيد.", points: 3 },
      { text: "نادرًا، أجد صعوبة في الاعتذار إذا لم أكن مخطئًا 100%.", points: 2 },
      { text: "لا، لن أعتذر أبدًا إذا لم أكن المخطئ.", points: 1 },
    ],
  },
];

const getResult = (score: number, gender: Gender) => {
    const results = {
      level1: {
        title: "حب عملي وواقعي",
        text: "حبك يتسم بالواقعية والهدوء. أنت تقدر الاستقرار والشراكة، لكن قد تحتاج إلى بذل المزيد من الجهد في التعبير عن مشاعرك وتقديرك لشريكك بشكل منتظم. حاول إضافة المزيد من اللفتات الرومانسية الصغيرة لإبقاء الشغف متقدًا."
      },
      level2: {
        title: "حب متوازن ومستقر",
        text: "أنت تعرف كيفية الموازنة بين الجانب العملي والعاطفي في الحب. علاقتك مبنية على الاحترام والتفاهم، وتتمتع بقدرة جيدة على التواصل وحل المشكلات. صداقتك مع شريكك قوية، مما يجعل علاقتكما متينة."
      },
      level3: {
        title: "حب عاطفي ورومانسي",
        text: "أنت شخص عاطفي ورومانسي، وتضع العلاقة في مقدمة أولوياتك. تحب التعبير عن مشاعرك وتقديرك، وتستمتع بقضاء وقت نوعي مع شريكك. تأكد فقط من أن هذا الشغف لا يتحول إلى غيرة أو تعلق مفرط."
      },
      level4: {
        title: "حب مثالي وشغوف",
        text: "أنت تعيش الحب بكل جوارحك. أنت شريك داعم، ومتواصل، ورومانسي إلى أبعد الحدود. علاقتك مليئة بالشغف والتقدير المتبادل، وتعمل بجد للحفاظ عليها قوية ومثيرة. أنت تمثل الشريك المثالي للكثيرين."
      }
    };
  
    if (score <= 18) {
      return { level: results.level1.title, description: results.level1.text };
    }
    if (score <= 26) {
      return { level: results.level2.title, description: results.level2.text };
    }
    if (score <= 34) {
      return { level: results.level3.title, description: results.level3.text };
    }
    return { level: results.level4.title, description: results.level4.text };
};

type TestStage = 'gender' | 'questions' | 'result';

// Helper to shuffle an array
function shuffleArray<T>(array: T[]): T[] {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}


export function LoveTestTool() {
  const [stage, setStage] = useState<TestStage>('gender');
  const [gender, setGender] = useState<Gender | null>(null);
  const [shuffledQuestions, setShuffledQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  
  const isFinished = stage === 'result';
  const totalScore = answers.reduce((sum, score) => sum + score, 0);
  const result = isFinished && gender ? getResult(totalScore, gender) : { level: '', description: '' };

  const startTest = (selectedGender: Gender) => {
    setGender(selectedGender);
    setAnswers([]);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setShuffledQuestions(shuffleArray(questions));
    setStage('questions');
  };

  const handleNextQuestion = () => {
    if (selectedAnswer === null) return;
    
    const newAnswers = [...answers, selectedAnswer];
    setAnswers(newAnswers);
    setSelectedAnswer(null);

    if (currentQuestionIndex < shuffledQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      setStage('result');
    }
  };

  const handleRestart = () => {
    setStage('gender');
    setGender(null);
  };
  
  const progress = isFinished ? 100 : ((currentQuestionIndex) / shuffledQuestions.length) * 100;
  
  const currentQuestion = shuffledQuestions[currentQuestionIndex];

  const shuffledAnswers = useMemo(() => {
    if (!currentQuestion) return [];
    return shuffleArray(currentQuestion.answers);
  }, [currentQuestion]);


  const renderContent = () => {
    switch(stage) {
      case 'gender':
        return (
          <div className="text-center space-y-6">
            <h3 className="text-lg font-semibold">قبل أن نبدأ، يرجى تحديد جنسك</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Button variant="outline" className="h-24 text-xl" onClick={() => startTest('male')}>
                <User className="ml-3 h-8 w-8" />
                ذكر
              </Button>
              <Button variant="outline" className="h-24 text-xl" onClick={() => startTest('female')}>
                <UserRound className="ml-3 h-8 w-8" />
                أنثى
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">هذا يساعدنا على تقديم تجربة أفضل في المستقبل.</p>
          </div>
        );
      
      case 'questions':
        if (!gender || !currentQuestion) return null;
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-center">{getQuestionText(currentQuestion, gender)}</h3>
            <RadioGroup
              key={currentQuestionIndex}
              value={selectedAnswer?.toString() || ""}
              onValueChange={(value) => setSelectedAnswer(parseInt(value, 10))}
              className="space-y-3"
            >
              {shuffledAnswers.map((answer, index) => (
                <Label 
                  key={index} 
                  dir="rtl"
                  className="flex items-center justify-between gap-x-3 p-4 border rounded-lg cursor-pointer hover:bg-muted/50 has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary"
                >
                  <span className="flex-1 text-right">{getAnswerText(answer, gender)}</span>
                  <RadioGroupItem 
                    value={String(answer.points)} 
                    id={`q${currentQuestionIndex}-a${index}`} 
                  />
                </Label>
              ))}
            </RadioGroup>
            <Button onClick={handleNextQuestion} disabled={selectedAnswer === null} className="w-full">
              {currentQuestionIndex === questions.length - 1 ? 'عرض النتيجة' : 'التالي'}
            </Button>
          </div>
        );
      case 'result':
        return (
          <div className="text-center space-y-4">
            <h3 className="text-2xl font-bold text-primary">{result.level}</h3>
            <p className="text-muted-foreground leading-relaxed">{result.description}</p>
            <p className="text-xs text-muted-foreground pt-4 border-t">إخلاء مسؤولية: هذا الاختبار هو لأغراض الترفيه والتثقيف الذاتي فقط ولا يمثل تشخيصًا نفسيًا.</p>
            <Button onClick={handleRestart} variant="outline" className="w-full">
                <Repeat className="ml-2 h-4 w-4" />
                أعد الاختبار
            </Button>
          </div>
        );
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
            <Heart className="h-6 w-6 text-primary" />
            اختبار الحب
        </CardTitle>
        <CardDescription>
            أجب عن الأسئلة التالية بصدق لاكتشاف أسلوبك في الحب والعلاقات العاطفية.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {stage !== 'gender' && (
             <div className="mb-6">
                <Progress value={progress} />
                <p className="text-sm text-muted-foreground mt-2 text-center">
                    {isFinished ? 'اكتمل الاختبار!' : `السؤال ${currentQuestionIndex + 1} من ${shuffledQuestions.length}`}
                </p>
            </div>
        )}
       
        {renderContent()}

      </CardContent>
    </Card>
  );
}
