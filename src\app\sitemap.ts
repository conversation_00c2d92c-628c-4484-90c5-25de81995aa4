import { MetadataRoute } from 'next';
import { toolCategories } from '@/lib/tools';
import HijriDate from 'hijri-date';

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;

export default function sitemap(): MetadataRoute.Sitemap {
  if (!siteUrl) {
    console.warn("NEXT_PUBLIC_SITE_URL is not set. Sitemap will be incomplete.");
    return [];
  }

  const staticRoutes: MetadataRoute.Sitemap = [
    {
      url: siteUrl,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 1,
    },
    {
      url: `${siteUrl}/p/privacy-policy`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${siteUrl}/p/terms-of-service`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${siteUrl}/p/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${siteUrl}/p/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${siteUrl}/p/cookies`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${siteUrl}/disclaimer`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${siteUrl}/sitemap-page`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.4,
    },
    {
      url: `${siteUrl}/articles`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
  ];

  const categoryRoutes: MetadataRoute.Sitemap = toolCategories.map((category) => ({
    url: `${siteUrl}/categories/${category.slug}`,
    lastModified: new Date(),
    changeFrequency: 'weekly',
    priority: 0.8,
  }));

  const toolRoutes: MetadataRoute.Sitemap = toolCategories.flatMap((category) =>
    category.tools
      .filter(tool => tool.component)
      .map((tool) => ({
        url: `${siteUrl}${tool.path}`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.9,
      }))
  );

  // Article routes
  const articleRoutes: MetadataRoute.Sitemap = [
    {
      url: `${siteUrl}/articles/how-to-calculate-zakat`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${siteUrl}/articles/hijri-calendar-guide`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.7,
    },
  ];

  const yearRoutes: MetadataRoute.Sitemap = [];
  const currentGregorianYear = new Date().getFullYear();
  const currentHijriYear = new HijriDate().getFullYear();

  // Add Gregorian years from 1940 to current year
  for (let year = 1940; year <= currentGregorianYear + 1; year++) {
    yearRoutes.push({
      url: `${siteUrl}/year/${year}`,
      lastModified: new Date(),
      changeFrequency: 'yearly',
      priority: 0.7,
    });
  }

  // Add Hijri years from 1360 to current year
  for (let year = 1360; year <= currentHijriYear + 1; year++) {
    yearRoutes.push({
      url: `${siteUrl}/year/${year}h`,
      lastModified: new Date(),
      changeFrequency: 'yearly',
      priority: 0.7,
    });
  }


  return [...staticRoutes, ...categoryRoutes, ...toolRoutes, ...articleRoutes, ...yearRoutes];
}
