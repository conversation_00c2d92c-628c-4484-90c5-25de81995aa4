'use client';

import { useState, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ArrowRightLeft, AlertTriangle } from 'lucide-react';
import type { getCurrencyRates } from '@/lib/actions/currency';

const FormSchema = z.object({
  amount: z.coerce.number().min(0).default(1),
  from: z.string().min(3, { message: 'اختر عملة' }).default('USD'),
  to: z.string().min(3, { message: 'اختر عملة' }).default('SAR'),
});

type CurrencyData = Awaited<ReturnType<typeof getCurrencyRates>>;

interface CurrencyConverterToolProps {
  initialData: CurrencyData;
}

// العملات العربية مع أسمائها المختصرة
const ARAB_CURRENCIES = {
  SAR: 'ريال سعودي',
  AED: 'درهم إماراتي', 
  KWD: 'دينار كويتي',
  QAR: 'ريال قطري',
  OMR: 'ريال عماني',
  BHD: 'دينار بحريني',
  JOD: 'دينار أردني',
  LBP: 'ليرة لبنانية',
  SYP: 'ليرة سورية',
  IQD: 'دينار عراقي',
  EGP: 'جنيه مصري',
  LYD: 'دينار ليبي',
  TND: 'دينار تونسي',
  DZD: 'دينار جزائري',
  MAD: 'درهم مغربي',
  MRU: 'أوقية موريتانية',
  SDG: 'جنيه سوداني',
  SOS: 'شلن صومالي',
  DJF: 'فرنك جيبوتي',
  KMF: 'فرنك قمري',
  YER: 'ريال يمني',
  USD: 'دولار أمريكي',
  EUR: 'يورو'
};

export function CurrencyConverterTool({ initialData }: CurrencyConverterToolProps) {
  const [result, setResult] = useState<string | null>(null);
  const { success, rates, currencies, error } = initialData;
  
  // تصفية العملات لتظهر العربية فقط
  const arabCurrencies = currencies?.filter(currency => 
    Object.keys(ARAB_CURRENCIES).includes(currency)
  ) || [];

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      amount: 1,
      from: 'USD',
      to: 'SAR',
    },
  });

  const { watch, setValue } = form;
  const watchedFields = watch();

  useEffect(() => {
    if (success && rates) {
      const { amount, from, to } = watchedFields;
      if (rates[from] && rates[to]) {
        const conversionRate = rates[to] / rates[from];
        const convertedAmount = amount * conversionRate;
        setResult(convertedAmount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 4 }));
      }
    }
  }, [watchedFields, rates, success]);
  
  const handleSwap = () => {
    const { from, to } = watchedFields;
    setValue('from', to);
    setValue('to', from);
  };

  const exchangeRateText = useMemo(() => {
    if (success && rates && watchedFields.from && watchedFields.to && rates[watchedFields.from] && rates[watchedFields.to]) {
        const rate = (rates[watchedFields.to] / rates[watchedFields.from]).toFixed(4);
        return `1 ${watchedFields.from} = ${rate} ${watchedFields.to}`;
    }
    return 'جاري تحميل أسعار الصرف...';
  }, [success, rates, watchedFields.from, watchedFields.to]);


  if (!success) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>خطأ في تحميل البيانات</AlertTitle>
        <AlertDescription>
          لم نتمكن من تحميل أسعار العملات. الرجاء المحاولة مرة أخرى لاحقًا.
          <p className="mt-2 text-xs">تفاصيل الخطأ: {error}</p>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>محول العملات</CardTitle>
        <CardDescription>
            {exchangeRateText}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>المبلغ</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="from"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>من</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger><SelectValue /></SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {arabCurrencies?.map(currency => (
                          <SelectItem key={currency} value={currency} className="text-sm">
                            <div className="flex flex-col">
                              <span className="font-medium">{currency}</span>
                              <span className="text-xs text-muted-foreground">{ARAB_CURRENCIES[currency as keyof typeof ARAB_CURRENCIES]}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
               <Button variant="ghost" size="icon" className="self-end mb-1" onClick={handleSwap} type="button">
                  <ArrowRightLeft className="w-5 h-5 text-muted-foreground" />
                </Button>
              <FormField
                control={form.control}
                name="to"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>إلى</FormLabel>
                     <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger><SelectValue /></SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {arabCurrencies?.map(currency => (
                          <SelectItem key={currency} value={currency} className="text-sm">
                            <div className="flex flex-col">
                              <span className="font-medium">{currency}</span>
                              <span className="text-xs text-muted-foreground">{ARAB_CURRENCIES[currency as keyof typeof ARAB_CURRENCIES]}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
            </div>
            <div className="pt-4">
              <p className="text-sm text-muted-foreground">المبلغ المحول</p>
              <p className="text-3xl lg:text-4xl font-bold font-mono text-primary">{result ?? '...'}</p>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
