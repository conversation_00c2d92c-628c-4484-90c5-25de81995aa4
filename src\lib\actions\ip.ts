
'use server';

import { headers } from 'next/headers';
import { COUNTRY_PHONE_CODES, COUNTRIES_CURRENCIES } from '../constants/currencies';

interface IpInfo {
  ip: string;
  countryCode?: string;
  countryName?: string;
  phoneCode?: string;
  error?: string;
}

// Create a reverse map from country code to country details
const countryCodeMap: Record<string, { name: string; currency: string; }> = {};
for (const currency in COUNTRIES_CURRENCIES) {
    const details = COUNTRIES_CURRENCIES[currency as keyof typeof COUNTRIES_CURRENCIES];
    countryCodeMap[details.countryCode] = { name: details.name, currency: details.currency };
}


export async function getIpInfo(): Promise<IpInfo> {
  const headersList = await headers();
  let ipAddress = headersList.get('x-forwarded-for')?.split(',')[0].trim() || headersList.get('x-real-ip');

  // If no IP from headers, try an external service just to get the IP
  if (!ipAddress) {
    try {
        const ipRes = await fetch('https://api.ipify.org?format=json', { cache: 'no-store' });
        if (ipRes.ok) {
            const ipData = await ipRes.json();
            ipAddress = ipData.ip;
        }
    } catch (e) {
      // Ignore error, will fallback later
    }
  }

  // If we have an IP, try to get geolocation info
  if (ipAddress) {
    try {
      // Switched to a more reliable geolocation service
      const geoResponse = await fetch(`http://ip-api.com/json/${ipAddress}`, {
         cache: 'no-store',
      });
      if (geoResponse.ok) {
        const geoData = await geoResponse.json();
        if (geoData.status === 'success' && geoData.countryCode) {
            const countryInfo = countryCodeMap[geoData.countryCode];
            return { 
                ip: ipAddress, 
                countryCode: geoData.countryCode,
                countryName: countryInfo?.name,
                phoneCode: COUNTRY_PHONE_CODES[geoData.countryCode]
            };
        }
      }
    } catch (error) {
      console.error('Error fetching geolocation from ip-api.com:', error);
    }
  }
  
  // Fallback if anything fails
  return { 
    ip: ipAddress || 'غير معروف', 
    countryName: 'غير معروف',
    error: 'تعذر تحديد الموقع الجغرافي' 
  };
}
