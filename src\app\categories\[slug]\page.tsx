import { notFound } from 'next/navigation';
import Link from 'next/link';
import { toolCategories } from '@/lib/tools';
import { PageHeader } from '@/components/PageHeader';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft } from 'lucide-react';
import type { Metadata, ResolvingMetadata } from 'next';
import { JsonLd } from '@/components/JsonLd';
import type { BreadcrumbList } from 'schema-dts';

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;

type Props = {
  params: { slug: string };
};

export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const { slug } = params;
  const category = toolCategories.find(c => c.slug === slug);

  if (!category) {
    return {
      title: 'قسم غير موجود',
    };
  }

  const previousImages = (await parent).openGraph?.images || [];
  const categoryUrl = siteUrl ? `${siteUrl}/categories/${slug}` : `/categories/${slug}`;

  return {
    title: category.name,
    description: category.description,
    alternates: {
      canonical: `/categories/${slug}`,
    },
    openGraph: {
      title: `${category.name} | أدوات بالعربي`,
      description: category.description,
      url: categoryUrl,
      images: [...previousImages],
    },
    twitter: {
      title: `${category.name} | أدوات بالعربي`,
      description: category.description,
      images: [...previousImages],
    },
  };
}


export default async function CategoryPage({ params }: { params: { slug: string } }) {
  const { slug } = params;
  const category = toolCategories.find(c => c.slug === slug);

  if (!category) {
    notFound();
  }

  const jsonLd: BreadcrumbList | null = siteUrl ? {
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'الرئيسية',
        item: siteUrl,
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: category.name,
        item: `${siteUrl}/categories/${slug}`,
      },
    ],
  } : null;

  return (
    <div className="w-full">
      {jsonLd && <JsonLd data={jsonLd} />}
      <PageHeader
        title={category.name}
        description={category.description}
      />
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-12">
        {category.tools.map(tool => (
          <Link key={tool.path} href={tool.path} className="group">
            <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-primary">
              <CardHeader>
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        {tool.icon && (
                            <div className="p-2 rounded-full bg-primary/10 text-primary">
                                <tool.icon className="w-5 h-5" />
                            </div>
                        )}
                        <CardTitle className="font-headline text-lg">{tool.name}</CardTitle>
                    </div>
                    <ArrowLeft className="h-5 w-5 text-muted-foreground transition-transform group-hover:translate-x-[-4px] group-hover:text-primary shrink-0" />
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {tool.description}
                </p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}

export async function generateStaticParams() {
  return toolCategories.map(category => ({
    slug: category.slug,
  }));
}
