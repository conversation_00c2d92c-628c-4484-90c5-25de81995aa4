
'use server';

import HTMLtoDOCX from 'html-to-docx';

/**
 * Generates a DOCX file buffer from an HTML string.
 * This function is a Server Action and will only run on the server.
 * @param textContent The plain text content to be converted.
 * @returns A Base64 encoded string of the generated DOCX file.
 */
export async function generateDocx(textContent: string): Promise<string> {
    try {
        // Wrap each line in a <p> tag to ensure it's treated as a paragraph
        const htmlString = textContent
            .split('\n')
            .map(p => `<p>${p || '&nbsp;'}</p>`) // Use &nbsp; for empty lines to preserve them
            .join('');
            
        const fileBuffer = await HTMLtoDOCX(htmlString, undefined, {
            document: {
                styles: {
                    paragraphStyles: [
                        {
                            id: 'Normal',
                            name: 'Normal',
                            run: {
                                size: 24, // 12pt
                                font: 'Calibri',
                            },
                            paragraph: {
                                alignment: 'right', // Align right
                                rightToLeft: true, // Set text direction to RTL
                                spacing: {
                                    after: 120, // Spacing after paragraph
                                },
                            },
                        },
                    ],
                },
            },
            skipFirstHeaderFooter: true,
        });
        
        // Check if fileBuffer is a Buffer or Blob and convert to Base64
        if (fileBuffer instanceof Buffer) {
            return fileBuffer.toString('base64');
        } 
        
        // The library returns a Blob in some environments
        if (typeof Blob !== 'undefined' && fileBuffer instanceof Blob) {
             const arrayBuffer = await fileBuffer.arrayBuffer();
             return Buffer.from(arrayBuffer).toString('base64');
        }
        
        throw new Error('Generated file is not a recognizable buffer or blob.');

    } catch (error) {
        console.error("Error in generateDocx server action:", error);
        throw new Error("Failed to generate DOCX file on the server.");
    }
}
