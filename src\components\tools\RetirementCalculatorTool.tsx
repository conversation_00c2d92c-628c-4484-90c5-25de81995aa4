
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  currentAge: requiredNumber().int().positive("العمر الحالي يجب أن يكون موجبًا."),
  retirementAge: requiredNumber().int().positive("عمر التقاعد يجب أن يكون موجبًا."),
  currentSavings: requiredNumber().nonnegative("المدخرات الحالية لا يمكن أن تكون سالبة.").default(0),
  monthlyContribution: requiredNumber().nonnegative("المساهمة الشهرية لا يمكن أن تكون سالبة.").default(0),
  annualReturn: requiredNumber().nonnegative("العائد السنوي لا يمكن أن يكون سالبًا.").default(5),
}).refine(data => data.retirementAge > data.currentAge, {
  message: "عمر التقاعد يجب أن يكون أكبر من العمر الحالي.",
  path: ['retirementAge'],
});

export function RetirementCalculatorTool() {
  const [result, setResult] = useState<number | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
        currentSavings: 0,
        monthlyContribution: 0,
        annualReturn: 5,
    }
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const { currentAge, retirementAge, currentSavings, monthlyContribution, annualReturn } = data;
    
    const yearsToRetirement = retirementAge - currentAge;
    const monthsToRetirement = yearsToRetirement * 12;
    const monthlyReturnRate = (annualReturn / 100) / 12;
    
    // Future value of current savings
    const fvOfCurrentSavings = currentSavings * Math.pow(1 + monthlyReturnRate, monthsToRetirement);

    // Future value of monthly contributions (annuity)
    const fvOfContributions = monthlyContribution * ((Math.pow(1 + monthlyReturnRate, monthsToRetirement) - 1) / monthlyReturnRate);

    const totalSavings = fvOfCurrentSavings + fvOfContributions;
    setResult(totalSavings);
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حاسبة التقاعد</CardTitle>
        <CardDescription>خطط لمستقبلك المالي وقدّر حجم مدخراتك عند التقاعد.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField name="currentAge" control={form.control} render={({ field }) => (
                <FormItem><FormLabel>عمرك الحالي</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
              <FormField name="retirementAge" control={form.control} render={({ field }) => (
                <FormItem><FormLabel>عمر التقاعد المستهدف</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
              <FormField name="currentSavings" control={form.control} render={({ field }) => (
                <FormItem><FormLabel>مدخراتك الحالية</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
              <FormField name="monthlyContribution" control={form.control} render={({ field }) => (
                <FormItem><FormLabel>المساهمة الشهرية</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
              <FormField name="annualReturn" control={form.control} render={({ field }) => (
                <FormItem className="md:col-span-2"><FormLabel>متوسط العائد السنوي المتوقع على الاستثمار (%)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
            </div>
            <Button type="submit" className="w-full">احسب مدخرات التقاعد</Button>
          </form>
        </Form>
        {result !== null && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-2">إجمالي مدخراتك عند التقاعد (تقديري)</h3>
            <div className="p-6 bg-primary/10 rounded-lg">
              <p className="text-5xl font-bold font-mono text-primary">
                {result.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR', minimumFractionDigits: 0, numberingSystem: 'latn' })}
              </p>
            </div>
            <p className="text-xs text-muted-foreground mt-4">
              هذا الرقم هو تقدير بناءً على المدخلات. النتائج الفعلية قد تختلف.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
