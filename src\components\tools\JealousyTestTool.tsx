
'use client';

import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { HeartCrack, Repeat, User, UserRound } from 'lucide-react';
import { cn } from '@/lib/utils';

type Gender = 'male' | 'female';

// Helper function to get the correct question text
const getQuestionText = (question: any, gender: Gender): string => {
  if (typeof question.text === 'string') {
    return question.text;
  }
  return question.text[gender];
};

const getAnswerText = (answer: any, gender: Gender): string => {
    if (typeof answer.text === 'string') {
      return answer.text;
    }
    return answer.text[gender];
};

type Answer = { text: string | { male: string; female: string }; points: number };
type Question = { text: string | { male: string; female: string }; answers: Answer[] };


const questions: Question[] = [
  // Trust & Security
  {
    text: {
      male: "كيف تشعر عندما تتحدث شريكتك عن شريك سابق؟",
      female: "كيف تشعرين عندما يتحدث شريكك عن شريكة سابقة؟"
    },
    answers: [
      { text: "لا أهتم، الماضي هو الماضي.", points: 1 },
      { text: "أشعر ببعض الفضول، لكن لا يزعجني الأمر.", points: 2 },
      { text: "أشعر بالانزعاج وأفضل عدم التحدث في الموضوع.", points: 3 },
      { text: {
          male: "أشعر بالغضب وأبدأ في مقارنة نفسي به.",
          female: "أشعر بالغضب وأبدأ في مقارنة نفسي بها."
        }, points: 4 },
    ],
  },
  {
    text: {
        male: "ماذا تفعل إذا تلقت شريكتك رسالة نصية في وقت متأخر من الليل؟",
        female: "ماذا تفعلين إذا تلقى شريكك رسالة نصية في وقت متأخر من الليل؟"
    },
    answers: [
      { text: {
          male: "أفترض أنها من صديقة أو فرد من العائلة.",
          female: "أفترض أنها من صديق أو فرد من العائلة."
        }, points: 1 },
      { text: {
          male: "قد أسأل بشكل عابر في الصباح من كان يراسلها.",
          female: "قد أسأل بشكل عابر في الصباح من كان يراسله."
        }, points: 2 },
      { text: "أشعر بالقلق وأحاول معرفة من المرسل.", points: 3 },
      { text: {
          male: "أواجهها على الفور وأطلب رؤية الرسالة.",
          female: "أواجهه على الفور وأطلب رؤية الرسالة."
        }, points: 4 },
    ],
  },
  {
    text: {
        male: "تخطط شريكتك للخروج مع صديقات لا تعرفهن جيدًا. كيف تتصرف؟",
        female: "يخطط شريكك للخروج مع أصدقاء لا تعرفهم جيدًا. كيف تتصرفين؟"
    },
    answers: [
      { text: {
          male: "أشجعها على قضاء وقت ممتع.",
          female: "أشجعه على قضاء وقت ممتع."
        }, points: 1 },
      { text: {
          male: "أتمنى لو كنت مدعوًا، لكن أتفهم حاجتها لوقتها الخاص.",
          female: "أتمنى لو كنت مدعوة، لكني أتفهم حاجته لوقته الخاص."
        }, points: 2 },
      { text: {
          male: "أشعر ببعض الاستبعاد وأظل قلقًا حتى تعود.",
          female: "أشعر ببعض الاستبعاد وأظل قلقة حتى يعود."
        }, points: 3 },
      { text: {
          male: "أصر على الذهاب معها أو أعبر عن استيائي من خروجها بدوني.",
          female: "أصر على الذهاب معه أو أعبر عن استيائي من خروجه بدوني."
        }, points: 4 },
    ],
  },
   {
    text: {
        male: "زميل عمل لشريكتك يقدم لها مجاملة. كيف يكون شعورك؟",
        female: "زميلة عمل لشريكك تقدم له مجاملة. كيف يكون شعورك؟"
    },
    answers: [
      { text: {
          male: "أشعر بالسعادة والفخر لشريكتي.",
          female: "أشعر بالسعادة والفخر لشريكي."
        }, points: 1 },
      { text: {
          male: "أشعر بالرضا ولكن قد أكون فضوليًا بشأن هذا الزميل.",
          female: "أشعر بالرضا ولكن قد أكون فضولية بشأن هذه الزميلة."
        }, points: 2 },
      { text: "أشعر بالشك وأتساءل عن طبيعة علاقتهما.", points: 3 },
      { text: "أشعر بالغيرة الشديدة وأعتبر المجاملة تجاوزًا للحدود.", points: 4 },
    ],
  },
  // Social Media
  {
    text: {
        male: "تتابع شريكتك شخصًا جذابًا على وسائل التواصل الاجتماعي. ما هو رد فعلك؟",
        female: "يتابع شريكك شخصًا جذابًا على وسائل التواصل الاجتماعي. ما هو رد فعلك؟"
    },
    answers: [
      { text: "لا ألاحظ أو لا أهتم.", points: 1 },
      { text: {
          male: "ألاحظ، لكن أفترض أنها مجرد متابعة عادية.",
          female: "ألاحظ، لكن أفترض أنه مجرد متابعة عادية."
        }, points: 2 },
      { text: "أقوم بفحص ملف ذلك الشخص لمعرفة المزيد عنه.", points: 3 },
      { text: {
          male: "أشعر بالتهديد وأسأل شريكتي لماذا تتابعه.",
          female: "أشعر بالتهديد وأسأل شريكي لماذا يتابعها."
        }, points: 4 },
    ],
  },
  {
    text: {
        male: "تجد أن شريكتك قد أعجبت بصورة قديمة لشخص ما على انستغرام. ماذا تفعل؟",
        female: "تجدين أن شريكك قد أعجب بصورة قديمة لشخص ما على انستغرام. ماذا تفعلين؟"
    },
    answers: [
      { text: {
          male: "لا أفعل شيئًا، ربما ظهرت لها الصورة بالصدفة.",
          female: "لا أفعل شيئًا، ربما ظهرت له الصورة بالصدفة."
        }, points: 1 },
      { text: "أشعر بالاستغراب للحظة ثم أنسى الأمر.", points: 2 },
      { text: "أشعر بالريبة وأبحث في حساب ذلك الشخص.", points: 3 },
      { text: {
          male: "أعتبره دليلًا على عدم الاهتمام وأقوم بمواجهتها.",
          female: "أعتبره دليلًا على عدم الاهتمام وأقوم بمواجهته."
        }, points: 4 },
    ],
  },
   {
    text: {
        male: "كم مرة تتفقد حسابات شريكتك على وسائل التواصل الاجتماعي؟",
        female: "كم مرة تتفقدين حسابات شريكك على وسائل التواصل الاجتماعي؟"
    },
    answers: [
      { text: "نادرًا جدًا أو أبدًا.", points: 1 },
      { text: "من وقت لآخر، بشكل عرضي.", points: 2 },
      { text: {
          male: "بشكل متكرر، لأرى ما تفعله ومع من تتفاعل.",
          female: "بشكل متكرر، لأرى ما يفعله ومع من يتفاعل."
        }, points: 3 },
      { text: "يوميًا، وأحلل كل إعجاب وتعليق.", points: 4 },
    ],
  },
  // Possessiveness
  {
    text: {
        male: "تقضي شريكتك وقتًا طويلاً في هواية لا تشمل وجودك. كيف تشعر؟",
        female: "يقضي شريكك وقتًا طويلاً في هواية لا تشمل وجودك. كيف تشعرين؟"
    },
    answers: [
      { text: {
          male: "أشعر بالسعادة لأن لديها اهتماماتها الخاصة.",
          female: "أشعر بالسعادة لأن لديه اهتماماته الخاصة."
        }, points: 1 },
      { text: {
          male: "أتمنى أن تشاركني أكثر، لكني أحترم وقتها.",
          female: "أتمنى أن يشاركني أكثر، لكني أحترم وقته."
        }, points: 2 },
      { text: "أشعر بالوحدة وأتمنى أن يقضي هذا الوقت معي.", points: 3 },
      { text: {
          male: "أشعر بالإهمال وأعتقد أنها تفضل هوايتها عليّ.",
          female: "أشعر بالإهمال وأعتقد أنه يفضل هوايته عليّ."
        }, points: 4 },
    ],
  },
   {
    text: {
        male: "إذا لم ترد شريكتك على مكالمتك على الفور، ماذا تفترض؟",
        female: "إذا لم يرد شريكك على مكالمتك على الفور، ماذا تفترضين؟"
    },
    answers: [
      { text: "أنها مشغولة وستعاود الاتصال عندما تستطيع.", points: 1 },
      { text: {
          male: "أنها قد تكون في اجتماع أو مكان لا تستطيع الرد فيه.",
          female: "أنه قد يكون في اجتماع أو مكان لا يستطيع الرد فيه."
        }, points: 2 },
      { text: {
          male: "أبدأ بالقلق وأفكر في أسباب عدم ردها.",
          female: "أبدأ بالقلق وأفكر في أسباب عدم رده."
        }, points: 3 },
      { text: "أفترض أنها تتجاهلني وأشعر بالانزعاج وأتصل مرارًا وتكرارًا.", points: 4 },
    ],
  },
  {
    text: {
        male: "كيف تصف حاجتك للاهتمام من شريكتك؟",
        female: "كيف تصفين حاجتك للاهتمام من شريكك؟"
    },
    answers: [
      { text: "أقدر الاهتمام ولكني مستقل ولا أحتاجه باستمرار.", points: 1 },
      { text: "أستمتع بالاهتمام وأطلبه من وقت لآخر.", points: 2 },
      { text: "أحتاج إلى الكثير من الاهتمام لأشعر بالأمان.", points: 3 },
      { text: {
          male: "أحتاج إلى اهتمام وتأكيد دائم بأني الأهم في حياتها.",
          female: "أحتاج إلى اهتمام وتأكيد دائم بأني الأهم في حياته."
        }, points: 4 },
    ],
  },
  // Suspicion
  {
    text: {
        male: "تتصرف شريكتك بغموض حول هاتفها. ماذا يدور في ذهنك؟",
        female: "يتصرف شريكك بغموض حول هاتفه. ماذا يدور في ذهنك؟"
    },
    answers: [
      { text: "ربما تخطط لمفاجأة لي.", points: 1 },
      { text: {
          male: "أفترض أن لديها خصوصيتها التي يجب أن أحترمها.",
          female: "أفترض أن لديه خصوصيته التي يجب أن أحترمها."
        }, points: 2 },
      { text: {
          male: "أشعر بالشك وأتساءل عما تخفيه.",
          female: "أشعر بالشك وأتساءل عما يخفيه."
        }, points: 3 },
      { text: {
          male: "أعتقد أنها تخونني وأبحث عن فرصة لتفقد هاتفها.",
          female: "أعتقد أنه يخونني وأبحث عن فرصة لتفقد هاتفه."
        }, points: 4 },
    ],
  },
   {
    text: {
        male: "تذكر شريكتك اسم زميل لها في العمل بشكل متكرر. ما رد فعلك؟",
        female: "يذكر شريكك اسم زميلة له في العمل بشكل متكرر. ما رد فعلك؟"
    },
    answers: [
      { text: "أستمع باهتمام، إنه جزء من حياتها اليومية.", points: 1 },
      { text: {
          male: "أسأل المزيد عن هذا الزميل من باب الفضول.",
          female: "أسأل المزيد عن هذه الزميلة من باب الفضول."
        }, points: 2 },
      { text: "أشعر بالريبة وأحاول تحليل سبب ذكر هذا الاسم كثيرًا.", points: 3 },
      { text: {
          male: "أشعر بالانزعاج وأطلب منها التوقف عن ذكر هذا الاسم.",
          female: "أشعر بالانزعاج وأطلب منه التوقف عن ذكر هذا الاسم."
        }, points: 4 },
    ],
  },
  {
    text: {
        male: "هل سبق لك أن فتشت في ممتلكات شريكتك (هاتف، بريد إلكتروني) بدون علمها؟",
        female: "هل سبق لكِ أن فتشتِ في ممتلكات شريكك (هاتف، بريد إلكتروني) بدون علمه؟"
    },
    answers: [
      { text: {
          male: "أبدًا، أنا أثق بها وأحترم خصوصيتها.",
          female: "أبدًا، أنا أثق به وأحترم خصوصيته."
        }, points: 1 },
      { text: "فكرت في الأمر لكني لم أفعله أبدًا.", points: 2 },
      { text: "لقد فعلت ذلك مرة أو مرتين عندما شعرت بالشك الشديد.", points: 3 },
      { text: "أفعل ذلك بانتظام لأطمئن.", points: 4 },
    ],
  },
  // Comparison
  {
    text: "عندما ترى أزواجًا آخرين يبدون سعداء، كيف تشعر تجاه علاقتك؟",
    answers: [
      { text: "أشعر بالسعادة لهم، وعلاقتي مستقلة.", points: 1 },
      { text: "أستلهم منهم بعض الأفكار الإيجابية.", points: 2 },
      { text: "أبدأ في مقارنة علاقتي بهم وأشعر ببعض النقص.", points: 3 },
      { text: "أشعر بالغيرة وأعتقد أن علاقتهم أفضل من علاقتي.", points: 4 },
    ],
  },
  {
    text: {
        male: "تحقق شريكتك نجاحًا كبيرًا في عملها. ما هو شعورك الأولي؟",
        female: "يحقق شريكك نجاحًا كبيرًا في عمله. ما هو شعورك الأولي؟"
    },
    answers: [
      { text: {
          male: "أشعر بالفخر والسعادة الخالصة من أجلها.",
          female: "أشعر بالفخر والسعادة الخالصة من أجله."
        }, points: 1 },
      { text: "أشعر بالسعادة، وربما بالقليل من الضغط لأحقق نجاحًا مماثلاً.", points: 2 },
      { text: {
          male: "أشعر بالسعادة لها، ولكني أقلق من أن هذا النجاح قد يغيرها أو يبعدها عني.",
          female: "أشعر بالسعادة له، لكني أقلق من أن هذا النجاح قد يغيره أو يبعده عني."
        }, points: 3 },
      { text: "أشعر بالغيرة وأخشى أن تتفوق عليّ.", points: 4 },
    ],
  },
  {
    text: {
        male: "هل تقارن مقدار الحب أو الهدايا التي تقدمها شريكتك بما يقدمه أصدقاؤك لشركائهم؟",
        female: "هل تقارنين مقدار الحب أو الهدايا التي يقدمها شريكك بما يقدمه أصدقائك لشركائهم؟"
    },
    answers: [
      { text: "لا، علاقتنا فريدة ولها طريقتها الخاصة في التعبير.", points: 1 },
      { text: "نادرًا، ربما إذا رأيت شيئًا مميزًا جدًا.", points: 2 },
      { text: "أحيانًا، خاصة إذا شعرت بالإهمال.", points: 3 },
      { text: "كثيرًا، وأشعر بالاستياء إذا شعرت أني أحصل على أقل.", points: 4 },
    ],
  },
  // General
  {
    text: {
        male: "كيف تصف مستوى ثقتك في شريكتك بشكل عام؟",
        female: "كيف تصفين مستوى ثقتك في شريكك بشكل عام؟"
    },
    answers: [
      { text: {
          male: "أثق بها تمامًا وبدون تحفظات.",
          female: "أثق به تمامًا وبدون تحفظات."
        }, points: 1 },
      { text: {
          male: "أثق بها، ولكن لدي بعض المخاوف البسيطة أحيانًا.",
          female: "أثق به، ولكن لدي بعض المخاوف البسيطة أحيانًا."
        }, points: 2 },
      { text: "أجد صعوبة في الثقة الكاملة وأحتاج إلى تأكيدات مستمرة.", points: 3 },
      { text: {
          male: "أشك في نواياها باستمرار وأجد صعوبة في تصديقها.",
          female: "أشك في نواياه باستمرار وأجد صعوبة في تصديقه."
        }, points: 4 },
    ],
  },
  {
    text: {
        male: "تغير شريكتك مظهرها بشكل ملحوظ (ملابس جديدة، قصة شعر). ما هو أول ما يتبادر إلى ذهنك؟",
        female: "يغير شريكك مظهره بشكل ملحوظ (ملابس جديدة، قصة شعر). ما هو أول ما يتبادر إلى ذهنك؟"
    },
    answers: [
      { text: "أنها تريد التجديد والشعور بالرضا عن نفسها.", points: 1 },
      { text: {
          male: "أعجب بالمظهر الجديد وأشجعها.",
          female: "أعجب بالمظهر الجديد وأشجعه."
        }, points: 2 },
      { text: "أتساءل ما إذا كانت تحاول جذب انتباه شخص آخر.", points: 3 },
      { text: "أشعر بالتهديد وأبدأ بالبحث عن سبب هذا التغيير المفاجئ.", points: 4 },
    ],
  },
  {
    text: "كيف تتعامل مع الخلافات المتعلقة بالغيرة في علاقتك؟",
    answers: [
      { text: {
          male: "نتحدث بهدوء ونوضح سوء الفهم.",
          female: "نتحدث بهدوء ونوضح سوء الفهم."
        }, points: 1 },
      { text: {
          male: "أعبر عن مشاعري وأحاول الاستماع لوجهة نظرها.",
          female: "أعبر عن مشاعري وأحاول الاستماع لوجهة نظره."
        }, points: 2 },
      { text: "غالبًا ما ينتهي الأمر بجدال وأجد صعوبة في تجاوز الأمر.", points: 3 },
      { text: "أتمسك بموقفي وأعتقد دائمًا أن شكوكي في محلها.", points: 4 },
    ],
  },
  {
    text: {
        male: "هل تعتقد أن القليل من الغيرة صحي للعلاقة؟",
        female: "هل تعتقدين أن القليل من الغيرة صحي للعلاقة؟"
    },
    answers: [
      { text: "لا، الثقة الكاملة هي الأهم.", points: 1 },
      { text: "نعم، القليل منها يظهر الاهتمام.", points: 2 },
      { text: "نعم، وأعتقد أنها ضرورية للحفاظ على الشغف.", points: 3 },
      { text: {
          male: "نعم، وإذا لم تغار شريكتي، فهذا يعني أنها لا تحبني.",
          female: "نعم، وإذا لم يغار شريكي، فهذا يعني أنه لا يحبني."
        }, points: 4 },
    ],
  },
];

const getResult = (score: number, gender: Gender) => {
  const results = {
    level1: {
      title: "ثقة عالية",
      male: "أنت شخص واثق جدًا في علاقتك ونادرًا ما تشعر بالغيرة. ثقتك بنفسك وبشريكتك هي أساس علاقتكما القوية. استمر في الحفاظ على هذا التواصل المفتوح.",
      female: "أنتِ شخص واثق جدًا في علاقتك ونادرًا ما تشعرين بالغيرة. ثقتك بنفسك وبشريكك هي أساس علاقتكما القوية. استمري في الحفاظ على هذا التواصل المفتوح."
    },
    level2: {
      title: "غيرة معتدلة",
      male: "لديك مستوى صحي وطبيعي من الغيرة. أنت تهتم بشريكتك ولكنك لا تدع مشاعر الغيرة تسيطر على علاقتك. هذا التوازن جيد للحفاظ على علاقة صحية.",
      female: "لديكِ مستوى صحي وطبيعي من الغيرة. أنتِ تهتمين بشريكك ولكنكِ لا تدعين مشاعر الغيرة تسيطر على علاقتك. هذا التوازن جيد للحفاظ على علاقة صحية."
    },
    level3: {
      title: "غيرة ملحوظة",
      male: "تميل إلى الشعور بالغيرة بشكل متكرر، وقد يؤثر ذلك على علاقتك. قد يكون من المفيد التحدث مع شريكتك حول مشاعرك والعمل على بناء الثقة لتجنب الشكوك غير المبررة.",
      female: "تميلين إلى الشعور بالغيرة بشكل متكرر، وقد يؤثر ذلك على علاقتك. قد يكون من المفيد التحدث مع شريكك حول مشاعرك والعمل على بناء الثقة لتجنب الشكوك غير المبررة."
    },
    level4: {
      title: "غيرة شديدة",
      male: "مشاعر الغيرة لديك قوية جدًا وقد تكون ضارة بعلاقتك وصحتك النفسية. من المهم التفكير في أسباب هذه الغيرة العميقة والعمل على معالجتها، ربما بمساعدة متخصص، للحفاظ على علاقة صحية ومستقرة.",
      female: "مشاعر الغيرة لديكِ قوية جدًا وقد تكون ضارة بعلاقتك وصحتك النفسية. من المهم التفكير في أسباب هذه الغيرة العميقة والعمل على معالجتها، ربما بمساعدة متخصص، للحفاظ على علاقة صحية ومستقرة."
    }
  };

  if (score <= 35) {
    return { level: results.level1.title, description: results.level1[gender] };
  }
  if (score <= 50) {
    return { level: results.level2.title, description: results.level2[gender] };
  }
  if (score <= 65) {
    return { level: results.level3.title, description: results.level3[gender] };
  }
  return { level: results.level4.title, description: results.level4[gender] };
};

type TestStage = 'gender' | 'questions' | 'result';

// Helper to shuffle an array
function shuffleArray<T>(array: T[]): T[] {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}


export function JealousyTestTool() {
  const [stage, setStage] = useState<TestStage>('gender');
  const [gender, setGender] = useState<Gender | null>(null);
  const [shuffledQuestions, setShuffledQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  
  const isFinished = stage === 'result';
  const totalScore = answers.reduce((sum, score) => sum + score, 0);
  const result = isFinished && gender ? getResult(totalScore, gender) : { level: '', description: '' };

  const startTest = (selectedGender: Gender) => {
    setGender(selectedGender);
    setAnswers([]);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setShuffledQuestions(shuffleArray(questions));
    setStage('questions');
  };

  const handleNextQuestion = () => {
    if (selectedAnswer === null) return;
    
    const newAnswers = [...answers, selectedAnswer];
    setAnswers(newAnswers);
    setSelectedAnswer(null);

    if (currentQuestionIndex < shuffledQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      setStage('result');
    }
  };

  const handleRestart = () => {
    setStage('gender');
    setGender(null);
  };
  
  const progress = isFinished ? 100 : ((currentQuestionIndex) / shuffledQuestions.length) * 100;
  
  const currentQuestion = shuffledQuestions[currentQuestionIndex];
  
  const shuffledAnswers = useMemo(() => {
    if (!currentQuestion) return [];
    return shuffleArray(currentQuestion.answers);
  }, [currentQuestion]);


  const renderContent = () => {
    switch(stage) {
      case 'gender':
        return (
          <div className="text-center space-y-6">
            <h3 className="text-lg font-semibold">قبل أن نبدأ، يرجى تحديد جنسك</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Button variant="outline" className="h-24 text-xl" onClick={() => startTest('male')}>
                <User className="ml-3 h-8 w-8" />
                ذكر
              </Button>
              <Button variant="outline" className="h-24 text-xl" onClick={() => startTest('female')}>
                <UserRound className="ml-3 h-8 w-8" />
                أنثى
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">هذا يساعدنا على تقديم تجربة أفضل في المستقبل.</p>
          </div>
        );
      
      case 'questions':
        if (!gender || !currentQuestion) return null;
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-center">{getQuestionText(currentQuestion, gender)}</h3>
            <RadioGroup
              key={currentQuestionIndex}
              value={selectedAnswer?.toString() || ""}
              onValueChange={(value) => setSelectedAnswer(parseInt(value, 10))}
              className="space-y-3"
            >
              {shuffledAnswers.map((answer, index) => (
                <Label 
                  key={index} 
                  dir="rtl"
                  className="flex items-center justify-between gap-x-3 p-4 border rounded-lg cursor-pointer hover:bg-muted/50 has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary"
                >
                  <span className="flex-1 text-right">{getAnswerText(answer, gender)}</span>
                  <RadioGroupItem 
                    value={String(answer.points)} 
                    id={`q${currentQuestionIndex}-a${index}`} 
                  />
                </Label>
              ))}
            </RadioGroup>
            <Button onClick={handleNextQuestion} disabled={selectedAnswer === null} className="w-full">
              {currentQuestionIndex === questions.length - 1 ? 'عرض النتيجة' : 'التالي'}
            </Button>
          </div>
        );
      case 'result':
        return (
          <div className="text-center space-y-4">
            <h3 className="text-2xl font-bold text-primary">{result.level}</h3>
            <p className="text-muted-foreground leading-relaxed">{result.description}</p>
            <p className="text-sm text-muted-foreground pt-4 border-t">إخلاء مسؤولية: هذا الاختبار هو لأغراض الترفيه والتثقيف الذاتي فقط ولا يمثل تشخيصًا نفسيًا.</p>
            <Button onClick={handleRestart} variant="outline" className="w-full">
                <Repeat className="ml-2 h-4 w-4" />
                أعد الاختبار
            </Button>
          </div>
        );
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
            <HeartCrack className="h-6 w-6 text-primary" />
            اختبار الغيرة
        </CardTitle>
        <CardDescription>
            أجب عن الأسئلة التالية بصدق لاكتشاف مستوى الغيرة في شخصيتك.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {stage !== 'gender' && (
             <div className="mb-6">
                <Progress value={progress} className="w-full" />
                <p className="text-sm text-muted-foreground mt-2 text-center">
                    {isFinished ? 'اكتمل الاختبار!' : `السؤال ${currentQuestionIndex + 1} من ${shuffledQuestions.length}`}
                </p>
            </div>
        )}
       
        {renderContent()}

      </CardContent>
    </Card>
  );
}
