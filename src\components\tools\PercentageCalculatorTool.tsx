
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

// نموذج حساب النسبة المئوية من رقم
const PercentOfNumberSchema = z.object({
  percent: requiredNumber().min(0, { message: 'يجب أن تكون النسبة أكبر من أو تساوي صفر' }),
  number: requiredNumber().min(0, { message: 'يجب أن يكون الرقم أكبر من أو يساوي صفر' }),
});

// نموذج حساب النسبة بين رقمين
const PercentBetweenNumbersSchema = z.object({
  part: requiredNumber(),
  whole: requiredNumber().refine(val => val !== 0, { message: 'لا يمكن أن يكون الرقم الكلي صفرًا' }),
});

// نموذج حساب الزيادة أو النقصان بالنسبة المئوية
const PercentChangeSchema = z.object({
  originalValue: requiredNumber().min(0, { message: 'يجب أن تكون القيمة الأصلية أكبر من أو تساوي صفر' }),
  percentChange: requiredNumber(),
});

// نموذج حساب المبلغ بعد الزيادة أو النقصان
const ValueAfterChangeSchema = z.object({
  originalValue: requiredNumber().min(0, { message: 'يجب أن تكون القيمة الأصلية أكبر من أو تساوي صفر' }),
  newValue: requiredNumber().min(0, { message: 'يجب أن تكون القيمة الجديدة أكبر من أو تساوي صفر' }),
});

export function PercentageCalculatorTool() {
  // إعداد النماذج للحسابات المختلفة
  const percentOfNumberForm = useForm<z.infer<typeof PercentOfNumberSchema>>({
    resolver: zodResolver(PercentOfNumberSchema),
    defaultValues: { percent: 0, number: 0 },
  });

  const percentBetweenNumbersForm = useForm<z.infer<typeof PercentBetweenNumbersSchema>>({
    resolver: zodResolver(PercentBetweenNumbersSchema),
    defaultValues: { part: 0, whole: 0 },
  });

  const percentChangeForm = useForm<z.infer<typeof PercentChangeSchema>>({
    resolver: zodResolver(PercentChangeSchema),
    defaultValues: { originalValue: 0, percentChange: 0 },
  });

  const valueAfterChangeForm = useForm<z.infer<typeof ValueAfterChangeSchema>>({
    resolver: zodResolver(ValueAfterChangeSchema),
    defaultValues: { originalValue: 0, newValue: 0 },
  });

  // متغيرات لتخزين النتائج
  const [percentOfNumberResult, setPercentOfNumberResult] = useState<number | null>(null);
  const [percentBetweenNumbersResult, setPercentBetweenNumbersResult] = useState<number | null>(null);
  const [percentChangeResult, setPercentChangeResult] = useState<number | null>(null);
  const [valueAfterChangeResult, setValueAfterChangeResult] = useState<number | null>(null);

  // دالة لحساب النسبة المئوية من رقم
  function calculatePercentOfNumber(data: z.infer<typeof PercentOfNumberSchema>) {
    const result = (data.percent / 100) * data.number;
    setPercentOfNumberResult(result);
  }

  // دالة لحساب النسبة بين رقمين
  function calculatePercentBetweenNumbers(data: z.infer<typeof PercentBetweenNumbersSchema>) {
    const result = (data.part / data.whole) * 100;
    setPercentBetweenNumbersResult(result);
  }

  // دالة لحساب القيمة بعد التغيير بنسبة مئوية
  function calculatePercentChange(data: z.infer<typeof PercentChangeSchema>) {
    const result = data.originalValue * (1 + data.percentChange / 100);
    setPercentChangeResult(result);
  }

  // دالة لحساب نسبة التغيير بين قيمتين
  function calculateValueAfterChange(data: z.infer<typeof ValueAfterChangeSchema>) {
    const result = ((data.newValue - data.originalValue) / data.originalValue) * 100;
    setValueAfterChangeResult(result);
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>حاسبة النسبة المئوية</CardTitle>
        <CardDescription>أداة لإجراء كافة أنواع حسابات النسبة المئوية</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="percentOfNumber" className="w-full">
          <TabsList className="grid grid-cols-4">
            <TabsTrigger value="percentOfNumber">النسبة من رقم</TabsTrigger>
            <TabsTrigger value="percentBetweenNumbers">النسبة بين رقمين</TabsTrigger>
            <TabsTrigger value="percentChange">حساب الزيادة</TabsTrigger>
            <TabsTrigger value="valueAfterChange">نسبة التغيير</TabsTrigger>
          </TabsList>
          
          {/* حساب النسبة المئوية من رقم */}
          <TabsContent value="percentOfNumber">
            <Form {...percentOfNumberForm}>
              <form onSubmit={percentOfNumberForm.handleSubmit(calculatePercentOfNumber)} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={percentOfNumberForm.control}
                    name="percent"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>النسبة المئوية (%)</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={percentOfNumberForm.control}
                    name="number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>من الرقم</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <Button type="submit" className="w-full">
                  احسب
                </Button>
                
                {percentOfNumberResult !== null && (
                  <div className="mt-4 p-4 bg-primary/10 rounded-lg text-center">
                    <p className="text-sm text-muted-foreground">النتيجة</p>
                    <p className="text-3xl font-bold font-mono text-primary">
                      {percentOfNumberResult.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 })}
                    </p>
                    <p className="text-sm text-muted-foreground mt-2">
                      {percentOfNumberForm.getValues().percent}% من {percentOfNumberForm.getValues().number} هو {percentOfNumberResult.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 })}
                    </p>
                  </div>
                )}
              </form>
            </Form>
          </TabsContent>
          
          {/* حساب النسبة بين رقمين */}
          <TabsContent value="percentBetweenNumbers">
            <Form {...percentBetweenNumbersForm}>
              <form onSubmit={percentBetweenNumbersForm.handleSubmit(calculatePercentBetweenNumbers)} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={percentBetweenNumbersForm.control}
                    name="part"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الجزء</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={percentBetweenNumbersForm.control}
                    name="whole"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الكل</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <Button type="submit" className="w-full">
                  احسب
                </Button>
                
                {percentBetweenNumbersResult !== null && (
                  <div className="mt-4 p-4 bg-primary/10 rounded-lg text-center">
                    <p className="text-sm text-muted-foreground">النتيجة</p>
                    <p className="text-3xl font-bold font-mono text-primary">
                      {percentBetweenNumbersResult.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 })}%
                    </p>
                    <p className="text-sm text-muted-foreground mt-2">
                      {percentBetweenNumbersForm.getValues().part} هو {percentBetweenNumbersResult.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 })}% من {percentBetweenNumbersForm.getValues().whole}
                    </p>
                  </div>
                )}
              </form>
            </Form>
          </TabsContent>
          
          {/* حساب الزيادة أو النقصان بالنسبة المئوية */}
          <TabsContent value="percentChange">
            <Form {...percentChangeForm}>
              <form onSubmit={percentChangeForm.handleSubmit(calculatePercentChange)} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={percentChangeForm.control}
                    name="originalValue"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>القيمة الأصلية</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={percentChangeForm.control}
                    name="percentChange"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>نسبة التغيير (%)</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormDescription>
                          أدخل رقمًا موجبًا للزيادة أو سالبًا للنقصان
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <Button type="submit" className="w-full">
                  احسب
                </Button>
                
                {percentChangeResult !== null && (
                  <div className="mt-4 p-4 bg-primary/10 rounded-lg text-center">
                    <p className="text-sm text-muted-foreground">النتيجة</p>
                    <p className="text-3xl font-bold font-mono text-primary">
                      {percentChangeResult.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 })}
                    </p>
                    <p className="text-sm text-muted-foreground mt-2">
                      {percentChangeForm.getValues().originalValue} {percentChangeForm.getValues().percentChange >= 0 ? 'زائد' : 'ناقص'} {Math.abs(percentChangeForm.getValues().percentChange)}% يساوي {percentChangeResult.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 })}
                    </p>
                  </div>
                )}
              </form>
            </Form>
          </TabsContent>
          
          {/* حساب نسبة التغيير بين قيمتين */}
          <TabsContent value="valueAfterChange">
            <Form {...valueAfterChangeForm}>
              <form onSubmit={valueAfterChangeForm.handleSubmit(calculateValueAfterChange)} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={valueAfterChangeForm.control}
                    name="originalValue"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>القيمة الأصلية</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={valueAfterChangeForm.control}
                    name="newValue"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>القيمة الجديدة</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <Button type="submit" className="w-full">
                  احسب
                </Button>
                
                {valueAfterChangeResult !== null && (
                  <div className="mt-4 p-4 bg-primary/10 rounded-lg text-center">
                    <p className="text-sm text-muted-foreground">النتيجة</p>
                    <p className="text-3xl font-bold font-mono text-primary">
                      {valueAfterChangeResult > 0 ? '+' : ''}{valueAfterChangeResult.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 })}%
                    </p>
                    <p className="text-sm text-muted-foreground mt-2">
                      التغيير من {valueAfterChangeForm.getValues().originalValue} إلى {valueAfterChangeForm.getValues().newValue} هو {valueAfterChangeResult > 0 ? 'زيادة' : 'نقصان'} بنسبة {Math.abs(valueAfterChangeResult).toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 })}%
                    </p>
                  </div>
                )}
              </form>
            </Form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
