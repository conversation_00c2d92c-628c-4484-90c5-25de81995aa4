import { Metadata } from 'next';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PageHeader } from '@/components/PageHeader';
import { toolCategories } from '@/lib/tools';
import { ArrowLeft } from 'lucide-react';

export const metadata: Metadata = {
  title: 'خريطة الموقع',
  description: 'خريطة شاملة لجميع صفحات وأدوات موقع أدوات بالعربي',
  keywords: ['خريطة الموقع', 'فهرس الصفحات', 'أدوات عربية', 'دليل الموقع'],
};

export default function SitemapPage() {
  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6 md:p-8">
      <PageHeader 
        title="خريطة الموقع" 
        description="دليل شامل لجميع صفحات وأدوات موقع أدوات بالعربي"
      />
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* الصفحات الرئيسية */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📄 الصفحات الرئيسية
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Link href="/" className="flex items-center justify-between p-2 rounded hover:bg-muted group">
                <span>الصفحة الرئيسية</span>
                <ArrowLeft className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
              <Link href="/articles" className="flex items-center justify-between p-2 rounded hover:bg-muted group">
                <span>المقالات والنصائح</span>
                <ArrowLeft className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
              <Link href="/sitemap-page" className="flex items-center justify-between p-2 rounded hover:bg-muted group">
                <span>خريطة الموقع</span>
                <ArrowLeft className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* الصفحات القانونية */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              ⚖️ الصفحات القانونية
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Link href="/p/privacy-policy" className="flex items-center justify-between p-2 rounded hover:bg-muted group">
                <span>سياسة الخصوصية</span>
                <ArrowLeft className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
              <Link href="/p/terms-of-service" className="flex items-center justify-between p-2 rounded hover:bg-muted group">
                <span>شروط الخدمة</span>
                <ArrowLeft className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
              <Link href="/disclaimer" className="flex items-center justify-between p-2 rounded hover:bg-muted group">
                <span>إخلاء المسؤولية</span>
                <ArrowLeft className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
              <Link href="/p/about" className="flex items-center justify-between p-2 rounded hover:bg-muted group">
                <span>حول الموقع</span>
                <ArrowLeft className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
              <Link href="/p/contact" className="flex items-center justify-between p-2 rounded hover:bg-muted group">
                <span>اتصل بنا</span>
                <ArrowLeft className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
              <Link href="/p/cookies" className="flex items-center justify-between p-2 rounded hover:bg-muted group">
                <span>سياسة الكوكيز</span>
                <ArrowLeft className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* فئات الأدوات */}
      <div className="mt-8">
        <h2 className="text-2xl font-headline font-bold mb-6">فئات الأدوات</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {toolCategories.map((category) => {
            const availableTools = category.tools.filter(tool => tool.component);
            if (availableTools.length === 0) return null;

            return (
              <Card key={category.slug}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <category.icon className="w-5 h-5" />
                    {category.name}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-1">
                    <Link 
                      href={`/categories/${category.slug}`}
                      className="flex items-center justify-between p-2 rounded hover:bg-muted group text-sm font-medium text-primary"
                    >
                      <span>عرض جميع أدوات {category.name}</span>
                      <ArrowLeft className="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                    </Link>
                    {availableTools.slice(0, 5).map((tool) => (
                      <Link
                        key={tool.name}
                        href={tool.path}
                        className="flex items-center justify-between p-2 rounded hover:bg-muted group text-sm"
                      >
                        <span>{tool.name}</span>
                        <ArrowLeft className="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                      </Link>
                    ))}
                    {availableTools.length > 5 && (
                      <div className="text-xs text-muted-foreground p-2">
                        و {availableTools.length - 5} أدوات أخرى...
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* المقالات */}
      <div className="mt-8">
        <h2 className="text-2xl font-headline font-bold mb-6">المقالات المتاحة</h2>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📚 مقالات ونصائح مفيدة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Link href="/articles/how-to-calculate-zakat" className="flex items-center justify-between p-3 rounded border hover:bg-muted group">
                <div>
                  <div className="font-medium">كيفية حساب الزكاة بطريقة صحيحة</div>
                  <div className="text-sm text-muted-foreground">دليل شامل لحساب الزكاة</div>
                </div>
                <ArrowLeft className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
              
              <Link href="/articles/hijri-calendar-guide" className="flex items-center justify-between p-3 rounded border hover:bg-muted group">
                <div>
                  <div className="font-medium">دليل شامل للتقويم الهجري والميلادي</div>
                  <div className="text-sm text-muted-foreground">الفروق والتحويل بين التقويمين</div>
                </div>
                <ArrowLeft className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* معلومات إضافية */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>معلومات إضافية</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-bold mb-2">الملفات التقنية</h4>
                <div className="space-y-1 text-sm">
                  <div>• <a href="/robots.txt" className="text-primary hover:underline">robots.txt</a></div>
                  <div>• <a href="/sitemap.xml" className="text-primary hover:underline">sitemap.xml</a></div>
                  <div>• <a href="/manifest.json" className="text-primary hover:underline">manifest.json</a></div>
                  <div>• <a href="/.well-known/security.txt" className="text-primary hover:underline">security.txt</a></div>
                </div>
              </div>
              
              <div>
                <h4 className="font-bold mb-2">إحصائيات الموقع</h4>
                <div className="space-y-1 text-sm">
                  <div>• عدد الفئات: {toolCategories.length}</div>
                  <div>• عدد الأدوات: {toolCategories.reduce((total, cat) => total + cat.tools.filter(t => t.component).length, 0)}</div>
                  <div>• عدد المقالات: 2+</div>
                  <div>• آخر تحديث: {new Date().toLocaleDateString('ar-SA-u-nu-latn')}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
