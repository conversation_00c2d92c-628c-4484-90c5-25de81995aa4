
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Copy, Check, Wand2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';

const invisibleCharacters = [
  { name: 'مساحة بدون فاصل', unicode: 'U+00A0', character: '\u00A0' },
  { name: 'مساحة إم', unicode: 'U+2003', character: '\u2003' },
  { name: 'مساحة إن', unicode: 'U+2002', character: '\u2002' },
  { name: 'مساحة رقيقة', unicode: 'U+2009', character: '\u2009' },
  { name: 'مسافة عرض صفر', unicode: 'U+200B', character: '\u200B' },
  { name: 'مساحة الشكل', unicode: 'U+2007', character: '\u2007' },
  { name: 'فاصل الأسطر', unicode: 'U+2028', character: '\u2028' },
  { name: 'الفضاء الأيديوغرافي', unicode: 'U+3000', character: '\u3000' },
];

export function InvisibleCharacterTool() {
  const { toast } = useToast();
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [mainCopied, setMainCopied] = useState(false);

  const [charCount, setCharCount] = useState<number>(10);
  const [generatedText, setGeneratedText] = useState<string>('');
  const [generatedCopied, setGeneratedCopied] = useState(false);
  const [testText, setTestText] = useState('');

  const copyToClipboard = (text: string, index?: number, isMain?: boolean) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: "تم النسخ بنجاح!",
        description: `تم نسخ الحرف المخفي إلى الحافظة.`,
      });
      if (isMain) {
        setMainCopied(true);
        setTimeout(() => setMainCopied(false), 2000);
      } else if (index !== undefined) {
        setCopiedIndex(index);
        setTimeout(() => setCopiedIndex(null), 2000);
      } else {
        setGeneratedCopied(true);
        setTimeout(() => setGeneratedCopied(false), 2000);
      }
    }, (err) => {
      toast({
        variant: 'destructive',
        title: 'فشل النسخ',
        description: 'لم نتمكن من نسخ النص إلى الحافظة.',
      });
    });
  };

  const handleGenerate = () => {
    let result = '';
    if (charCount > 0 && charCount <= 5000) {
      for (let i = 0; i < charCount; i++) {
        const randomIndex = Math.floor(Math.random() * invisibleCharacters.length);
        result += invisibleCharacters[randomIndex].character;
      }
      setGeneratedText(result);
    } else {
        toast({
            variant: 'destructive',
            title: 'عدد غير صالح',
            description: 'الرجاء إدخال عدد بين 1 و 5000.',
        });
    }
  };

  return (
    <Card className="w-full max-w-3xl">
      <CardHeader>
        <CardTitle>مولد الحرف المخفي (نص فارغ)</CardTitle>
        <CardDescription>
          انسخ حرف مخفي أو اسم مخفي لاستخدامه في منصات التواصل الاجتماعي والألعاب.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        <div className="p-6 bg-secondary/50 rounded-lg text-center">
          <h3 className="text-lg font-medium mb-2">نسخ سريع</h3>
          <p className="text-muted-foreground mb-4">انقر على الزر لنسخ الحرف المخفي الأكثر شيوعًا.</p>
          <Button size="lg" onClick={() => copyToClipboard('\u200B', undefined, true)}>
            {mainCopied ? <Check className="ml-2 h-4 w-4" /> : <Copy className="ml-2 h-4 w-4" />}
            {mainCopied ? 'تم النسخ!' : 'انسخ الحرف المخفي'}
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-xl">مولد عشوائي</CardTitle>
            <CardDescription>أنشئ سلسلة عشوائية من الحروف المخفية بالطول الذي تريده.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <label htmlFor="char-count" className="text-sm font-medium mb-2 block">
                  عدد الحروف
                </label>
                <Input
                  id="char-count"
                  type="number"
                  value={charCount}
                  onChange={(e) => setCharCount(parseInt(e.target.value, 10))}
                  min="1"
                  max="5000"
                  placeholder="مثال: 10"
                />
              </div>
              <Button onClick={handleGenerate} className="self-end h-10">
                <Wand2 className="ml-2 h-4 w-4" />
                توليد
              </Button>
            </div>
            {generatedText && (
              <div>
                <div className="flex justify-between items-center mb-2">
                    <label className="text-sm font-medium">النتيجة المولدة:</label>
                    <Button variant="outline" size="sm" onClick={() => copyToClipboard(generatedText)}>
                        {generatedCopied ? <Check className="ml-2 h-4 w-4" /> : <Copy className="ml-2 h-4 w-4" />}
                        {generatedCopied ? 'تم النسخ!' : 'نسخ'}
                    </Button>
                </div>
                <Textarea
                  readOnly
                  value={generatedText}
                  className="min-h-[100px] bg-muted"
                  placeholder="ستظهر السلسلة العشوائية هنا"
                />
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">اختباره</CardTitle>
            <CardDescription>
              قم بلصق الحرف الفارغ في منطقة النص أدناه لاختباره. إذا نجح الأمر، فيجب أن يختفي النص الرمادي الفاتح.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              value={testText}
              onChange={(e) => setTestText(e.target.value)}
              placeholder="الصق هنا للاختبار"
              className="min-h-[100px]"
            />
            <p className="text-sm text-muted-foreground mt-2 text-left">
              الأحرف: {testText.length}
            </p>
          </CardContent>
        </Card>

        <div>
            <h3 className="text-lg font-medium mb-2">خيارات إضافية</h3>
            <p className="text-muted-foreground mb-4">
            تحتوي بعض الأحرف على عرض مرئي (مسافة) بينما يكون البعض الآخر غير مرئي تمامًا.
            </p>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>الوصف</TableHead>
                    <TableHead>اليونيكود</TableHead>
                    <TableHead className="text-left">نسخ</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invisibleCharacters.map((char, index) => (
                    <TableRow key={char.unicode}>
                      <TableCell className="font-medium">{char.name}</TableCell>
                      <TableCell className="font-mono text-left" dir="ltr">{char.unicode}</TableCell>
                      <TableCell className="text-left">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(char.character, index)}
                        >
                          {copiedIndex === index ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
        </div>
      </CardContent>
    </Card>
  );
}
