
'use client';

import { useState, useEffect, useMemo } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { CheckCircle, XCircle, ChevronRight, BookOpen, BrainCircuit, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useForm, FormProvider, useFormContext } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

const QuizSchema = z.object({
  userAnswer: z.string(),
});

export function MultiplicationTableTool() {
  const [selectedNumber, setSelectedNumber] = useState<number>(1);
  const numbers = Array.from({ length: 10 }, (_, i) => i + 1);

  // State for the quiz
  const [currentQuestion, setCurrentQuestion] = useState({ num1: 1, num2: 1 });
  const [feedback, setFeedback] = useState<{ message: string; isCorrect: boolean } | null>(null);
  const [score, setScore] = useState({ correct: 0, incorrect: 0 });
  
  const methods = useForm<z.infer<typeof QuizSchema>>({
    resolver: zodResolver(QuizSchema),
    defaultValues: { userAnswer: '' },
  });

  const generateNewQuestion = (isInitial = false) => {
    let num1, num2;
    do {
      num1 = numbers[Math.floor(Math.random() * numbers.length)];
      num2 = numbers[Math.floor(Math.random() * numbers.length)];
    } while (!isInitial && num1 === currentQuestion.num1 && num2 === currentQuestion.num2);
    
    setCurrentQuestion({ num1, num2 });
    methods.reset({ userAnswer: '' });
    setFeedback(null);
  };
  
  useEffect(() => {
    generateNewQuestion(true);
  }, []);

  const handleNextQuestion = (data: z.infer<typeof QuizSchema>) => {
    const userAnswer = data.userAnswer;
    if (userAnswer === null || userAnswer.trim() === '') {
        generateNewQuestion();
        return;
    }
    
    const correctAnswer = currentQuestion.num1 * currentQuestion.num2;
    const isCorrect = parseInt(userAnswer, 10) === correctAnswer;

    if (isCorrect) {
      setFeedback({ message: 'إجابة صحيحة!', isCorrect: true });
      setScore(prev => ({ ...prev, correct: prev.correct + 1 }));
    } else {
      setFeedback({ message: `خطأ، الإجابة الصحيحة هي ${correctAnswer}`, isCorrect: false });
      setScore(prev => ({ ...prev, incorrect: prev.incorrect + 1 }));
    }
    
    setTimeout(() => {
        generateNewQuestion();
    }, 1200);
  };
  
  const resetScore = () => {
    setScore({ correct: 0, incorrect: 0 });
    generateNewQuestion(true);
  };


  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>جدول الضرب</CardTitle>
        <CardDescription>
          تعلم وتدرب على حفظ جداول الضرب من 1 إلى 10.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="view-table" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="view-table"><BookOpen className="ml-2 h-4 w-4" />عرض الجداول</TabsTrigger>
                <TabsTrigger value="test-yourself"><BrainCircuit className="ml-2 h-4 w-4" />اختبر حفظك</TabsTrigger>
            </TabsList>
            <TabsContent value="view-table" className="pt-4">
               <div className="mb-6">
                  <label className="text-sm font-medium mb-2 block text-right">
                    : اختر جدول الضرب المطلوب لعرضه
                  </label>
                  <div className="grid grid-cols-5 md:grid-cols-10 gap-2">
                    {numbers.map((num) => (
                      <Button
                        key={num}
                        variant={selectedNumber === num ? "default" : "outline"}
                        onClick={() => setSelectedNumber(num)}
                        className="text-lg font-bold"
                      >
                        {num}
                      </Button>
                    ))}
                  </div>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4 text-center">
                {numbers.map((multiplier) => (
                    <div
                    key={multiplier}
                    className="p-4 bg-muted rounded-lg border transition-all hover:bg-primary/10 hover:shadow-md"
                    >
                    <p className="font-mono text-lg text-muted-foreground">
                        {selectedNumber} × {multiplier}
                    </p>
                    <p className="text-3xl font-bold font-mono text-primary">
                        {selectedNumber * multiplier}
                    </p>
                    </div>
                ))}
                </div>
            </TabsContent>
            <TabsContent value="test-yourself" className="pt-6">
                <div className="text-center space-y-6">
                    <p className="text-muted-foreground">سيتم اختبارك في جميع جداول الضرب من 1 إلى 10 بشكل عشوائي.</p>
                    <div className="flex justify-center items-center gap-2 p-4 bg-secondary rounded-lg">
                        <span className="text-4xl font-bold font-mono">{currentQuestion.num1}</span>
                        <span className="text-3xl">×</span>
                        <span className="text-4xl font-bold font-mono">{currentQuestion.num2}</span>
                        <span className="text-3xl">=</span>
                        <span className="text-4xl font-bold font-mono">?</span>
                    </div>
                    
                    <FormProvider {...methods}>
                      <form onSubmit={methods.handleSubmit(handleNextQuestion)} className="space-y-4">
                          <Input
                            {...methods.register('userAnswer')}
                            type="number"
                            placeholder="أدخل إجابتك هنا"
                            className="text-center text-lg h-12"
                            disabled={!!feedback}
                          />

                          <Button type="submit" size="lg" className="w-full" disabled={!!feedback}>
                            السؤال التالي
                            <ChevronRight className="mr-2 h-4 w-4" />
                          </Button>
                      </form>
                    </FormProvider>

                    {feedback && (
                        <div className={cn(
                            "flex items-center justify-center gap-2 p-3 rounded-md",
                            feedback.isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        )}>
                            {feedback.isCorrect ? <CheckCircle className="h-5 w-5" /> : <XCircle className="h-5 w-5" />}
                            <span className="font-medium">{feedback.message}</span>
                        </div>
                    )}
                    
                    <div className="flex justify-around pt-4 border-t items-center">
                        <div className="text-green-600">
                            <p className="text-sm font-medium">صحيحة</p>
                            <p className="text-2xl font-bold">{score.correct}</p>
                        </div>
                        <Button variant="ghost" size="sm" onClick={resetScore}>
                            <RefreshCw className="ml-2 h-4 w-4" />
                            إعادة تعيين
                        </Button>
                         <div className="text-red-600">
                            <p className="text-sm font-medium">خاطئة</p>
                            <p className="text-2xl font-bold">{score.incorrect}</p>
                        </div>
                    </div>
                </div>
            </TabsContent>
        </Tabs>

      </CardContent>
    </Card>
  );
}
