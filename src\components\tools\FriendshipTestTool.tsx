
'use client';

import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Repeat, User, UserRound, Users } from 'lucide-react';
import { cn } from '@/lib/utils';

type Gender = 'male' | 'female';

const getQuestionText = (question: any, gender: Gender): string => {
  if (typeof question.text === 'string') {
    return question.text;
  }
  return question.text[gender];
};

const getAnswerText = (answer: any, gender: Gender): string => {
    if (typeof answer.text === 'string') {
      return answer.text;
    }
    return answer.text[gender];
  };

type Answer = { text: string | { male: string; female: string }; points: number };
type Question = { text: string | { male: string; female: string }; answers: Answer[] };


const questions: Question[] = [
  // Loyalty & Support
  {
    text: "صديقك يمر بأزمة صعبة في منتصف الليل. ماذا تفعل؟",
    answers: [
      { text: "أذهب إليه فورًا أو أبقى على الهاتف معه حتى يشعر بتحسن.", points: 4 },
      { text: "أتصل به للاطمئنان وأعرض المساعدة في الصباح.", points: 3 },
      { text: "أرسل له رسالة دعم وأخبره أنني أفكر فيه.", points: 2 },
      { text: "أفترض أنه سيتدبر أمره وأنتظر حتى يتصل بي هو.", points: 1 },
    ],
  },
  {
    text: "سمعت إشاعة سيئة عن صديقك المقرب. كيف تتصرف؟",
    answers: [
      { text: "أدافع عنه على الفور وأرفض تصديق الإشاعة.", points: 4 },
      { text: "أتحدث مع صديقي مباشرة لأسمع القصة منه.", points: 3 },
      { text: "أشعر بالشك وأحاول معرفة المزيد من التفاصيل من الآخرين.", points: 2 },
      { text: "أبدأ في تصديق الإشاعة وأبتعد عنه قليلاً.", points: 1 },
    ],
  },
  {
    text: "صديقك حقق نجاحًا كبيرًا كنت تطمح إليه. ما هو شعورك؟",
    answers: [
      { text: "أشعر بسعادة غامرة وفخر حقيقي من أجله.", points: 4 },
      { text: "أشعر بالسعادة له، مع قليل من الغيرة الصحية التي تحفزني.", points: 3 },
      { text: "أجد صعوبة في إظهار السعادة وأشعر بالانزعاج.", points: 2 },
      { text: "أشعر بالغيرة الشديدة وأتجنب تهنئته.", points: 1 },
    ],
  },
  // Communication
  {
    text: "كم مرة تتواصل مع أصدقائك المقربين؟",
    answers: [
      { text: "نتواصل بشكل شبه يومي أو على الأقل عدة مرات في الأسبوع.", points: 4 },
      { text: "مرة واحدة في الأسبوع تقريبًا لنطمئن على بعضنا البعض.", points: 3 },
      { text: "أتواصل معهم فقط في المناسبات أو عندما أحتاج شيئًا.", points: 2 },
      { text: "نادرًا جدًا، قد تمر شهور دون تواصل.", points: 1 },
    ],
  },
  {
    text: "حدث خلاف بينك وبين صديقك. ماذا تفعل؟",
    answers: [
      { text: "أبادر بالحديث معه لحل المشكلة وفهم وجهة نظره.", points: 4 },
      { text: "أنتظر بضعة أيام حتى تهدأ الأمور ثم أتحدث معه.", points: 3 },
      { text: "أتجاهل المشكلة وأتصرف كأن شيئًا لم يكن.", points: 2 },
      { text: "أنتظر منه أن يعتذر أولاً.", points: 1 },
    ],
  },
  // Trust & Honesty
  {
    text: "صديقك ائتمنك على سر كبير. هل يمكنك الحفاظ عليه؟",
    answers: [
      { text: "نعم، بالتأكيد. سره في بئر.", points: 4 },
      { text: "غالبًا نعم، إلا إذا شعرت أن شخصًا آخر يجب أن يعرف.", points: 3 },
      { text: "قد أخبر به شخصًا آخر مقربًا جدًا أثق به.", points: 2 },
      { text: "أجد صعوبة في كتمان الأسرار.", points: 1 },
    ],
  },
  {
    text: "هل أنت صادق تمامًا مع أصدقائك حتى لو كانت الحقيقة مؤلمة؟",
    answers: [
      { text: "نعم، أؤمن بأن الصراحة أساس الصداقة الحقيقية، مع مراعاة طريقة الكلام.", points: 4 },
      { text: "أحاول أن أكون صادقًا، لكن قد أتجنب بعض الحقائق لتجنب إيذاء مشاعرهم.", points: 3 },
      { text: "غالبًا ما أقول 'كذبة بيضاء' لتجنب المشاكل.", points: 2 },
      { text: "أفضل عدم قول أي شيء قد يسبب انزعاجًا، حتى لو كان مهمًا.", points: 1 },
    ],
  },
  // Effort & Giving
  {
    text: "صديقك يحتاج إلى مساعدة في الانتقال إلى منزل جديد في عطلة نهاية الأسبوع. ماذا تفعل؟",
    answers: [
      { text: "أكون أول الحاضرين وأبقى حتى النهاية.", points: 4 },
      { text: "أذهب للمساعدة لبضع ساعات حسب استطاعتي.", points: 3 },
      { text: "أعتذر بوجود خطط أخرى ولكن أتمنى له التوفيق.", points: 2 },
      { text: "أتجاهل طلبه وأتظاهر بأنني لم أره.", points: 1 },
    ],
  },
  {
    text: "هل تبذل جهدًا لتذكر المناسبات الخاصة بأصدقائك (مثل أعياد الميلاد)؟",
    answers: [
      { text: "نعم، أسجلها وأحرص دائمًا على أن أكون أول المهنئين.", points: 4 },
      { text: "أتذكر معظمها، وأعتمد على تذكيرات وسائل التواصل الاجتماعي.", points: 3 },
      { text: "أتذكرها إذا ذكرني بها شخص آخر.", points: 2 },
      { text: "لا، أنا سيئ جدًا في تذكر التواريخ.", points: 1 },
    ],
  },
  // Shared Experiences
  {
    text: "ما هو دورك في التخطيط للقاءات والتجمعات مع الأصدقاء؟",
    answers: [
      { text: "غالبًا ما أكون أنا المبادر والمنظم.", points: 4 },
      { text: "أشارك في التخطيط وأقترح أفكارًا.", points: 3 },
      { text: "أفضل أن تتم دعوتي بدلاً من التخطيط.", points: 2 },
      { text: "عادةً ما أعتذر عن الحضور.", points: 1 },
    ],
  },
  {
    text: "هل تستمتع بتجربة أشياء جديدة يقترحها أصدقاؤك، حتى لو لم تكن من اهتماماتك؟",
    answers: [
      { text: "بالتأكيد، أحب استكشاف اهتماماتهم وتجربة أشياء جديدة معهم.", points: 4 },
      { text: "أنا منفتح على التجربة إذا بدت الفكرة ممتعة.", points: 3 },
      { text: "أتردد قليلاً وأفضل الالتزام بالأنشطة المعتادة.", points: 2 },
      { text: "لا، أفضل أن أفعل ما أحبه فقط.", points: 1 },
    ],
  },
];

const getResult = (score: number, gender: Gender) => {
    const results = {
      level1: {
        title: "صديق مخلص وداعم",
        text: "أنت تجسيد للصديق الحقيقي. الولاء، الدعم، والمبادرة هي من أبرز صفاتك. أصدقاؤك محظوظون بوجودك في حياتهم لأنك دائمًا موجود في السراء والضراء. استمر في كونك هذا الشخص الرائع."
      },
      level2: {
        title: "صديق جيد وموثوق",
        text: "أنت صديق جيد تهتم بأصدقائك وتقدر علاقاتك. قد لا تكون دائمًا المبادر، لكنك موجود عند الحاجة وتوفر دعمًا حقيقيًا. صداقتك قيمة ومستقرة."
      },
      level3: {
        title: "صديق في بعض الأحيان",
        text: "صداقتك تعتمد على الظروف. قد تكون صديقًا رائعًا عندما تكون الأمور سهلة، لكنك قد تتردد في بذل جهد إضافي في الأوقات الصعبة. علاقاتك يمكن أن تصبح أعمق إذا استثمرت فيها المزيد من الوقت والجهد."
      },
      level4: {
        title: "معرفة سطحية",
        text: "تميل إلى إبقاء علاقاتك سطحية ولا تستثمر الكثير في الصداقات. قد يكون هذا ناتجًا عن انشغالك أو تفضيلك للعزلة. تذكر أن الصداقات الحقيقية تتطلب جهدًا متبادلاً لتنمو وتزدهر."
      }
    };
  
    if (score >= 35) {
      return { level: results.level1.title, description: results.level1.text };
    }
    if (score >= 25) {
      return { level: results.level2.title, description: results.level2.text };
    }
    if (score >= 15) {
      return { level: results.level3.title, description: results.level3.text };
    }
    return { level: results.level4.title, description: results.level4.text };
};

type TestStage = 'gender' | 'questions' | 'result';

// Helper to shuffle an array
function shuffleArray<T>(array: T[]): T[] {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}


export function FriendshipTestTool() {
  const [stage, setStage] = useState<TestStage>('gender');
  const [gender, setGender] = useState<Gender | null>(null);
  const [shuffledQuestions, setShuffledQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  
  const isFinished = stage === 'result';
  const totalScore = answers.reduce((sum, score) => sum + score, 0);
  const result = isFinished && gender ? getResult(totalScore, gender) : { level: '', description: '' };

  const startTest = (selectedGender: Gender) => {
    setGender(selectedGender);
    setAnswers([]);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setShuffledQuestions(shuffleArray(questions));
    setStage('questions');
  };

  const handleNextQuestion = () => {
    if (selectedAnswer === null) return;
    
    const newAnswers = [...answers, selectedAnswer];
    setAnswers(newAnswers);
    setSelectedAnswer(null);

    if (currentQuestionIndex < shuffledQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      setStage('result');
    }
  };

  const handleRestart = () => {
    setStage('gender');
    setGender(null);
  };
  
  const progress = isFinished ? 100 : ((currentQuestionIndex) / shuffledQuestions.length) * 100;
  
  const currentQuestion = shuffledQuestions[currentQuestionIndex];
  
  const shuffledAnswers = useMemo(() => {
    if (!currentQuestion) return [];
    return shuffleArray(currentQuestion.answers);
  }, [currentQuestion]);


  const renderContent = () => {
    switch(stage) {
      case 'gender':
        return (
          <div className="text-center space-y-6">
            <h3 className="text-lg font-semibold">قبل أن نبدأ، يرجى تحديد جنسك</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Button variant="outline" className="h-24 text-xl" onClick={() => startTest('male')}>
                <User className="ml-3 h-8 w-8" />
                ذكر
              </Button>
              <Button variant="outline" className="h-24 text-xl" onClick={() => startTest('female')}>
                <UserRound className="ml-3 h-8 w-8" />
                أنثى
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">هذا يساعدنا على تقديم تجربة أفضل في المستقبل.</p>
          </div>
        );
      
      case 'questions':
        if (!gender || !currentQuestion) return null;
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-center">{getQuestionText(currentQuestion, gender)}</h3>
            <RadioGroup
              key={currentQuestionIndex}
              value={selectedAnswer?.toString() || ""}
              onValueChange={(value) => setSelectedAnswer(parseInt(value, 10))}
              className="space-y-3"
            >
              {shuffledAnswers.map((answer, index) => (
                <Label 
                  key={index} 
                  dir="rtl"
                  className="flex items-center justify-between gap-x-3 p-4 border rounded-lg cursor-pointer hover:bg-muted/50 has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary"
                >
                  <span className="flex-1 text-right">{getAnswerText(answer, gender)}</span>
                  <RadioGroupItem 
                    value={String(answer.points)} 
                    id={`q${currentQuestionIndex}-a${index}`} 
                  />
                </Label>
              ))}
            </RadioGroup>
            <Button onClick={handleNextQuestion} disabled={selectedAnswer === null} className="w-full">
              {currentQuestionIndex === questions.length - 1 ? 'عرض النتيجة' : 'التالي'}
            </Button>
          </div>
        );
      case 'result':
        return (
          <div className="text-center space-y-4">
            <h3 className="text-2xl font-bold text-primary">{result.level}</h3>
            <p className="text-muted-foreground leading-relaxed">{result.description}</p>
            <p className="text-xs text-muted-foreground pt-4 border-t">إخلاء مسؤولية: هذا الاختبار هو لأغراض الترفيه والتثقيف الذاتي فقط ولا يمثل تشخيصًا نفسيًا.</p>
            <Button onClick={handleRestart} variant="outline" className="w-full">
                <Repeat className="ml-2 h-4 w-4" />
                أعد الاختبار
            </Button>
          </div>
        );
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
            <Users className="h-6 w-6 text-primary" />
            اختبار الصداقة
        </CardTitle>
        <CardDescription>
            أجب عن الأسئلة التالية بصدق لاكتشاف أي نوع من الأصدقاء أنت.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {stage !== 'gender' && (
             <div className="mb-6">
                <Progress value={progress} />
                <p className="text-sm text-muted-foreground mt-2 text-center">
                    {isFinished ? 'اكتمل الاختبار!' : `السؤال ${currentQuestionIndex + 1} من ${shuffledQuestions.length}`}
                </p>
            </div>
        )}
       
        {renderContent()}

      </CardContent>
    </Card>
  );
}
