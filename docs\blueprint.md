# **App Name**: جامع الأدوات

## Core Features:

- ملخص النصوص: Summarize blocks of text from links and files that the user provides. Uses an LLM tool to ensure no hallucinated content gets passed along to the user.
- User-Friendly Navigation: Collapsible category menus for organizing tools
- RTL & Responsive Layout: Responsive design supporting RTL direction for Arabic content
- Modular Architecture: Modular code structure for easy updates and scalability
- Real-Time Currency Conversion: Dynamic fetching and display of currency exchange rates from a free public API
- Versatile Toolkit: Comprehensive suite of financial, personal, educational, web, and additional tools with Arabic UI
- Intuitive Homepage: Homepage with search bar and brief descriptions for all available tools

## Style Guidelines:

- Primary color: A deep, authoritative blue (#3B5998) for a sense of trust and stability.
- Background color: Light, desaturated blue (#E8F0FE) for a clean, calming backdrop.
- Accent color: A soft, muted green (#77DD77) to indicate success states or availability of functionality.
- Headline font: 'Poppins' (sans-serif) for a modern, fashionable feel in titles.
- Body font: 'PT Sans' (sans-serif) for readable text within tools.
- Note: currently only Google Fonts are supported.
- Consistent use of simple, geometric icons for tool categories.
- Right-to-left (RTL) layout to ensure native readability for Arabic-speaking users.