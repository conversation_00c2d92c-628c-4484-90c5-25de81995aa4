'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { summarizeArabicText } from '@/ai/flows/summarize-arabic-text';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Sparkles } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const FormSchema = z.object({
  text: z.string().min(50, {
    message: 'الرجاء إدخال نص لا يقل عن 50 حرفًا.',
  }),
});

export function SummarizeArabicTextTool() {
  const [summary, setSummary] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      text: '',
    },
  });

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    setIsLoading(true);
    setSummary('');
    try {
      const result = await summarizeArabicText({ text: data.text });
      setSummary(result.summary);
    } catch (e) {
      console.error(e);
      toast({
        variant: 'destructive',
        title: 'خطأ في التلخيص',
        description: 'حدث خطأ أثناء محاولة تلخيص النص. الرجاء المحاولة مرة أخرى.',
      });
    }
    setIsLoading(false);
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <Card>
        <CardHeader>
          <CardTitle>النص الأصلي</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="text"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الصق النص المراد تلخيصه هنا</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="ابدأ بكتابة أو لصق النص هنا..."
                        className="resize-y min-h-[300px] lg:min-h-[400px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" disabled={isLoading} className="w-full">
                {isLoading ? (
                  <>
                    <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                    جاري التلخيص...
                  </>
                ) : (
                  <>
                    <Sparkles className="ml-2 h-4 w-4" />
                    لخص النص
                  </>
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
      <Card className="bg-primary/5">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="text-primary" />
            الملخص
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="min-h-[300px] lg:min-h-[400px] p-4 rounded-md bg-background relative">
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm rounded-md">
                <div className="flex flex-col items-center gap-2 text-muted-foreground">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span>يتم الآن إنشاء الملخص...</span>
                </div>
              </div>
            )}
            {summary ? (
              <p className="text-foreground whitespace-pre-wrap leading-relaxed">{summary}</p>
            ) : (
              <div className="flex items-center justify-center h-full">
                <p className="text-muted-foreground text-center">
                  سيظهر الملخص الذي تم إنشاؤه بواسطة الذكاء الاصطناعي هنا.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
