
import type { LucideIcon } from 'lucide-react';
import type { ComponentType } from 'react';
import {
  Landmark,
  ArrowRightLeft,
  CalendarClock,
  HeartPulse,
  GraduationCap,
  Code,
  Sparkles,
  Puzzle,
  Wallet,
  // Tool icons
  ClipboardList,
  Receipt,
  Gem,
  TrendingUp,
  Coins,
  Percent,
  Box,
  Calculator,
  Timer,
  Clock,
  History,
  PiggyBank,
  QrCode,
  SpellCheck,
  Ruler,
  Repeat,
  Flame,
  Baby,
  CalendarHeart,
  Cake,
  CalendarRange,
  CalendarDays,
  BookOpen,
  Users,
  Globe,
  MessageCircle,
  SquareRadical,
  Divide,
  Eraser,
  CalendarCheck,
  Moon,
  Sun,
  School,
  Coffee,
  Building,
  Weight,
  Type,
  EyeOff,
  Wand2,
  Beef,
  Route,
  Pen,
  Droplets,
  ScanLine,
  Hash,
  FileText,
  HandHeart,
  X,
  HeartCrack,
  Heart,
  Zap,
  Shield,
  Flower2,
  Cat,
  BrainCircuit,
  Eye,
  Text,
  Lightbulb,
} from 'lucide-react';

// Import tool components
import { AgeCalculatorTool } from '@/components/tools/AgeCalculatorTool';
import { AgeDifferenceCalculatorTool } from '@/components/tools/AgeDifferenceCalculatorTool';
import { AngerTestTool } from '@/components/tools/AngerTestTool';
import { ArabicNameDecoratorTool } from '@/components/tools/ArabicNameDecoratorTool';
import { AramcoStockTool } from '@/components/tools/AramcoStockTool';
import { AverageCalculatorTool } from '@/components/tools/AverageCalculatorTool';
import { BmiCalculatorTool } from '@/components/tools/BmiCalculatorTool';
import { CaloriesCalculatorTool } from '@/components/tools/CaloriesCalculatorTool';
import { CbmCalculatorTool } from '@/components/tools/CbmCalculatorTool';
import { ColorBlindnessTestTool } from '@/components/tools/ColorBlindnessTestTool';
import { CountdownCardTool } from '@/components/tools/CountdownCardTool';
import { CurrencyConverterTool } from '@/components/tools/CurrencyConverterTool';
import { DateConverterTool } from '@/components/tools/DateConverterTool';
import { DateDifferenceTool } from '@/components/tools/DateDifferenceTool';
import { DiscountCalculatorTool } from '@/components/tools/DiscountCalculatorTool';
import { FaddanToMeterConverterTool } from '@/components/tools/FaddanToMeterConverterTool';
import { FeetToMeterConverterTool } from '@/components/tools/FeetToMeterConverterTool';
import { FemininityTestTool } from '@/components/tools/FemininityTestTool';
import { FinancialAidRequestTool } from '@/components/tools/FinancialAidRequestTool';
import { FriendshipTestTool } from '@/components/tools/FriendshipTestTool';
import { GallonToLiterConverterTool } from '@/components/tools/GallonToLiterConverterTool';
import { GoldPriceTool } from '@/components/tools/GoldPriceTool';
import { GpaCalculatorTool } from '@/components/tools/GpaCalculatorTool';
import { HourlyWageCalculatorTool } from '@/components/tools/HourlyWageCalculatorTool';
import { InvisibleCharacterTool } from '@/components/tools/InvisibleCharacterTool';
import { IslamicQuizTool } from '@/components/tools/IslamicQuizTool';
import { IstikharaPrayerTool } from '@/components/tools/IstikharaPrayerTool';
import { JealousyTestTool } from '@/components/tools/JealousyTestTool';
import { JordanianTawjihiCalculatorTool } from '@/components/tools/JordanianTawjihiCalculatorTool';
import { KidsQuizTool } from '@/components/tools/KidsQuizTool';
import { LoveTestTool } from '@/components/tools/LoveTestTool';
import { MasculinityTestTool } from '@/components/tools/MasculinityTestTool';
import { MileKilometerConverterTool } from '@/components/tools/MileKilometerConverterTool';
import { MultiplicationTableTool } from '@/components/tools/MultiplicationTableTool';
import { MyIpTool } from '@/components/tools/MyIpTool';
import { NumberToWordsTool } from '@/components/tools/NumberToWordsTool';
import { OmaniDakhiliyahDialectTestTool } from '@/components/tools/OmaniDakhiliyahDialectTestTool';
import { OunceToGramConverterTool } from '@/components/tools/OunceToGramConverterTool';
import { OvertimeCalculatorTool } from '@/components/tools/OvertimeCalculatorTool';
import { OvulationCalculatorTool } from '@/components/tools/OvulationCalculatorTool';
import { ParaphraseTextTool } from '@/components/tools/ParaphraseTextTool';
import { PercentageCalculatorTool } from '@/components/tools/PercentageCalculatorTool';
import { PersonalityStrengthTestTool } from '@/components/tools/PersonalityStrengthTestTool';
import { PoundToKgConverterTool } from '@/components/tools/PoundToKgConverterTool';
import { PregnancyCalculatorTool } from '@/components/tools/PregnancyCalculatorTool';
import { ProteinCalculatorTool } from '@/components/tools/ProteinCalculatorTool';
import { QrCodeGeneratorTool } from '@/components/tools/QrCodeGeneratorTool';
import { QrCodeReaderTool } from '@/components/tools/QrCodeReaderTool';
import { ResignationLetterGeneratorTool } from '@/components/tools/ResignationLetterGeneratorTool';
import { RetirementCalculatorTool } from '@/components/tools/RetirementCalculatorTool';
import { ReverseTextTool } from '@/components/tools/ReverseTextTool';
import { SampleSizeCalculatorTool } from '@/components/tools/SampleSizeCalculatorTool';
import { SensitivityTestTool } from '@/components/tools/SensitivityTestTool';
import { SimpleCalculatorTool } from '@/components/tools/SimpleCalculatorTool';
import { SpoiledTestTool } from '@/components/tools/SpoiledTestTool';
import { SqrtCalculatorTool } from '@/components/tools/SqrtCalculatorTool';
import { SummarizeArabicTextTool } from '@/components/tools/SummarizeArabicTextTool';
import { TextRepeaterTool } from '@/components/tools/TextRepeaterTool';
import { TodaysDateTool } from '@/components/tools/TodaysDateTool';
import { UnitConverterTool } from '@/components/tools/UnitConverterTool';
import { VatCalculatorTool } from '@/components/tools/VatCalculatorTool';
import { WeightedGradeCalculatorTool } from '@/components/tools/WeightedGradeCalculatorTool';
import { WhatsappToolsTool } from '@/components/tools/WhatsappToolsTool';
import { WordCountTool } from '@/components/tools/WordCountTool';
import { ZakatCalculatorTool } from '@/components/tools/ZakatCalculatorTool';
import { ZodiacSignCalculatorTool } from '@/components/tools/ZodiacSignCalculatorTool';


// Import data fetching functions
import { getCurrencyRates } from './actions/currency';
import { getIpInfo } from './actions/ip';
import { getGoldAndSilverPrices } from './actions/gold';
import { getAramcoStock } from './actions/aramco';
import * as DateCalculators from './dates';

export interface Tool {
  name: string;
  slug: string;
  path: string;
  description: string;
  icon?: LucideIcon;
  component?: ComponentType<any>;
  getData?: () => Promise<any>;
  seoDescription?: string;
  faq?: { question: string; answer: string }[];
  relatedSlugs?: string[];
}

export interface ToolCategory {
  name: string;
  slug: string;
  description: string;
  icon: LucideIcon;
  tools: Tool[];
}

export const toolCategories: ToolCategory[] = [
  {
    name: 'الحاسبات المالية',
    slug: 'financial-calculators',
    description: 'أدوات لإدارة أموالك وحساباتك بدقة وسهولة.',
    icon: Landmark,
    tools: [
      { 
        name: 'حساب زكاة المال', 
        slug: 'zakat-calculator', 
        path: '/tools/zakat-calculator', 
        description: 'احسب قيمة الزكاة الواجبة على أموالك.', 
        component: ZakatCalculatorTool, 
        icon: Coins,
        getData: () => getGoldAndSilverPrices(),
        relatedSlugs: ['gold-price', 'currency-converter', 'percentage-calculator'],
      },
      { 
        name: 'حساب ضريبة القيمة المضافة', 
        slug: 'vat-calculator', 
        path: '/tools/vat-calculator', 
        description: 'حساب ضريبة القيمة المضافة بسهولة.', 
        component: VatCalculatorTool, 
        icon: Receipt,
        relatedSlugs: ['percentage-calculator', 'discount-calculator'],
      },
      { 
        name: 'تحويل العملات', 
        slug: 'currency-converter', 
        path: '/tools/currency-converter', 
        description: 'أسعار صرف العملات محدثة باستمرار.', 
        component: CurrencyConverterTool, 
        getData: getCurrencyRates, 
        icon: ArrowRightLeft,
        relatedSlugs: ['gold-price', 'aramco-stock'],
      },
      { 
        name: 'أسعار الذهب اليوم', 
        slug: 'gold-price', 
        path: '/tools/gold-price', 
        description: 'اطلع على أسعار الذهب لحظة بلحظة.', 
        component: GoldPriceTool,
        getData: () => getGoldAndSilverPrices(), 
        icon: Gem,
        relatedSlugs: ['ounce-to-gram-converter', 'currency-converter', 'zakat-calculator'],
      },
      { 
        name: 'سعر سهم أرامكو اليوم', 
        slug: 'aramco-stock', 
        path: '/tools/aramco-stock', 
        description: 'تابع سعر سهم شركة أرامكو السعودية.', 
        component: AramcoStockTool,
        getData: getAramcoStock, 
        icon: TrendingUp,
      },
      { 
        name: 'حساب الخصم', 
        slug: 'discount-calculator', 
        path: '/tools/discount-calculator', 
        description: 'معرفة السعر النهائي بعد الخصم.', 
        component: DiscountCalculatorTool, 
        icon: Percent,
        relatedSlugs: ['percentage-calculator', 'vat-calculator'],
      },
      { 
        name: 'حساب CBM للشحن', 
        slug: 'cbm-calculator', 
        path: '/tools/cbm-calculator', 
        description: 'حساب حجم الشحنات التجارية بالمتر المكعب.', 
        component: CbmCalculatorTool, 
        icon: Box,
        relatedSlugs: ['unit-converter'],
      },
    ],
  },
  {
    name: 'أدوات الرواتب والأجور',
    slug: 'payroll-tools',
    description: 'حسابات متعلقة بالراتب والتقاعد والأجور.',
    icon: Wallet,
    tools: [
        {
          name: 'كم باقي على الراتب',
          slug: 'saudi-salary-countdown',
          path: '/tools/saudi-salary-countdown',
          description: 'عداد تنازلي لموعد صرف رواتب الموظفين في السعودية (يوم 27).',
          component: CountdownCardTool,
          getData: async () => Promise.resolve(DateCalculators.getSalaryCountdown()),
          icon: Wallet,
        },
        { 
          name: 'حساب أجر الساعة', 
          slug: 'hourly-wage-calculator', 
          path: '/tools/hourly-wage-calculator', 
          description: 'احسب قيمة أجرك بالساعة.', 
          component: HourlyWageCalculatorTool, 
          icon: Clock,
          relatedSlugs: ['overtime-calculator', 'retirement-calculator'],
        },
        { 
          name: 'حساب الأجر الإضافي', 
          slug: 'overtime-calculator', 
          path: '/tools/overtime-calculator', 
          description: 'معرفة قيمة العمل الإضافي.', 
          component: OvertimeCalculatorTool, 
          icon: History,
          relatedSlugs: ['hourly-wage-calculator'],
        },
        { 
          name: 'حاسبة التقاعد', 
          slug: 'retirement-calculator', 
          path: '/tools/retirement-calculator', 
          description: 'خطط لمستقبلك المالي بعد التقاعد.', 
          component: RetirementCalculatorTool, 
          icon: PiggyBank,
          relatedSlugs: ['hourly-wage-calculator'],
        },
        {
          name: 'عداد حساب المواطن',
          slug: 'citizen-account-countdown',
          path: '/tools/citizen-account-countdown',
          description: 'الوقت المتبقي حتى إيداع دفعة حساب المواطن القادمة.',
          component: CountdownCardTool,
          getData: async () => Promise.resolve(DateCalculators.getCitizenAccount()),
          icon: Users,
        },
        {
          name: 'عداد راتب التقاعد',
          slug: 'retirement-pension-countdown',
          path: '/tools/retirement-pension-countdown',
          description: 'الوقت المتبقي حتى إيداع رواتب المتقاعدين.',
          component: CountdownCardTool,
          getData: async () => Promise.resolve(DateCalculators.getRetirementPension()),
          icon: Building,
        },
        {
          name: 'عداد الدعم السكني',
          slug: 'housing-support-countdown',
          path: '/tools/housing-support-countdown',
          description: 'الوقت المتبقي حتى إيداع الدعم السكني للمستفيدين.',
          component: CountdownCardTool,
          getData: async () => Promise.resolve(DateCalculators.getHousingSupport()),
          icon: Landmark,
        },
    ]
  },
  {
    name: 'المحولات',
    slug: 'converters',
    description: 'تحويل بين الوحدات والصيغ المختلفة بسهولة.',
    icon: ArrowRightLeft,
    tools: [
      { 
        name: 'تحويل الوحدات', 
        slug: 'unit-converter', 
        path: '/tools/unit-converter', 
        description: 'تحويل الطول والوزن والحرارة وغيرها.', 
        component: UnitConverterTool, 
        icon: Ruler,
        relatedSlugs: ['mile-kilometer-converter', 'cbm-calculator', 'ounce-to-gram-converter'],
      },
      {
        name: 'تحويل من ميل إلى كيلو',
        slug: 'mile-kilometer-converter',
        path: '/tools/mile-kilometer-converter',
        description: 'تحويل المسافات بين الميل والكيلومتر بسهولة.',
        component: MileKilometerConverterTool,
        icon: Route,
        relatedSlugs: ['unit-converter'],
      },
      {
        name: 'الفدان كم متر',
        slug: 'faddan-to-meter-converter',
        path: '/tools/faddan-to-meter-converter',
        description: 'تحويل المساحة من فدان إلى متر مربع والعكس.',
        component: FaddanToMeterConverterTool,
        icon: Ruler,
        relatedSlugs: ['unit-converter'],
      },
       {
        name: 'تحويل من جالون إلى لتر',
        slug: 'gallon-to-liter-converter',
        path: '/tools/gallon-to-liter-converter',
        description: 'تحويل حجم السوائل بين الجالون الأمريكي واللتر.',
        component: GallonToLiterConverterTool,
        icon: Droplets,
        relatedSlugs: ['unit-converter'],
      },
      {
        name: 'تحويل من قدم إلى متر',
        slug: 'feet-to-meter-converter',
        path: '/tools/feet-to-meter-converter',
        description: 'تحويل الطول بين القدم والمتر.',
        component: FeetToMeterConverterTool,
        icon: Ruler,
        relatedSlugs: ['unit-converter'],
      },
      { 
        name: 'تحويل من رطل الى كيلو', 
        slug: 'pound-to-kg-converter', 
        path: '/tools/pound-to-kg-converter', 
        description: 'تحويل الوزن بين الرطل (باوند) والكيلوجرام.', 
        component: PoundToKgConverterTool, 
        icon: Weight,
        relatedSlugs: ['unit-converter'],
      },
      { 
        name: 'اونصة الذهب كم جرام',
        slug: 'ounce-to-gram-converter',
        path: '/tools/ounce-to-gram-converter',
        description: 'تحويل أونصة تروي (وحدة قياس المعادن الثمينة) إلى جرام.',
        component: OunceToGramConverterTool,
        icon: Weight,
        relatedSlugs: ['gold-price', 'unit-converter'],
      },
    ],
  },
  {
    name: 'أدوات الصحة والحياة اليومية',
    slug: 'health-daily-life-tools',
    description: 'أدوات لمتابعة صحتك وتنظيم أمور حياتك.',
    icon: HeartPulse,
    tools: [
      { 
        name: 'حاسبة مؤشر كتلة الجسم', 
        slug: 'bmi-calculator', 
        path: '/tools/bmi-calculator', 
        description: 'معرفة ما إذا كان وزنك صحيًا.', 
        component: BmiCalculatorTool, 
        icon: HeartPulse,
        relatedSlugs: ['calories-calculator', 'protein-calculator'],
      },
      { 
        name: 'حاسبة السعرات الحرارية', 
        slug: 'calories-calculator', 
        path: '/tools/calories-calculator', 
        description: 'حساب احتياجك اليومي من السعرات.', 
        component: CaloriesCalculatorTool, 
        icon: Flame,
        relatedSlugs: ['bmi-calculator', 'protein-calculator'],
      },
      { 
        name: 'حاسبة الحمل والولادة', 
        slug: 'pregnancy-calculator', 
        path: '/tools/pregnancy-calculator', 
        description: 'توقعي موعد ولادتك وتابعي حملك.', 
        component: PregnancyCalculatorTool, 
        icon: Baby,
        relatedSlugs: ['ovulation-calculator'],
      },
      { 
        name: 'حاسبة التبويض', 
        slug: 'ovulation-calculator', 
        path: '/tools/ovulation-calculator', 
        description: 'معرفة أيام التبويض لزيادة فرص الحمل.', 
        component: OvulationCalculatorTool, 
        icon: CalendarHeart,
        relatedSlugs: ['pregnancy-calculator'],
      },
      { 
        name: 'حاسبة البروتين', 
        slug: 'protein-calculator', 
        path: '/tools/protein-calculator', 
        description: 'احسب احتياجك اليومي من البروتين.', 
        component: ProteinCalculatorTool, 
        icon: Beef,
        relatedSlugs: ['calories-calculator', 'bmi-calculator'],
      },
    ],
  },
  {
    name: 'التواريخ والأوقات',
    slug: 'dates-times',
    description: 'أدوات متنوعة للتعامل مع التواريخ والأوقات.',
    icon: CalendarClock,
    tools: [
      {
        name: 'تاريخ اليوم',
        slug: 'todays-date',
        path: '/tools/todays-date',
        description: 'عرض تاريخ اليوم بالتقويم الميلادي والهجري.',
        component: TodaysDateTool,
        icon: CalendarCheck,
      },
      {
        name: 'عداد رمضان',
        slug: 'ramadan-countdown',
        path: '/tools/ramadan-countdown',
        description: 'الوقت المتبقي حتى بداية شهر رمضان المبارك.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getRamadanCountdown()),
        icon: Moon,
      },
      {
        name: 'عداد عيد الفطر',
        slug: 'eid-alfitr-countdown',
        path: '/tools/eid-alfitr-countdown',
        description: 'الوقت المتبقي حتى أول أيام عيد الفطر السعيد.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getEidAlFitr()),
        icon: Moon,
      },
      {
        name: 'عداد عيد الأضحى',
        slug: 'eid-aladha-countdown',
        path: '/tools/eid-aladha-countdown',
        description: 'الوقت المتبقي حتى أول أيام عيد الأضحى المبارك.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getEidAlAdha()),
        icon: Moon,
      },
      {
        name: 'عداد يوم عرفة',
        slug: 'arafah-day-countdown',
        path: '/tools/arafah-day-countdown',
        description: 'الوقت المتبقي حتى يوم عرفة، ركن الحج الأعظم.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getArafahDay()),
        icon: Sun,
      },
      {
        name: 'عداد يوم التأسيس',
        slug: 'founding-day-countdown',
        path: '/tools/founding-day-countdown',
        description: 'الوقت المتبقي حتى الاحتفال بيوم التأسيس للمملكة.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getFoundingDay()),
        icon: Landmark,
      },
      {
        name: 'عداد اليوم الوطني السعودي',
        slug: 'saudi-national-day-countdown',
        path: '/tools/saudi-national-day-countdown',
        description: 'الوقت المتبقي حتى الاحتفال باليوم الوطني للمملكة.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getNationalDay()),
        icon: CalendarCheck,
      },
      {
        name: 'عداد الإجازة القادمة',
        slug: 'next-vacation-countdown',
        path: '/tools/next-vacation-countdown',
        description: 'الوقت المتبقي حتى أقرب إجازة دراسية قادمة.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getNextVacation()),
        icon: Coffee,
      },
       {
        name: 'كم باقي على المدرسة السعودية​',
        slug: 'study-calendar-countdown',
        path: '/tools/study-calendar-countdown',
        description: 'مواعيد بداية العام الدراسي والإجازات بناءً على التقويم الرسمي المعتمد.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getSaudiCalendar()),
        icon: School,
      },
      {
        name: 'عداد بداية الشتاء',
        slug: 'winter-countdown',
        path: '/tools/winter-countdown',
        description: 'الوقت المتبقي حتى بداية فصل الشتاء فلكيًا.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getWinterStart()),
        icon: Timer,
      },
       {
        name: 'عداد نهاية الصيف',
        slug: 'summer-end-countdown',
        path: '/tools/summer-end-countdown',
        description: 'الوقت المتبقي حتى نهاية فصل الصيف فلكيًا.',
        component: CountdownCardTool,
        getData: async () => Promise.resolve(DateCalculators.getSummerEnd()),
        icon: Sun,
      },
      { 
        name: 'حاسبة العمر', 
        slug: 'age-calculator', 
        path: '/tools/age-calculator', 
        description: 'احسب عمرك بالسنوات والشهور والأيام.', 
        component: AgeCalculatorTool, 
        icon: Cake,
        relatedSlugs: ['age-difference-calculator', 'date-difference', 'date-converter'],
      },
      { 
        name: 'حساب الفرق بين تاريخين', 
        slug: 'date-difference', 
        path: '/tools/date-difference', 
        description: 'حساب المدة بين تاريخين مختلفين.', 
        component: DateDifferenceTool, 
        icon: CalendarRange,
        relatedSlugs: ['age-calculator', 'age-difference-calculator', 'date-converter'],
      },
      { 
        name: 'حساب فرق العمر', 
        slug: 'age-difference-calculator', 
        path: '/tools/age-difference-calculator', 
        description: 'احسب فرق العمر بين شخصين بالسنوات والأشهر والأيام.', 
        component: AgeDifferenceCalculatorTool, 
        icon: Users,
        relatedSlugs: ['age-calculator', 'date-difference'],
      },
      { 
        name: 'محول التاريخ', 
        slug: 'date-converter', 
        path: '/tools/date-converter', 
        description: 'التحويل بين التاريخ الميلادي والهجري والعكس.', 
        component: DateConverterTool, 
        icon: CalendarDays,
        relatedSlugs: ['age-calculator', 'date-difference'],
      },
    ],
  },
  {
    name: 'أدوات التعليم',
    slug: 'education-tools',
    description: 'أدوات مفيدة للطلاب والباحثين في مسيرتهم الدراسية.',
    icon: GraduationCap,
    tools: [
      {
        name: 'جدول الضرب',
        slug: 'multiplication-table',
        path: '/tools/multiplication-table',
        description: 'عرض جدول الضرب بشكل تفاعلي لسهولة الحفظ.',
        component: MultiplicationTableTool,
        icon: X,
      },
      { 
        name: 'حساب المعدل التراكمي', 
        slug: 'gpa-calculator', 
        path: '/tools/gpa-calculator', 
        description: 'احسب معدلك التراكمي بسهولة.', 
        component: GpaCalculatorTool, 
        icon: GraduationCap,
        relatedSlugs: ['weighted-grade-calculator', 'percentage-calculator'],
      },
      { 
        name: 'حساب النسبة الموزونة', 
        slug: 'weighted-grade-calculator', 
        path: '/tools/weighted-grade-calculator', 
        description: 'حساب الدرجة النهائية الموزونة للقبول الجامعي.', 
        component: WeightedGradeCalculatorTool, 
        icon: BookOpen,
        relatedSlugs: ['gpa-calculator'],
      },
      { 
        name: 'حساب معدل التوجيهي', 
        slug: 'jordanian-tawjihi-calculator', 
        path: '/tools/jordanian-tawjihi-calculator', 
        description: 'حساب معدل شهادة الثانوية العامة الأردنية.', 
        component: JordanianTawjihiCalculatorTool, 
        icon: GraduationCap,
        relatedSlugs: ['gpa-calculator'],
      },
      { 
        name: 'تحديد حجم العينة', 
        slug: 'sample-size-calculator', 
        path: '/tools/sample-size-calculator', 
        description: 'تحديد حجم العينة المناسب لبحثك العلمي.', 
        component: SampleSizeCalculatorTool, 
        icon: Users,
      },
    ],
  },
  {
    name: 'أدوات النصوص والكتابة',
    slug: 'text-writing-tools',
    description: 'أدوات لمعالجة النصوص وتحسينها وإنشاء المحتوى.',
    icon: Text,
    tools: [
      { 
        name: 'ملخص النصوص العربية', 
        slug: 'summarize-arabic-text', 
        path: '/tools/summarize-arabic-text', 
        description: 'تلخيص النصوص العربية الطويلة بضغطة زر.', 
        component: SummarizeArabicTextTool, 
        icon: ClipboardList,
        relatedSlugs: ['paraphrase-text', 'word-count'],
      },
      { 
        name: 'أداة إعادة صياغة النص', 
        slug: 'paraphrase-text', 
        path: '/tools/paraphrase-text', 
        description: 'أعد صياغة النصوص والعبارات بأسلوب مختلف.', 
        component: ParaphraseTextTool, 
        icon: Wand2,
        relatedSlugs: ['summarize-arabic-text', 'word-count'],
      },
      { 
        name: 'مولد نماذج الاستقالة', 
        slug: 'resignation-letter-generator', 
        path: '/tools/resignation-letter-generator', 
        description: 'أنشئ خطاب استقالة احترافي في ثوانٍ.', 
        component: ResignationLetterGeneratorTool, 
        icon: FileText,
        relatedSlugs: ['financial-aid-request-generator'],
      },
      {
        name: 'مولد طلب مساعدة مالية',
        slug: 'financial-aid-request-generator',
        path: '/tools/financial-aid-request-generator',
        description: 'أنشئ معروض طلب مساعدة مالية بأنماط مختلفة.',
        component: FinancialAidRequestTool,
        icon: HandHeart,
        relatedSlugs: ['resignation-letter-generator'],
      },
      { 
        name: 'عداد الكلمات', 
        slug: 'word-count', 
        path: '/tools/word-count', 
        description: 'حساب عدد الكلمات والأحرف في نص معين.', 
        component: WordCountTool, 
        icon: Type,
        relatedSlugs: ['summarize-arabic-text', 'paraphrase-text'],
      },
      { 
        name: 'عكس النص', 
        slug: 'reverse-text', 
        path: '/tools/reverse-text', 
        description: 'للنصوص العربية في البرامج غير الداعمة.', 
        component: ReverseTextTool, 
        icon: Eraser,
      },
       { 
        name: 'تكرار النص', 
        slug: 'text-repeater', 
        path: '/tools/text-repeater', 
        description: 'تكرار أي نص أو كلمة لعدد معين من المرات.', 
        component: TextRepeaterTool, 
        icon: Repeat,
      },
      { 
        name: 'مولد الحرف المخفي (نص فارغ)', 
        slug: 'invisible-character', 
        path: '/tools/invisible-character', 
        description: 'نسخ حروف غير مرئية لاستخدامها في التطبيقات.', 
        component: InvisibleCharacterTool, 
        icon: EyeOff,
      },
      { 
        name: 'زخرفة اسماء', 
        slug: 'arabic-name-decorator', 
        path: '/tools/arabic-name-decorator', 
        description: 'زخرفة اسمك باللغة العربية والإنجليزية بأنماط فريدة.', 
        component: ArabicNameDecoratorTool, 
        icon: Pen,
      },
    ]
  },
  {
    name: 'حاسبات عامة',
    slug: 'general-calculators',
    description: 'مجموعة من الحاسبات الرياضية واليومية المفيدة.',
    icon: Calculator,
    tools: [
      { 
        name: 'حاسبة النسبة المئوية', 
        slug: 'percentage-calculator', 
        path: '/tools/percentage-calculator', 
        description: 'إجراء كافة حسابات النسبة المئوية.', 
        component: PercentageCalculatorTool, 
        icon: Percent,
        relatedSlugs: ['discount-calculator', 'vat-calculator', 'gpa-calculator'],
      },
      { 
        name: 'آلة حاسبة بسيطة', 
        slug: 'simple-calculator', 
        path: '/tools/simple-calculator', 
        description: 'إجراء العمليات الحسابية الأساسية.', 
        component: SimpleCalculatorTool, 
        icon: Calculator,
      },
      { 
        name: 'حساب الجذر التربيعي', 
        slug: 'sqrt-calculator', 
        path: '/tools/sqrt-calculator', 
        description: 'إيجاد الجذر التربيعي لأي رقم.', 
        component: SqrtCalculatorTool, 
        icon: SquareRadical,
      },
      { 
        name: 'حساب الوسط الحسابي', 
        slug: 'average-calculator', 
        path: '/tools/average-calculator', 
        description: 'حساب المتوسط الحسابي لمجموعة أرقام.', 
        component: AverageCalculatorTool, 
        icon: Divide,
      },
    ]
  },
  {
    name: 'اختبارات وتحليلات',
    slug: 'tests-and-analytics',
    description: 'مجموعة من الاختبارات الترفيهية لتحليل الشخصية.',
    icon: Puzzle,
    tools: [
      {
        name: 'اختبار عمى الألوان',
        slug: 'color-blindness-test',
        path: '/tools/color-blindness-test',
        description: 'اختبر قدرتك على تمييز الألوان باستخدام لوحات إيشيهارا.',
        component: ColorBlindnessTestTool,
        icon: Eye,
      },
      {
        name: 'اختبار لهجة أهل الداخلية',
        slug: 'omani-dakhiliyah-dialect-test',
        path: '/tools/omani-dakhiliyah-dialect-test',
        description: 'اكتشف مدى معرفتك بلهجة أهل الداخلية في سلطنة عُمان.',
        component: OmaniDakhiliyahDialectTestTool,
        icon: MessageCircle,
      },
       {
        name: 'اختبار قوة الشخصية',
        slug: 'personality-strength-test',
        path: '/tools/personality-strength-test',
        description: 'اكتشف مدى صلابتك ومرونتك النفسية في مواجهة التحديات.',
        component: PersonalityStrengthTestTool,
        icon: Gem,
        relatedSlugs: ['masculinity-test', 'anger-test', 'femininity-test'],
      },
       {
        name: 'اختبار الغيرة',
        slug: 'jealousy-test',
        path: '/tools/jealousy-test',
        description: 'اكتشف مستوى الغيرة في شخصيتك من خلال هذا الاختبار البسيط.',
        component: JealousyTestTool,
        icon: HeartCrack,
        relatedSlugs: ['love-test', 'friendship-test', 'anger-test'],
      },
      {
        name: 'اختبار العصبية',
        slug: 'anger-test',
        path: '/tools/anger-test',
        description: 'اكتشف مستوى هدوئك أو انفعالك في المواقف المختلفة.',
        component: AngerTestTool,
        icon: Zap,
        relatedSlugs: ['jealousy-test', 'masculinity-test'],
      },
      {
        name: 'اختبار الحب',
        slug: 'love-test',
        path: '/tools/love-test',
        description: 'اكتشف أسلوبك في الحب والعلاقات العاطفية.',
        component: LoveTestTool,
        icon: Heart,
        relatedSlugs: ['jealousy-test', 'friendship-test'],
      },
      {
        name: 'اختبار الصداقة',
        slug: 'friendship-test',
        path: '/tools/friendship-test',
        description: 'اكتشف أي نوع من الأصدقاء أنت وما هي نقاط قوتك.',
        component: FriendshipTestTool,
        icon: Users,
        relatedSlugs: ['love-test', 'jealousy-test'],
      },
      {
        name: 'اختبار الرجولة',
        slug: 'masculinity-test',
        path: '/tools/masculinity-test',
        description: 'اختبر جوانب القوة والمسؤولية والنضج في شخصيتك.',
        component: MasculinityTestTool,
        icon: Shield,
        relatedSlugs: ['anger-test', 'friendship-test'],
      },
       {
        name: 'اختبار الأنوثة',
        slug: 'femininity-test',
        path: '/tools/femininity-test',
        description: 'استكشفي جوانب شخصيتك وقوتك الداخلية والناعمة.',
        component: FemininityTestTool,
        icon: Flower2,
        relatedSlugs: ['love-test', 'friendship-test'],
      },
       {
        name: 'اختبار الدلع (للدلوعات)',
        slug: 'spoiled-test',
        path: '/tools/spoiled-test',
        description: 'اكتشفي درجة الدلع في شخصيتك مع اختبار الدلوعات المرح.',
        component: SpoiledTestTool,
        icon: Cat,
        relatedSlugs: ['femininity-test', 'love-test'],
      },
       {
        name: 'اختبار الحساسية',
        slug: 'sensitivity-test',
        path: '/tools/sensitivity-test',
        description: 'اكتشف مدى حساسيتك وتأثرك بالبيئة والمشاعر من حولك.',
        component: SensitivityTestTool,
        icon: Droplets,
      },
    ]
  },
  {
    name: 'أدوات المطورين',
    slug: 'developer-tools',
    description: 'أدوات تقنية للمطورين والمصممين.',
    icon: Code,
    tools: [
      { 
        name: 'مولد رمز QR', 
        slug: 'qr-code-generator', 
        path: '/tools/qr-code-generator', 
        description: 'إنشاء رموز QR مخصصة للروابط والنصوص وغيرها.', 
        component: QrCodeGeneratorTool, 
        icon: QrCode,
        relatedSlugs: ['qr-code-reader', 'my-ip', 'whatsapp-tools'],
      },
       { 
        name: 'قارئ رمز QR', 
        slug: 'qr-code-reader', 
        path: '/tools/qr-code-reader', 
        description: 'فك تشفير رموز QR من خلال رفع صورة.', 
        component: QrCodeReaderTool, 
        icon: ScanLine,
        relatedSlugs: ['qr-code-generator'],
      },
      { 
        name: 'معرفة IP الخاص بي', 
        slug: 'my-ip', 
        path: '/tools/my-ip', 
        description: 'عرض عنوان IP العام الخاص بك.', 
        component: MyIpTool, 
        getData: getIpInfo, 
        icon: Globe,
      },
    ]
  },
  {
    name: 'أدوات إسلامية',
    slug: 'islamic-tools',
    description: 'أدوات وحاسبات إسلامية متنوعة.',
    icon: Moon,
    tools: [
      {
        name: 'دعاء الاستخارة',
        slug: 'istikhara-prayer',
        path: '/tools/istikhara-prayer',
        description: 'دليل شامل لكيفية أداء صلاة الاستخارة ودعائها.',
        component: IstikharaPrayerTool,
        icon: BookOpen,
      },
      {
        name: 'اسئلة دينية',
        slug: 'islamic-quiz',
        path: '/tools/islamic-quiz',
        description: 'اختبر معلوماتك في القرآن والسيرة والفقه والعقيدة.',
        component: IslamicQuizTool,
        icon: BrainCircuit,
      },
    ]
  },
  {
    name: 'أدوات ترفيهية',
    slug: 'fun-tools',
    description: 'أدوات مسلية لقضاء وقت ممتع.',
    icon: Sparkles,
    tools: [
       {
        name: 'لعبة أسئلة للأطفال',
        slug: 'kids-quiz',
        path: '/tools/kids-quiz',
        description: 'اختبر معلوماتك العامة في العلوم والجغرافيا والحيوانات.',
        component: KidsQuizTool,
        icon: Lightbulb,
      },
       { 
        name: 'حاسبة الأبراج', 
        slug: 'zodiac-sign-calculator', 
        path: '/tools/zodiac-sign-calculator', 
        description: 'اكتشف برجك الشمسي بناءً على تاريخ ميلادك.', 
        component: ZodiacSignCalculatorTool, 
        icon: Sparkles,
      },
       {
        name: 'تفقيط الأرقام',
        slug: 'number-to-words',
        path: '/tools/number-to-words',
        description: 'تحويل الأرقام إلى كلمات (تفقيط) للعملات والشيكات.',
        component: NumberToWordsTool,
        icon: SpellCheck,
        relatedSlugs: ['zakat-calculator'],
      },
      { 
        name: 'انشاء رابط WhatsApp', 
        slug: 'whatsapp-tools', 
        path: '/tools/whatsapp-tools', 
        description: 'حوّل رقم الواتس إلى رابط مباشر، أرسل رسائل بدون حفظ الرقم، واصنع رابط واتس اب بسهولة.', 
        component: WhatsappToolsTool,
        getData: getIpInfo, 
        icon: MessageCircle,
      },
    ]
  },
];
