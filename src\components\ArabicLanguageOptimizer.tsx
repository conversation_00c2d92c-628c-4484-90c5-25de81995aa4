'use client';

import { useEffect } from 'react';

interface ArabicLanguageOptimizerProps {
  children: React.ReactNode;
}

export function ArabicLanguageOptimizer({ children }: ArabicLanguageOptimizerProps) {
  useEffect(() => {
    // Set document language attributes
    document.documentElement.lang = 'ar';
    document.documentElement.dir = 'rtl';
    
    // Add Arabic-specific meta tags
    const metaTags = [
      { name: 'language', content: 'Arabic' },
      { name: 'content-language', content: 'ar' },
      { httpEquiv: 'Content-Language', content: 'ar' },
    ];

    metaTags.forEach(tag => {
      const existingTag = document.querySelector(`meta[name="${tag.name}"], meta[http-equiv="${tag.httpEquiv}"]`);
      if (!existingTag) {
        const meta = document.createElement('meta');
        if (tag.name) meta.name = tag.name;
        if (tag.httpEquiv) meta.httpEquiv = tag.httpEquiv;
        meta.content = tag.content;
        document.head.appendChild(meta);
      }
    });

    // Optimize Arabic font loading
    optimizeArabicFonts();
    
    // Set up Arabic text rendering optimizations
    optimizeArabicTextRendering();
    
    // Handle Arabic number formatting
    formatArabicNumbers();
    
  }, []);

  return <>{children}</>;
}

function optimizeArabicFonts() {
  // Preload Arabic fonts with font-display: swap
  const arabicFonts = [
    {
      family: 'Tajawal',
      weights: ['400', '500', '700'],
      display: 'swap'
    },
    {
      family: 'Wafeq',
      weights: ['600'],
      display: 'swap'
    }
  ];

  arabicFonts.forEach(font => {
    font.weights.forEach(weight => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'font';
      link.type = 'font/woff2';
      link.crossOrigin = 'anonymous';
      link.href = `https://fonts.gstatic.com/s/${font.family.toLowerCase()}/v1/${font.family}-${weight}.woff2`;
      document.head.appendChild(link);
    });
  });

  // Add font-display: swap to existing font faces
  const style = document.createElement('style');
  style.textContent = `
    @font-face {
      font-family: 'Tajawal';
      font-display: swap;
    }
    @font-face {
      font-family: 'Wafeq';
      font-display: swap;
    }
  `;
  document.head.appendChild(style);
}

function optimizeArabicTextRendering() {
  // Add CSS for better Arabic text rendering
  const style = document.createElement('style');
  style.textContent = `
    /* Arabic text optimization */
    [lang="ar"], [dir="rtl"] {
      text-rendering: optimizeLegibility;
      -webkit-font-feature-settings: "liga", "kern", "calt";
      font-feature-settings: "liga", "kern", "calt";
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
      text-size-adjust: 100%;
    }

    /* Arabic kashida (tatweel) handling */
    .arabic-text {
      -webkit-hyphens: auto;
      -ms-hyphens: auto;
      hyphens: auto;
      hyphenate-character: "ـ";
    }

    /* Arabic line breaking */
    .arabic-paragraph {
      word-break: keep-all;
      overflow-wrap: break-word;
      line-break: strict;
    }

    /* Arabic punctuation spacing */
    .arabic-text .punctuation {
      margin-left: 0.1em;
      margin-right: 0;
    }

    /* Arabic quote marks */
    .arabic-text q::before { content: "«"; }
    .arabic-text q::after { content: "»"; }
    
    /* Arabic emphasis */
    .arabic-text em {
      font-style: normal;
      font-weight: 600;
      text-decoration: underline;
      text-decoration-color: currentColor;
      text-underline-offset: 0.2em;
    }
  `;
  document.head.appendChild(style);
}

function formatArabicNumbers() {
  // Convert Western numerals to Arabic-Indic numerals where appropriate
  const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  
  // This would be used selectively for specific content
  // Most modern Arabic websites use Western numerals for better international compatibility
}

// Hook for Arabic language features
export function useArabicLanguageFeatures() {
  useEffect(() => {
    // Set up Arabic keyboard support
    document.addEventListener('keydown', handleArabicKeyboard);
    
    // Set up Arabic text direction detection
    document.addEventListener('input', handleTextDirectionDetection);
    
    return () => {
      document.removeEventListener('keydown', handleArabicKeyboard);
      document.removeEventListener('input', handleTextDirectionDetection);
    };
  }, []);
}

function handleArabicKeyboard(event: KeyboardEvent) {
  // Handle Arabic-specific keyboard shortcuts
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'r':
        // Toggle text direction
        event.preventDefault();
        toggleTextDirection();
        break;
    }
  }
}

function handleTextDirectionDetection(event: Event) {
  const target = event.target as HTMLInputElement | HTMLTextAreaElement;
  if (!target.value) return;
  
  // Detect if text contains Arabic characters
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  
  if (arabicRegex.test(target.value)) {
    target.dir = 'rtl';
    target.style.textAlign = 'right';
  } else {
    target.dir = 'ltr';
    target.style.textAlign = 'left';
  }
}

function toggleTextDirection() {
  const activeElement = document.activeElement as HTMLElement;
  if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
    const currentDir = activeElement.dir || 'ltr';
    activeElement.dir = currentDir === 'rtl' ? 'ltr' : 'rtl';
    (activeElement as HTMLInputElement).style.textAlign = currentDir === 'rtl' ? 'left' : 'right';
  }
}

// Arabic SEO utilities
export const arabicSEOUtils = {
  // Generate Arabic-optimized meta description
  generateArabicMetaDescription: (text: string, maxLength: number = 160): string => {
    // Remove HTML tags
    const cleanText = text.replace(/<[^>]*>/g, '');
    
    // Ensure proper Arabic text flow
    const arabicText = cleanText.trim();
    
    if (arabicText.length <= maxLength) {
      return arabicText;
    }
    
    // Find the last complete word within the limit
    const truncated = arabicText.substring(0, maxLength);
    const lastSpace = truncated.lastIndexOf(' ');
    
    return lastSpace > 0 ? truncated.substring(0, lastSpace) + '...' : truncated + '...';
  },

  // Generate Arabic keywords
  generateArabicKeywords: (baseKeywords: string[]): string[] => {
    const arabicStopWords = ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك'];
    
    return baseKeywords
      .filter(keyword => !arabicStopWords.includes(keyword))
      .map(keyword => keyword.trim())
      .filter(keyword => keyword.length > 2);
  },

  // Optimize Arabic URL slugs
  generateArabicSlug: (text: string): string => {
    return text
      .toLowerCase()
      .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  },

  // Format Arabic dates for SEO
  formatArabicDate: (date: Date): string => {
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      calendar: 'gregory'
    }).format(date);
  },

  // Format Hijri dates for SEO
  formatHijriDate: (date: Date): string => {
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      calendar: 'islamic'
    }).format(date);
  }
};

// Arabic accessibility enhancements
export function enhanceArabicAccessibility() {
  // Add ARIA labels in Arabic
  const elements = document.querySelectorAll('[data-arabic-label]');
  elements.forEach(element => {
    const arabicLabel = element.getAttribute('data-arabic-label');
    if (arabicLabel) {
      element.setAttribute('aria-label', arabicLabel);
    }
  });

  // Enhance Arabic screen reader support
  const style = document.createElement('style');
  style.textContent = `
    /* Screen reader optimizations for Arabic */
    .sr-only-ar {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
      direction: rtl;
    }

    /* Skip links for Arabic navigation */
    .skip-link-ar {
      position: absolute;
      top: -40px;
      left: 6px;
      background: hsl(var(--primary));
      color: hsl(var(--primary-foreground));
      padding: 8px;
      text-decoration: none;
      border-radius: 4px;
      direction: rtl;
      z-index: 1000;
    }

    .skip-link-ar:focus {
      top: 6px;
    }
  `;
  document.head.appendChild(style);
}
