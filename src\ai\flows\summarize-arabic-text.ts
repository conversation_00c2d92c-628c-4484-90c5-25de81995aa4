'use server';

/**
 * @fileOverview Summarizes Arabic text from a URL or file.
 *
 * - summarizeArabicText - A function that summarizes Arabic text from a URL or file.
 * - SummarizeArabicTextInput - The input type for the summarizeArabicText function.
 * - SummarizeArabicTextOutput - The return type for the summarizeArabicText function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const SummarizeArabicTextInputSchema = z.object({
  text: z.string().describe('The Arabic text to summarize.'),
});
export type SummarizeArabicTextInput = z.infer<typeof SummarizeArabicTextInputSchema>;

const SummarizeArabicTextOutputSchema = z.object({
  summary: z.string().describe('ملخص النص العربي.'),
});
export type SummarizeArabicTextOutput = z.infer<typeof SummarizeArabicTextOutputSchema>;

export async function summarizeArabicText(input: SummarizeArabicTextInput): Promise<SummarizeArabicTextOutput> {
  return summarizeArabicTextFlow(input);
}

const summarizeArabicTextPrompt = ai.definePrompt({
  name: 'summarizeArabicTextPrompt',
  input: {schema: SummarizeArabicTextInputSchema},
  output: {schema: SummarizeArabicTextOutputSchema},
  prompt: `قم بتلخيص النص العربي التالي. يجب أن يكون الملخص باللغة العربية، دقيقاً، ويتجنب المحتوى غير الصحيح:\n\n{{{text}}}`,
});

const summarizeArabicTextFlow = ai.defineFlow(
  {
    name: 'summarizeArabicTextFlow',
    inputSchema: SummarizeArabicTextInputSchema,
    outputSchema: SummarizeArabicTextOutputSchema,
  },
  async input => {
    const {output} = await summarizeArabicTextPrompt(input);
    return output!;
  }
);
