'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Calendar, Moon } from 'lucide-react';

interface DateInfo {
  gregorian: string;
  hijri: string;
}

export function TodaysDateTool() {
  const [dateInfo, setDateInfo] = useState<DateInfo>({ gregorian: '', hijri: '' });

  useEffect(() => {
    const today = new Date();
    
    const gregorianFormatter = new Intl.DateTimeFormat('ar-SA-u-ca-gregory-nu-latn', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    
    const hijriFormatter = new Intl.DateTimeFormat('ar-SA-u-ca-islamic-nu-latn', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    setDateInfo({
      gregorian: gregorianFormatter.format(today),
      hijri: hijriFormatter.format(today)
    });
  }, []);

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>تاريخ اليوم</CardTitle>
        <CardDescription>عرض التاريخ الحالي بالتقويمين الميلادي والهجري.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="p-6 bg-blue-500/10 rounded-lg border border-blue-500/20 text-center">
            <div className="flex items-center justify-center gap-3 mb-2">
                <Calendar className="h-7 w-7 text-blue-600"/>
                <h3 className="text-xl font-semibold text-blue-800">التاريخ الميلادي</h3>
            </div>
            <p className="text-2xl md:text-3xl font-bold text-blue-700 font-mono" dir="rtl">
                {dateInfo.gregorian || 'جاري التحميل...'}
            </p>
        </div>
        
        <div className="p-6 bg-green-500/10 rounded-lg border border-green-500/20 text-center">
            <div className="flex items-center justify-center gap-3 mb-2">
                <Moon className="h-7 w-7 text-green-600"/>
                <h3 className="text-xl font-semibold text-green-800">التاريخ الهجري</h3>
            </div>
            <p className="text-2xl md:text-3xl font-bold text-green-700 font-mono" dir="rtl">
                {dateInfo.hijri || 'جاري التحميل...'}
            </p>
        </div>
      </CardContent>
    </Card>
  );
}
