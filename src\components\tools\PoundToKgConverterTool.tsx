
'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRightLeft } from 'lucide-react';

const POUND_TO_KG_FACTOR = 0.453592;

export function PoundToKgConverterTool() {
  const [pounds, setPounds] = useState('1');
  const [kilograms, setKilograms] = useState(POUND_TO_KG_FACTOR.toString());

  const handlePoundChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPounds(value);
    if (value && !isNaN(parseFloat(value))) {
      const kgValue = parseFloat(value) * POUND_TO_KG_FACTOR;
      setKilograms(kgValue.toLocaleString('en-US', {maximumFractionDigits: 4, useGrouping: false}));
    } else {
      setKilograms('');
    }
  };

  const handleKilogramChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setKilograms(value);
    if (value && !isNaN(parseFloat(value))) {
      const poundValue = parseFloat(value) / POUND_TO_KG_FACTOR;
      setPounds(poundValue.toLocaleString('en-US', {maximumFractionDigits: 4, useGrouping: false}));
    } else {
      setPounds('');
    }
  };

  const handleSwap = () => {
    const currentPounds = pounds;
    const currentKilograms = kilograms;
    setPounds(currentKilograms);
    setKilograms(currentPounds);
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>تحويل من رطل الى كيلو (والعكس)</CardTitle>
        <CardDescription>
          أدخل الوزن في أي من الحقلين لرؤية التحويل الفوري.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-4 mb-6 bg-muted rounded-lg text-center">
            <p className="text-sm text-muted-foreground">معامل التحويل</p>
            <p className="text-lg font-bold font-mono text-primary">
                1 رطل (باوند) = 0.453592 كيلوجرام
            </p>
        </div>
        <div className="flex flex-col sm:flex-row items-center gap-4">
          <div className="w-full">
            <label htmlFor="pounds" className="text-sm font-medium mb-2 block">
              رطل (Pound/lb)
            </label>
            <Input
              id="pounds"
              type="number"
              value={pounds}
              onChange={handlePoundChange}
              placeholder="أدخل الوزن بالرطل"
              dir="ltr"
            />
          </div>
          
          <div className="shrink-0 pt-6">
            <Button variant="ghost" size="icon" onClick={handleSwap} aria-label="تبديل الوحدات">
                <ArrowRightLeft className="w-6 h-6 text-muted-foreground" />
            </Button>
          </div>

          <div className="w-full">
            <label htmlFor="kilograms" className="text-sm font-medium mb-2 block">
              كيلوجرام (kg)
            </label>
            <Input
              id="kilograms"
              type="number"
              value={kilograms}
              onChange={handleKilogramChange}
              placeholder="أدخل الوزن بالكيلوجرام"
              dir="ltr"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
