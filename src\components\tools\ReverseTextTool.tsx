'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Copy, ArrowRightLeft, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const FormSchema = z.object({
  text: z.string().min(1, { message: 'الرجاء إدخال نص لعكسه.' }),
});

export function ReverseTextTool() {
  const [result, setResult] = useState<string>('');
  const { toast } = useToast();

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zod<PERSON><PERSON>ol<PERSON>(FormSchema),
    defaultValues: {
      text: '',
    },
  });

  // [معزول, ابتدائي, وسطي, نهائي]
  const arabicForms: { [key: string]: string[] } = {
    'ا': ['ا', '', '', 'ﺎ'], 'أ': ['أ', '', '', 'ﺄ'], 'إ': ['إ', '', '', 'ﺈ'], 'آ': ['آ', '', '', 'ﺂ'],
    'ب': ['ب', 'ﺑ', 'ﺒ', 'ﺐ'], 'ت': ['ت', 'ﺗ', 'ﺘ', 'ﺖ'], 'ة': ['ة', '', '', 'ﺔ'],
    'ث': ['ث', 'ﺛ', 'ﺜ', 'ﺚ'], 'ج': ['ج', 'ﺟ', 'ﺠ', 'ﺞ'], 'ح': ['ح', 'ﺣ', 'ﺤ', 'ﺢ'],
    'خ': ['خ', 'ﺧ', 'ﺨ', 'ﺦ'], 'د': ['د', '', '', 'ﺪ'], 'ذ': ['ذ', '', '', 'ﺬ'],
    'ر': ['ر', '', '', 'ﺮ'], 'ز': ['ز', '', '', 'ﺰ'], 'س': ['س', 'ﺳ', 'ﺴ', 'ﺲ'],
    'ش': ['ش', 'ﺷ', 'ﺸ', 'ﺶ'], 'ص': ['ص', 'ﺻ', 'ﺼ', 'ﺺ'], 'ض': ['ض', 'ﺿ', 'ﻀ', 'ﺾ'],
    'ط': ['ط', 'ﻃ', 'ﻄ', 'ﻂ'], 'ظ': ['ظ', 'ﻇ', 'ﻈ', 'ﻆ'], 'ع': ['ع', 'ﻋ', 'ﻌ', 'ﻊ'],
    'غ': ['غ', 'ﻏ', 'ﻐ', 'ﻎ'], 'ف': ['ف', 'ﻓ', 'ﻔ', 'ﻒ'], 'ق': ['ق', 'ﻗ', 'ﻘ', 'ﻖ'],
    'ك': ['ك', 'ﻛ', 'ﻜ', 'ﻚ'], 'ل': ['ل', 'ﻟ', 'ﻠ', 'ﻞ'], 'م': ['م', 'ﻣ', 'ﻤ', 'ﻢ'],
    'ن': ['ن', 'ﻧ', 'ﻨ', 'ﻦ'], 'ه': ['ه', 'ﻫ', 'ﻬ', 'ﻪ'], 'و': ['و', '', '', 'ﻮ'],
    'ؤ': ['ؤ', '', '', 'ﺆ'], 'ي': ['ي', 'ﻳ', 'ﻴ', 'ﻲ'], 'ئ': ['ئ', 'ﺋ', 'ﺌ', 'ﺊ'],
    'ى': ['ى', '', '', 'ﻰ'],
    // حروف مركبة (Ligatures)
    'ﻻ': ['ﻻ', '', '', 'ﻼ'], 'ﻷ': ['ﻷ', '', '', 'ﻸ'], 'ﻹ': ['ﻹ', '', '', 'ﻺ'], 'ﻵ': ['ﻵ', '', '', 'ﻶ'],
  };

  const doesConnectForward = (char: string): boolean => arabicForms[char] && arabicForms[char][1] !== '';

  function processArabicText(text: string): string {
  // الخطوة 0: التشكيل - نحفظ التشكيل مع مواقعه
  const diacritics = /[\u064B-\u065F]/g; // جميع التشكيلات
  const diacriticMap: { [index: number]: string[] } = {};
  let stripped = '';

  // إزالة التشكيل وتخزينه
  [...text].forEach((ch, i) => {
    if (ch.match(diacritics)) {
      if (!diacriticMap[i - 1]) diacriticMap[i - 1] = [];
      diacriticMap[i - 1].push(ch);
    } else {
      stripped += ch;
    }
  });

  // الخطوة 1: معالجة "لام ألف" بدقة حتى لو بينها تشكيل
  let processedText = stripped
    .replace(/ل[ًٌٍَُِّْ]*[أ]/g, 'ﻷ')
    .replace(/ل[ًٌٍَُِّْ]*[إ]/g, 'ﻹ')
    .replace(/ل[ًٌٍَُِّْ]*[آ]/g, 'ﻵ')
    .replace(/ل[ًٌٍَُِّْ]*[ا]/g, 'ﻻ');

  const chars = [...processedText];
  let shapedText = '';

  for (let i = 0; i < chars.length; i++) {
    const currentChar = chars[i];
    const prevChar = chars[i - 1];
    const nextChar = chars[i + 1];

    const forms = arabicForms[currentChar];
    if (!forms) {
      shapedText += currentChar;
      continue;
    }

    const prevConnects = prevChar && doesConnectForward(prevChar);
    const nextIsConnectable = nextChar && arabicForms[nextChar];
    const currentConnects = doesConnectForward(currentChar);

    let formIndex = 0;
    if (prevConnects && currentConnects && nextIsConnectable) {
      formIndex = 2;
    } else if (prevConnects) {
      formIndex = 3;
    } else if (currentConnects && nextIsConnectable) {
      formIndex = 1;
    }

    let baseForm = forms[formIndex] || forms[0];

    // إضافة التشكيل من الديكرتيك ماب
    const diacs = diacriticMap[i];
    if (diacs && diacs.length) {
      baseForm += diacs.join('');
    }

    shapedText += baseForm;
  }

  return [...shapedText].reverse().join('');
}

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const processed = processArabicText(data.text);
    setResult(processed);
  }

  const copyToClipboard = () => {
    if (result) {
      navigator.clipboard.writeText(result).then(() => {
        toast({
          title: "تم النسخ",
          description: "تم نسخ النص المعكوس إلى الحافظة.",
        });
      });
    }
  };

  const handleSwap = () => {
    if (result) {
      form.setValue('text', result);
      setResult('');
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>🔁 عكس وتشكيل الحروف العربية تلقائيًا</CardTitle>
        <CardDescription>
          اكتب النص العربي في المربع أدناه ليتم عكسه مع الحفاظ على الشكل الصحيح للحروف المتصلة والمنفصلة.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="text"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>النص الأصلي</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="اكتب هنا... مثلاً: السلام عليكم ورحمة الله"
                      className="min-h-[150px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full">
              <ArrowRightLeft className="ml-2 h-4 w-4" />
              عكس وتشكيل النص
            </Button>
          </form>
        </Form>
        
        {result && (
          <div className="mt-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium">النتيجة:</h3>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={handleSwap}>
                  <RefreshCw className="h-4 w-4 ml-2" />
                  تبديل
                </Button>
                <Button variant="outline" size="sm" onClick={copyToClipboard}>
                  <Copy className="h-4 w-4 ml-2" />
                  نسخ
                </Button>
              </div>
            </div>
            <Textarea
              readOnly
              value={result}
              className="min-h-[150px] text-left bg-muted/50 border-2 border-primary/20 font-mono text-2xl"
              dir="ltr"
              style={{ wordBreak: 'break-all', whiteSpace: 'pre-wrap' }}
            />
            <div className="mt-2 text-sm text-muted-foreground">
              💡 النص الآن معكوس مع تطبيق الأشكال الصحيحة للحروف المتصلة والمنفصلة - جاهز للاستخدام في Photoshop والبرامج الأخرى
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
