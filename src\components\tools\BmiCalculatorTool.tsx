
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  height: requiredNumber().positive({ message: 'أدخل طولاً صحيحًا بالسنتيمتر.' }),
  weight: requiredNumber().positive({ message: 'أدخل وزنًا صحيحًا بالكيلوجرام.' }),
});

interface Result {
  bmi: number;
  category: string;
  color: string;
}

export function BmiCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });

  function getBmiCategory(bmi: number): { category: string, color: string } {
    if (bmi < 18.5) return { category: 'نقص في الوزن', color: 'bg-blue-400' };
    if (bmi < 25) return { category: 'وزن طبيعي', color: 'bg-blue-500' };
    if (bmi < 30) return { category: 'زيادة في الوزن', color: 'bg-yellow-400' };
    if (bmi < 35) return { category: 'سمنة (الدرجة الأولى)', color: 'bg-orange-500' };
    if (bmi < 40) return { category: 'سمنة (الدرجة الثانية)', color: 'bg-red-500' };
    return { category: 'سمنة مفرطة (الدرجة الثالثة)', color: 'bg-red-700' };
  }

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const heightInMeters = data.height / 100;
    const bmi = data.weight / (heightInMeters * heightInMeters);
    const { category, color } = getBmiCategory(bmi);
    setResult({ bmi, category, color });
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>حساب مؤشر كتلة الجسم</CardTitle>
        <CardDescription>أدخل التفاصيل أدناه لتقييم وزنك.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="weight"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>الوزن (كجم)</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="مثال: 70" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="height"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>الطول (سم)</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="مثال: 175" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full">
              احسب
            </Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-2">النتيجة</h3>
            <p className="text-5xl font-bold font-mono text-primary">{result.bmi.toFixed(1)}</p>
            <p className="text-lg font-medium mt-2">{result.category}</p>
            <div className="mt-4 px-4">
                <Progress value={Math.min(result.bmi, 50) * 2} indicatorClassName={result.color} />
                 <div className="w-full flex justify-between text-xs text-muted-foreground mt-1">
                    <span>18.5</span>
                    <span>25</span>
                    <span>30</span>
                    <span>40+</span>
                 </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
