'use server';

import { z } from 'zod';

const API_URL = 'https://open.er-api.com/v6/latest/USD';

const CurrencyResponseSchema = z.object({
  result: z.string(),
  rates: z.record(z.number()),
});

export async function getCurrencyRates() {
  try {
    const response = await fetch(API_URL, {
      next: { revalidate: 3600 }, // Revalidate every hour
    });

    if (!response.ok) {
      throw new Error('Failed to fetch currency rates');
    }

    const data = await response.json();
    const parsedData = CurrencyResponseSchema.safeParse(data);

    if (!parsedData.success) {
      throw new Error('Invalid data format from currency API');
    }

    return {
      success: true,
      rates: parsedData.data.rates,
      currencies: Object.keys(parsedData.data.rates),
    };
  } catch (error) {
    console.error('Currency API Error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
}
