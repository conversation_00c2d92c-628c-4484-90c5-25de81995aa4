'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

export function SimpleCalculatorTool() {
  const [display, setDisplay] = useState('0');
  const [equation, setEquation] = useState('');
  const [isResult, setIsResult] = useState(false);
  const [waitingForOperand, setWaitingForOperand] = useState(true);
  
  const handleNumberClick = (num: string) => {
    if (waitingForOperand) {
      setDisplay(num);
      setWaitingForOperand(false);
    } else {
      setDisplay(display === '0' ? num : display + num);
    }
    
    if (isResult) {
      setEquation('');
      setIsResult(false);
    }
  };

  const handleOperatorClick = (op: string) => {
    const currentValue = parseFloat(display);
    
    if (isResult) {
      // استخدام النتيجة السابقة للعملية الجديدة
      setEquation(display + ' ' + op);
      setWaitingForOperand(true);
      setIsResult(false);
    } else if (waitingForOperand) {
      // تغيير العملية الحالية إذا لم يتم إدخال رقم جديد
      if (equation.length > 0) {
        setEquation(equation.slice(0, equation.length - 1) + op);
      }
    } else {
      // إضافة الرقم والعملية إلى المعادلة
      setEquation(equation + (equation ? ' ' : '') + display + ' ' + op);
      setWaitingForOperand(true);
    }
  };

  const handleDecimalClick = () => {
    if (waitingForOperand) {
      setDisplay('0.');
      setWaitingForOperand(false);
    } else if (!display.includes('.')) {
      setDisplay(display + '.');
    }
    
    if (isResult) {
      setEquation('');
      setIsResult(false);
    }
  };

  const handleClearClick = () => {
    setDisplay('0');
    setEquation('');
    setIsResult(false);
    setWaitingForOperand(true);
  };

  const handleBackspaceClick = () => {
    if (waitingForOperand || isResult) return;
    
    if (display.length > 1) {
      setDisplay(display.substring(0, display.length - 1));
    } else {
      setDisplay('0');
      setWaitingForOperand(true);
    }
  };

  const handleEqualsClick = () => {
    try {
      if (equation === '' || waitingForOperand) {
        return;
      }
      
      // إضافة الرقم الأخير إلى المعادلة
      const fullEquation = `${equation} ${display}`;
      
      // استبدال علامات القسمة والضرب بعلامات جافاسكريبت
      const parsedEquation = fullEquation
        .replace(/×/g, '*')
        .replace(/÷/g, '/');
      
      // تنظيف المعادلة وتحويلها لصيغة قابلة للتنفيذ
      const evalReady = parsedEquation.trim();
      
      // تحذير: استخدام eval يمكن أن يكون خطيراً لكن في هذه الحالة البسيطة مع القيود المفروضة، يمكن استخدامه
      const result = eval(evalReady);
      
      // التحقق من النتيجة وعرضها بشكل مناسب
      let finalResult;
      if (Number.isInteger(result)) {
        finalResult = result.toString();
      } else {
        finalResult = parseFloat(result.toFixed(8)).toString();
      }
      
      setDisplay(finalResult);
      setEquation('');
      setIsResult(true);
      setWaitingForOperand(true);
    } catch (error) {
      setDisplay('خطأ');
      setEquation('');
      setIsResult(true);
      setWaitingForOperand(true);
    }
  };

  const calculatorButtons = [
    { value: '7', onClick: () => handleNumberClick('7') },
    { value: '8', onClick: () => handleNumberClick('8') },
    { value: '9', onClick: () => handleNumberClick('9') },
    { value: '÷', onClick: () => handleOperatorClick('÷'), variant: 'secondary' },
    { value: '4', onClick: () => handleNumberClick('4') },
    { value: '5', onClick: () => handleNumberClick('5') },
    { value: '6', onClick: () => handleNumberClick('6') },
    { value: '×', onClick: () => handleOperatorClick('×'), variant: 'secondary' },
    { value: '1', onClick: () => handleNumberClick('1') },
    { value: '2', onClick: () => handleNumberClick('2') },
    { value: '3', onClick: () => handleNumberClick('3') },
    { value: '-', onClick: () => handleOperatorClick('-'), variant: 'secondary' },
    { value: '0', onClick: () => handleNumberClick('0') },
    { value: '.', onClick: handleDecimalClick },
    { value: '=', onClick: handleEqualsClick, variant: 'default' },
    { value: '+', onClick: () => handleOperatorClick('+'), variant: 'secondary' },
  ];

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>آلة حاسبة بسيطة</CardTitle>
        <CardDescription>إجراء العمليات الحسابية الأساسية</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center space-x-2 space-x-reverse">
            <div className="flex-1">
              <Input
                value={display}
                readOnly
                className="text-xl text-left font-mono h-14 text-2xl"
              />
            </div>
            <Button
              variant="outline"
              onClick={handleBackspaceClick}
              className="px-3 h-14"
              aria-label="مسح آخر رقم"
            >
              ←
            </Button>
            <Button
              variant="destructive"
              onClick={handleClearClick}
              className="px-3 h-14"
              aria-label="مسح الكل"
            >
              C
            </Button>
          </div>
          
          <div className="grid grid-cols-4 gap-2">
            {calculatorButtons.map((button, index) => (
              <Button
                key={index}
                variant={button.variant as any || 'outline'}
                onClick={button.onClick}
                className="p-6 text-xl h-14"
              >
                {button.value}
              </Button>
            ))}
          </div>
          
          {equation && (
            <div className="text-sm text-muted-foreground mt-2 text-left font-mono overflow-x-auto">
              {equation} {isResult ? '' : display}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
