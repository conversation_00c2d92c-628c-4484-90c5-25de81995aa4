'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Shield, Cookie, Eye, BarChart3, MapPin, Clock } from 'lucide-react';
import Link from 'next/link';

interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
}

interface GDPRComplianceProps {
  onPreferencesChange?: (preferences: CookiePreferences) => void;
}

export function GDPRCompliance({ onPreferencesChange }: GDPRComplianceProps) {
  const [showBanner, setShowBanner] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true, // Always true, cannot be disabled
    analytics: false,
    marketing: false,
    functional: false,
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Check if user has already made a choice
    const savedPreferences = localStorage.getItem('gdpr-preferences');
    const consentGiven = localStorage.getItem('gdpr-consent-date');
    
    if (!savedPreferences || !consentGiven) {
      setShowBanner(true);
    } else {
      try {
        const parsed = JSON.parse(savedPreferences);
        setPreferences(parsed);
        onPreferencesChange?.(parsed);
      } catch (error) {
        console.error('Error parsing GDPR preferences:', error);
        setShowBanner(true);
      }
    }
  }, [onPreferencesChange]);

  const handleAcceptAll = () => {
    const allAccepted: CookiePreferences = {
      necessary: true,
      analytics: true,
      marketing: true,
      functional: true,
    };
    
    savePreferences(allAccepted);
    setShowBanner(false);
  };

  const handleRejectAll = () => {
    const onlyNecessary: CookiePreferences = {
      necessary: true,
      analytics: false,
      marketing: false,
      functional: false,
    };
    
    savePreferences(onlyNecessary);
    setShowBanner(false);
  };

  const handleCustomize = () => {
    setShowPreferences(true);
  };

  const handleSavePreferences = () => {
    savePreferences(preferences);
    setShowPreferences(false);
    setShowBanner(false);
  };

  const savePreferences = (prefs: CookiePreferences) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('gdpr-preferences', JSON.stringify(prefs));
      localStorage.setItem('gdpr-consent-date', new Date().toISOString());
    }
    setPreferences(prefs);
    onPreferencesChange?.(prefs);
  };

  const updatePreference = (key: keyof CookiePreferences, value: boolean) => {
    if (key === 'necessary') return; // Cannot disable necessary cookies
    
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  if (!showBanner && !showPreferences) return null;

  return (
    <>
      {/* GDPR Cookie Banner */}
      {showBanner && (
        <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-background/95 backdrop-blur-sm border-t shadow-lg">
          <div className="max-w-6xl mx-auto">
            <Card>
              <CardHeader className="pb-4">
                <div className="flex items-center gap-2">
                  <Cookie className="w-5 h-5 text-primary" />
                  <CardTitle className="text-lg">إعدادات الخصوصية وملفات تعريف الارتباط</CardTitle>
                </div>
                <CardDescription className="text-sm">
                  نحن نستخدم ملفات تعريف الارتباط لتحسين تجربتك وتقديم محتوى مخصص وتحليل حركة المرور. 
                  يمكنك اختيار أنواع ملفات تعريف الارتباط التي تريد السماح بها.
                  <Link href="/p/cookies" className="text-primary hover:underline mr-2">
                    اقرأ سياسة ملفات تعريف الارتباط
                  </Link>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button onClick={handleAcceptAll} className="flex-1">
                    قبول جميع ملفات تعريف الارتباط
                  </Button>
                  <Button onClick={handleRejectAll} variant="outline" className="flex-1">
                    رفض الكل (الضرورية فقط)
                  </Button>
                  <Button onClick={handleCustomize} variant="secondary" className="flex-1">
                    تخصيص الإعدادات
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Cookie Preferences Modal */}
      {showPreferences && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <div className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-primary" />
                <CardTitle>إعدادات الخصوصية المفصلة</CardTitle>
              </div>
              <CardDescription>
                اختر أنواع ملفات تعريف الارتباط التي تريد السماح بها. يمكنك تغيير هذه الإعدادات في أي وقت.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Necessary Cookies */}
              <div className="flex items-start justify-between space-x-4 rtl:space-x-reverse">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <Shield className="w-4 h-4 text-green-600" />
                    <h3 className="font-semibold">ملفات تعريف الارتباط الضرورية</h3>
                    <Badge variant="secondary">مطلوبة</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    هذه الملفات ضرورية لعمل الموقع بشكل صحيح ولا يمكن تعطيلها. تشمل إعدادات الأمان والتنقل الأساسي.
                  </p>
                </div>
                <Switch checked={true} disabled />
              </div>

              {/* Analytics Cookies */}
              <div className="flex items-start justify-between space-x-4 rtl:space-x-reverse">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <BarChart3 className="w-4 h-4 text-blue-600" />
                    <h3 className="font-semibold">ملفات تعريف الارتباط التحليلية</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    تساعدنا في فهم كيفية استخدام الزوار للموقع لتحسين الأداء والمحتوى. تشمل Google Analytics.
                  </p>
                </div>
                <Switch 
                  checked={preferences.analytics}
                  onCheckedChange={(checked) => updatePreference('analytics', checked)}
                />
              </div>

              {/* Marketing Cookies */}
              <div className="flex items-start justify-between space-x-4 rtl:space-x-reverse">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <Eye className="w-4 h-4 text-purple-600" />
                    <h3 className="font-semibold">ملفات تعريف الارتباط التسويقية</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    تُستخدم لعرض إعلانات مخصصة وقياس فعالية الحملات الإعلانية. تشمل Google AdSense.
                  </p>
                </div>
                <Switch 
                  checked={preferences.marketing}
                  onCheckedChange={(checked) => updatePreference('marketing', checked)}
                />
              </div>

              {/* Functional Cookies */}
              <div className="flex items-start justify-between space-x-4 rtl:space-x-reverse">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <MapPin className="w-4 h-4 text-orange-600" />
                    <h3 className="font-semibold">ملفات تعريف الارتباط الوظيفية</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    تحفظ تفضيلاتك وإعداداتك لتحسين تجربة الاستخدام، مثل اللغة والموقع.
                  </p>
                </div>
                <Switch 
                  checked={preferences.functional}
                  onCheckedChange={(checked) => updatePreference('functional', checked)}
                />
              </div>

              <div className="pt-4 border-t">
                <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
                  <Clock className="w-4 h-4" />
                  <span>يمكنك تغيير هذه الإعدادات في أي وقت من خلال النقر على أيقونة ملفات تعريف الارتباط في أسفل الصفحة.</span>
                </div>
                <div className="flex gap-3">
                  <Button onClick={handleSavePreferences} className="flex-1">
                    حفظ الإعدادات
                  </Button>
                  <Button 
                    onClick={() => setShowPreferences(false)} 
                    variant="outline"
                  >
                    إلغاء
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
}

// Cookie Settings Button (to reopen preferences)
export function CookieSettingsButton() {
  const [showPreferences, setShowPreferences] = useState(false);

  return (
    <>
      <Button
        onClick={() => setShowPreferences(true)}
        variant="ghost"
        size="sm"
        className="fixed bottom-4 left-4 z-40 bg-background/80 backdrop-blur-sm border shadow-lg"
      >
        <Cookie className="w-4 h-4 ml-2" />
        إعدادات ملفات تعريف الارتباط
      </Button>

      {showPreferences && (
        <GDPRCompliance onPreferencesChange={() => setShowPreferences(false)} />
      )}
    </>
  );
}

// Data Processing Information Component
export function DataProcessingInfo() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">معلومات معالجة البيانات</h2>
        <p className="text-muted-foreground">
          نحن ملتزمون بحماية خصوصيتك ومعالجة بياناتك الشخصية بشفافية كاملة وفقاً للائحة العامة لحماية البيانات (GDPR).
        </p>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              البيانات التي نجمعها
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li>• معلومات الاستخدام (الصفحات المزارة، الوقت المقضي)</li>
              <li>• البيانات التقنية (نوع المتصفح، عنوان IP، نوع الجهاز)</li>
              <li>• تفضيلات ملفات تعريف الارتباط</li>
              <li>• البيانات المدخلة في الأدوات (لا يتم حفظها على خوادمنا)</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5" />
              كيف نستخدم بياناتك
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li>• تحسين أداء الموقع وتجربة المستخدم</li>
              <li>• تحليل أنماط الاستخدام لتطوير أدوات جديدة</li>
              <li>• عرض إعلانات ذات صلة (بموافقتك)</li>
              <li>• ضمان أمان الموقع ومنع الاستخدام المسيء</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="w-5 h-5" />
              حقوقك
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li>• الحق في الوصول إلى بياناتك الشخصية</li>
              <li>• الحق في تصحيح البيانات غير الصحيحة</li>
              <li>• الحق في حذف بياناتك (الحق في النسيان)</li>
              <li>• الحق في تقييد معالجة البيانات</li>
              <li>• الحق في نقل البيانات</li>
              <li>• الحق في الاعتراض على المعالجة</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8 p-4 bg-muted rounded-lg">
        <p className="text-sm text-muted-foreground">
          لممارسة أي من هذه الحقوق أو لأي استفسارات حول معالجة البيانات، يرجى التواصل معنا عبر صفحة
          <Link href="/p/contact" className="text-primary hover:underline mx-1">اتصل بنا</Link>
          أو إرسال بريد إلكتروني إلى <EMAIL>
        </p>
      </div>
    </div>
  );
}
