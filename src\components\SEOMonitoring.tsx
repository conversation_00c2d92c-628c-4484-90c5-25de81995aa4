'use client';

import { useEffect } from 'react';

interface SEOMonitoringProps {
  pageTitle?: string;
  pageUrl?: string;
  pageType?: 'homepage' | 'tool' | 'category' | 'article' | 'page';
}

export function SEOMonitoring({ pageTitle, pageUrl, pageType = 'page' }: SEOMonitoringProps) {
  useEffect(() => {
    // Monitor page load performance for SEO
    monitorPagePerformance();
    
    // Check SEO elements
    validateSEOElements();
    
    // Monitor Core Web Vitals
    monitorCoreWebVitals();
    
    // Track page view for analytics
    trackPageView(pageTitle, pageUrl, pageType);
    
  }, [pageTitle, pageUrl, pageType]);

  return null; // This is a monitoring component, no UI
}

function monitorPagePerformance() {
  if (typeof window === 'undefined') return;

  // Monitor page load time
  window.addEventListener('load', () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    if (navigation) {
      const metrics = {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstByte: navigation.responseStart - navigation.requestStart,
        domInteractive: navigation.domInteractive - navigation.fetchStart,
        totalLoadTime: navigation.loadEventEnd - navigation.fetchStart
      };

      // Send to analytics if performance is poor
      if (metrics.totalLoadTime > 3000) {
        sendSEOAlert('slow_page_load', {
          loadTime: metrics.totalLoadTime,
          url: window.location.href
        });
      }

      // Log in development
      if (process.env.NODE_ENV === 'development') {
        console.log('[SEO Performance]', metrics);
      }
    }
  });
}

function validateSEOElements() {
  if (typeof window === 'undefined') return;

  const seoChecks = {
    hasTitle: !!document.title,
    titleLength: document.title.length,
    hasMetaDescription: !!document.querySelector('meta[name="description"]'),
    hasCanonical: !!document.querySelector('link[rel="canonical"]'),
    hasOpenGraph: !!document.querySelector('meta[property^="og:"]'),
    hasTwitterCard: !!document.querySelector('meta[name^="twitter:"]'),
    hasStructuredData: !!document.querySelector('script[type="application/ld+json"]'),
    hasH1: !!document.querySelector('h1'),
    h1Count: document.querySelectorAll('h1').length,
    hasAltTexts: Array.from(document.querySelectorAll('img')).every(img => img.alt),
    hasLangAttribute: !!document.documentElement.lang,
    hasDirAttribute: !!document.documentElement.dir
  };

  // Check for SEO issues
  const issues = [];
  
  if (!seoChecks.hasTitle) issues.push('missing_title');
  if (seoChecks.titleLength > 60) issues.push('title_too_long');
  if (seoChecks.titleLength < 30) issues.push('title_too_short');
  if (!seoChecks.hasMetaDescription) issues.push('missing_meta_description');
  if (!seoChecks.hasCanonical) issues.push('missing_canonical');
  if (!seoChecks.hasH1) issues.push('missing_h1');
  if (seoChecks.h1Count > 1) issues.push('multiple_h1');
  if (!seoChecks.hasAltTexts) issues.push('missing_alt_texts');
  if (!seoChecks.hasStructuredData) issues.push('missing_structured_data');

  // Send alerts for critical issues
  if (issues.length > 0) {
    sendSEOAlert('seo_validation_issues', {
      issues,
      url: window.location.href,
      checks: seoChecks
    });
  }

  // Log in development
  if (process.env.NODE_ENV === 'development') {
    console.log('[SEO Validation]', { checks: seoChecks, issues });
  }
}

function monitorCoreWebVitals() {
  if (typeof window === 'undefined') return;

  // Monitor Largest Contentful Paint (LCP)
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'largest-contentful-paint') {
        const lcp = entry.startTime;
        
        if (lcp > 2500) { // Poor LCP
          sendSEOAlert('poor_lcp', {
            value: lcp,
            url: window.location.href
          });
        }
        
        if (process.env.NODE_ENV === 'development') {
          console.log('[Core Web Vitals] LCP:', lcp);
        }
      }
    }
  });

  observer.observe({ entryTypes: ['largest-contentful-paint'] });

  // Monitor Cumulative Layout Shift (CLS)
  let clsValue = 0;
  const clsObserver = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (!(entry as any).hadRecentInput) {
        clsValue += (entry as any).value;
      }
    }
    
    if (clsValue > 0.1) { // Poor CLS
      sendSEOAlert('poor_cls', {
        value: clsValue,
        url: window.location.href
      });
    }
  });

  clsObserver.observe({ entryTypes: ['layout-shift'] });
}

function trackPageView(pageTitle?: string, pageUrl?: string, pageType?: string) {
  if (typeof window === 'undefined') return;

  // Send to Google Analytics if available
  if ((window as any).gtag) {
    (window as any).gtag('event', 'page_view', {
      page_title: pageTitle || document.title,
      page_location: pageUrl || window.location.href,
      page_type: pageType,
      language: document.documentElement.lang || 'ar',
      user_agent: navigator.userAgent,
      screen_resolution: `${screen.width}x${screen.height}`,
      viewport_size: `${window.innerWidth}x${window.innerHeight}`
    });
  }

  // Track Arabic-specific metrics
  const arabicMetrics = {
    isArabicContent: /[\u0600-\u06FF]/.test(document.body.textContent || ''),
    isRTL: document.documentElement.dir === 'rtl',
    arabicFontLoaded: document.fonts.check('16px Tajawal'),
    pageLanguage: document.documentElement.lang
  };

  if (process.env.NODE_ENV === 'development') {
    console.log('[Arabic SEO Metrics]', arabicMetrics);
  }
}

function sendSEOAlert(type: string, data: any) {
  // In production, this would send to your monitoring service
  if (process.env.NODE_ENV === 'development') {
    console.warn(`[SEO Alert] ${type}:`, data);
  }

  // Send to Google Analytics as an event
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', 'seo_issue', {
      event_category: 'SEO Monitoring',
      event_label: type,
      custom_parameters: data
    });
  }
}

// SEO Health Check Hook
export function useSEOHealthCheck() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const runHealthCheck = () => {
      const healthScore = calculateSEOHealthScore();
      
      if (healthScore < 80) {
        sendSEOAlert('low_seo_health_score', {
          score: healthScore,
          url: window.location.href
        });
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('[SEO Health Score]', healthScore);
      }
    };

    // Run health check after page load
    setTimeout(runHealthCheck, 2000);
  }, []);
}

function calculateSEOHealthScore(): number {
  let score = 100;
  
  // Title checks
  if (!document.title) score -= 20;
  else if (document.title.length > 60 || document.title.length < 30) score -= 10;
  
  // Meta description
  const metaDesc = document.querySelector('meta[name="description"]') as HTMLMetaElement;
  if (!metaDesc) score -= 15;
  else if (metaDesc.content.length > 160 || metaDesc.content.length < 120) score -= 5;
  
  // Heading structure
  const h1s = document.querySelectorAll('h1');
  if (h1s.length === 0) score -= 15;
  else if (h1s.length > 1) score -= 10;
  
  // Images without alt text
  const imagesWithoutAlt = Array.from(document.querySelectorAll('img')).filter(img => !img.alt);
  score -= imagesWithoutAlt.length * 2;
  
  // Canonical URL
  if (!document.querySelector('link[rel="canonical"]')) score -= 10;
  
  // Structured data
  if (!document.querySelector('script[type="application/ld+json"]')) score -= 10;
  
  // Open Graph
  if (!document.querySelector('meta[property^="og:"]')) score -= 5;
  
  // Language and direction
  if (!document.documentElement.lang) score -= 5;
  if (!document.documentElement.dir) score -= 5;
  
  return Math.max(0, score);
}

// Arabic SEO Specific Monitoring
export function useArabicSEOMonitoring() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const checkArabicSEO = () => {
      const issues = [];
      
      // Check RTL direction
      if (document.documentElement.dir !== 'rtl') {
        issues.push('missing_rtl_direction');
      }
      
      // Check Arabic language attribute
      if (!document.documentElement.lang.startsWith('ar')) {
        issues.push('incorrect_language_attribute');
      }
      
      // Check Arabic font loading
      if (!document.fonts.check('16px Tajawal')) {
        issues.push('arabic_font_not_loaded');
      }
      
      // Check for Arabic content
      const hasArabicText = /[\u0600-\u06FF]/.test(document.body.textContent || '');
      if (!hasArabicText) {
        issues.push('no_arabic_content_detected');
      }
      
      // Check Arabic number formatting
      const hasWesternNumerals = /[0-9]/.test(document.body.textContent || '');
      const hasArabicNumerals = /[٠-٩]/.test(document.body.textContent || '');
      
      if (hasWesternNumerals && !hasArabicNumerals) {
        // This is actually fine for modern Arabic websites
        // Most use Western numerals for better international compatibility
      }
      
      if (issues.length > 0) {
        sendSEOAlert('arabic_seo_issues', {
          issues,
          url: window.location.href
        });
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.log('[Arabic SEO Check]', { issues });
      }
    };

    setTimeout(checkArabicSEO, 1000);
  }, []);
}

// Export monitoring utilities
export const seoMonitoringUtils = {
  calculateSEOHealthScore,
  validateSEOElements,
  monitorPagePerformance,
  sendSEOAlert
};
