
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  gender: z.enum(['male', 'female'], { required_error: 'الرجاء تحديد الجنس.' }),
  age: requiredNumber().int().min(1, 'العمر يجب أن يكون 1 أو أكثر.').max(120),
  weight: requiredNumber().positive('الوزن يجب أن يكون رقمًا موجبًا.'),
  height: requiredNumber().positive('الطول يجب أن يكون رقمًا موجبًا.'),
  activityLevel: z.string().min(1, 'الرجاء اختيار مستوى النشاط.'),
});

interface Result {
  bmr: number;
  maintenance: number;
  weightLoss: number;
  weightGain: number;
}

const activityFactors: { [key: string]: { factor: number; label: string } } = {
  sedentary: { factor: 1.2, label: 'خامل (عمل مكتبي، قليل أو بدون رياضة)' },
  light: { factor: 1.375, label: 'نشاط خفيف (رياضة خفيفة 1-3 أيام/أسبوع)' },
  moderate: { factor: 1.55, label: 'نشاط متوسط (رياضة معتدلة 3-5 أيام/أسبوع)' },
  active: { factor: 1.725, label: 'نشيط (رياضة شديدة 6-7 أيام/أسبوع)' },
  veryActive: { factor: 1.9, label: 'نشيط جدًا (رياضة شديدة جدًا وعمل بدني)' },
};

export function CaloriesCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const { gender, age, weight, height, activityLevel } = data;
    let bmr: number;
    if (gender === 'male') {
      bmr = 10 * weight + 6.25 * height - 5 * age + 5;
    } else {
      bmr = 10 * weight + 6.25 * height - 5 * age - 161;
    }

    const maintenance = bmr * activityFactors[activityLevel].factor;

    setResult({
      bmr: Math.round(bmr),
      maintenance: Math.round(maintenance),
      weightLoss: Math.round(maintenance - 500),
      weightGain: Math.round(maintenance + 500),
    });
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حاسبة السعرات الحرارية (BMR)</CardTitle>
        <CardDescription>احسب احتياجك اليومي من السعرات الحرارية للحفاظ على وزنك أو تغييره.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem className="space-y-3 md:col-span-2">
                    <FormLabel>الجنس</FormLabel>
                    <FormControl className="flex flex-col items-end" >
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex space-x-4 space-x-reverse"
                      >
                        <div className="">
                            <FormItem className="flex flex-row-reverse items-center space-x-2 space-x-reverse">
                              <FormControl><RadioGroupItem value="male" /></FormControl>
                              <FormLabel className="font-normal">ذكر</FormLabel>
                            </FormItem>
                            <FormItem className="flex flex-row-reverse items-center space-x-2 space-x-reverse">
                              <FormControl><RadioGroupItem value="female" /></FormControl>
                              <FormLabel className="font-normal">أنثى</FormLabel>
                            </FormItem>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField name="age" control={form.control} render={({ field }) => (
                  <FormItem><FormLabel>العمر (بالسنوات)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
                )} />
              <FormField name="weight" control={form.control} render={({ field }) => (
                  <FormItem><FormLabel>الوزن (كجم)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
                )} />
              <FormField name="height" control={form.control} render={({ field }) => (
                  <FormItem><FormLabel>الطول (سم)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
                )} />
              <FormField
                control={form.control}
                name="activityLevel"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>مستوى النشاط</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl><SelectTrigger><SelectValue placeholder="اختر مستوى نشاطك" /></SelectTrigger></FormControl>
                      <SelectContent>
                        {Object.entries(activityFactors).map(([key, value]) => (
                          <SelectItem key={key} value={key}>{value.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Button type="submit" className="w-full">احسب السعرات</Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center space-y-4">
             <h3 className="text-xl font-headline font-semibold">النتائج</h3>
             <p className="text-muted-foreground">هذه الأرقام تقديرية. استشر مختصًا للحصول على توصيات شخصية.</p>
             <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="p-4 bg-secondary rounded-lg"><p className="text-sm text-muted-foreground">معدل الأيض الأساسي</p><p className="text-2xl font-bold font-mono text-primary">{result.bmr}</p><p className="text-xs text-muted-foreground">سعرة/يوم</p></div>
                <div className="p-4 bg-primary/10 rounded-lg"><p className="text-sm text-muted-foreground">للحفاظ على الوزن</p><p className="text-2xl font-bold font-mono text-primary">{result.maintenance}</p><p className="text-xs text-muted-foreground">سعرة/يوم</p></div>
                <div className="p-4 bg-green-500/10 rounded-lg"><p className="text-sm text-muted-foreground">لخسارة الوزن</p><p className="text-2xl font-bold font-mono text-green-600">{result.weightLoss}</p><p className="text-xs text-muted-foreground">~0.5 كجم/أسبوع</p></div>
                <div className="p-4 bg-blue-500/10 rounded-lg"><p className="text-sm text-muted-foreground">لزيادة الوزن</p><p className="text-2xl font-bold font-mono text-blue-600">{result.weightGain}</p><p className="text-xs text-muted-foreground">~0.5 كجم/أسبوع</p></div>
             </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
