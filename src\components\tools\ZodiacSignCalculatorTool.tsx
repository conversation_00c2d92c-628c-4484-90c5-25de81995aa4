

'use client';

import { useState } from 'react';
import { useForm, Control } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const zodiacSigns = [
  { name: 'الحمل', date: '21 مارس – 19 أبريل', start: { month: 3, day: 21 }, end: { month: 4, day: 19 }, symbol: '♈' },
  { name: 'الثور', date: '20 أبريل – 20 مايو', start: { month: 4, day: 20 }, end: { month: 5, day: 20 }, symbol: '♉' },
  { name: 'الجوزاء', date: '21 مايو – 20 يونيو', start: { month: 5, day: 21 }, end: { month: 6, day: 20 }, symbol: '♊' },
  { name: 'السرطان', date: '21 يونيو – 22 يوليو', start: { month: 6, day: 21 }, end: { month: 7, day: 22 }, symbol: '♋' },
  { name: 'الأسد', date: '23 يوليو – 22 أغسطس', start: { month: 7, day: 23 }, end: { month: 8, day: 22 }, symbol: '♌' },
  { name: 'العذراء', date: '23 أغسطس – 22 سبتمبر', start: { month: 8, day: 23 }, end: { month: 9, day: 22 }, symbol: '♍' },
  { name: 'الميزان', date: '23 سبتمبر – 22 أكتوبر', start: { month: 9, day: 23 }, end: { month: 10, day: 22 }, symbol: '♎' },
  { name: 'العقرب', date: '23 أكتوبر – 21 نوفمبر', start: { month: 10, day: 23 }, end: { month: 11, day: 21 }, symbol: '♏' },
  { name: 'القوس', date: '22 نوفمبر – 21 ديسمبر', start: { month: 11, day: 22 }, end: { month: 12, day: 21 }, symbol: '♐' },
  { name: 'الجدي', date: '22 ديسمبر – 19 يناير', start: { month: 12, day: 22 }, end: { month: 1, day: 19 }, symbol: '♑' },
  { name: 'الدلو', date: '20 يناير – 18 فبراير', start: { month: 1, day: 20 }, end: { month: 2, day: 18 }, symbol: '♒' },
  { name: 'الحوت', date: '19 فبراير – 20 مارس', start: { month: 2, day: 19 }, end: { month: 3, day: 20 }, symbol: '♓' },
];

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  day: requiredNumber('اليوم مطلوب'),
  month: requiredNumber('الشهر مطلوب'),
}).refine(data => {
    try {
        const year = 2000; // Use a leap year for validation
        const date = new Date(year, data.month - 1, data.day);
        return date.getMonth() === data.month - 1 && date.getDate() === data.day;
    } catch {
        return false;
    }
}, {
    message: 'التاريخ المدخل غير صالح.',
    path: ['day'],
});

type FormValues = z.infer<typeof FormSchema>;
type ZodiacSign = typeof zodiacSigns[0];

export function ZodiacSignCalculatorTool() {
  const [result, setResult] = useState<ZodiacSign | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      day: undefined,
      month: undefined,
    }
  });

  function getZodiacSign(day: number, month: number): ZodiacSign | null {
    for (const sign of zodiacSigns) {
        const { start, end } = sign;
        // Handle signs that span across the new year (like Capricorn)
        if (start.month > end.month) {
            if ((month === start.month && day >= start.day) || (month === end.month && day <= end.day)) {
                return sign;
            }
        } else {
            if ((month === start.month && day >= start.day) || (month === end.month && day <= end.day) || (month > start.month && month < end.month)) {
                return sign;
            }
        }
    }
    return null;
  }
  

  function onSubmit(data: FormValues) {
    const sign = getZodiacSign(data.day, data.month);
    setResult(sign);
  }

  const daysOptions = Array.from({ length: 31 }, (_, i) => i + 1);
  const monthsOptions = Array.from({ length: 12 }, (_, i) => i + 1);

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>ما هو برجي؟</CardTitle>
        <CardDescription>أدخل يوم وشهر ميلادك لاكتشاف برجك الشمسي.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
                <FormField
                    control={form.control}
                    name="day"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>يوم الميلاد</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value?.toString()}>
                                <FormControl><SelectTrigger><SelectValue placeholder="اختر اليوم" /></SelectTrigger></FormControl>
                                <SelectContent>{daysOptions.map(d => <SelectItem key={d} value={String(d)}>{d}</SelectItem>)}</SelectContent>
                            </Select>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                 <FormField
                    control={form.control}
                    name="month"
                    render={({ field }) => (
                    <FormItem>
                        <FormLabel>شهر الميلاد</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value?.toString()}>
                        <FormControl>
                            <SelectTrigger>
                                <SelectValue placeholder="اختر الشهر" />
                            </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {monthsOptions.map(month => <SelectItem key={month} value={String(month)}>{month}</SelectItem>)}
                        </SelectContent>
                        </Select>
                        <FormMessage />
                    </FormItem>
                    )}
                />
            </div>
            <Button type="submit" className="w-full">
              اكتشف برجي
            </Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center p-6 bg-primary/10 rounded-lg border border-primary/20">
              <p className="text-6xl mb-4">{result.symbol}</p>
              <h3 className="text-2xl font-headline font-semibold">برجك هو {result.name}</h3>
              <p className="text-muted-foreground mt-2 font-mono">{result.date}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
