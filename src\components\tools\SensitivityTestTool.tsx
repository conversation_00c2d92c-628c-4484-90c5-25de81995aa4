
'use client';

import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Repeat, User, UserRound, Droplets } from 'lucide-react';
import { cn } from '@/lib/utils';

type Gender = 'male' | 'female';

const getQuestionText = (question: any, gender: Gender): string => {
  if (typeof question.text === 'string') {
    return question.text;
  }
  return question.text[gender];
};

const getAnswerText = (answer: any, gender: Gender): string => {
    if (typeof answer.text === 'string') {
      return answer.text;
    }
    return answer.text[gender];
  };

type Answer = { text: string | { male: string; female: string }; points: number };
type Question = { text: string | { male: string; female: string }; answers: Answer[] };


const questions: Question[] = [
  // Emotional Sensitivity
  {
    text: "عندما تشاهد فيلمًا مؤثرًا، هل تجد نفسك تتأثر بشدة وقد تبكي؟",
    answers: [
      { text: "نعم، أتأثر بعمق وأعيش المشاعر بقوة.", points: 4 },
      { text: "أحيانًا، إذا كانت القصة قوية جدًا.", points: 3 },
      { text: "نادرًا ما أتأثر بهذه الطريقة.", points: 2 },
      { text: "لا، أنا أستمتع بالفيلم ولكن دون تأثر عاطفي عميق.", points: 1 },
    ],
  },
  {
    text: "كيف تتعامل مع مزاج الآخرين من حولك؟",
    answers: [
      { text: "أتأثر به بشكل كبير، سواء كان إيجابيًا أو سلبيًا، وكأنه مزاجي الخاص.", points: 4 },
      { text: "ألاحظ مزاجهم وأحاول أن أكون متعاطفًا.", points: 3 },
      { text: "ألاحظه، لكن لا أتركه يؤثر عليّ بشكل كبير.", points: 2 },
      { text: "لا ألاحظ عادةً تغيرات مزاج الآخرين الدقيقة.", points: 1 },
    ],
  },
  // Sensory Sensitivity
  {
    text: "هل تزعجك الأصوات العالية المفاجئة أو الأضواء الساطعة؟",
    answers: [
      { text: "نعم، تزعجني بشدة وأحاول تجنبها.", points: 4 },
      { text: "تزعجني قليلاً، لكن أتكيف معها.", points: 3 },
      { text: "لا تزعجني في العادة.", points: 2 },
      { text: "لا ألاحظها على الإطلاق.", points: 1 },
    ],
  },
  {
    text: "هل تلاحظ التفاصيل الدقيقة في بيئتك، مثل تغيير بسيط في ديكور غرفة أو رائحة خفيفة؟",
    answers: [
      { text: "نعم، أنا شديد الملاحظة للتفاصيل الدقيقة التي قد لا يراها الآخرون.", points: 4 },
      { text: "ألاحظ بعض التفاصيل إذا كانت واضحة.", points: 3 },
      { text: "نادرًا ما ألاحظ مثل هذه التغييرات.", points: 2 },
      { text: "أنا أركز على الصورة الكبيرة ولا أهتم بالتفاصيل الصغيرة.", points: 1 },
    ],
  },
  {
    text: "كيف يؤثر عليك يوم حافل بالأنشطة والضوضاء؟",
    answers: [
      { text: "أشعر بالإرهاق الشديد وأحتاج إلى وقت بمفردي في مكان هادئ لاستعادة طاقتي.", points: 4 },
      { text: "أشعر بالتعب، ولكنه تعب طبيعي بعد يوم حافل.", points: 3 },
      { text: "أستمتع بالأيام الحافلة ولا تؤثر عليّ كثيرًا.", points: 2 },
      { text: "أشعر بالحيوية والنشاط بعد يوم حافل.", points: 1 },
    ],
  },
  // Depth of Processing
  {
    text: "هل تميل إلى التفكير بعمق في الأمور قبل اتخاذ قرار؟",
    answers: [
      { text: "نعم، أفكر في كل الاحتمالات والنتائج المترتبة بعمق شديد.", points: 4 },
      { text: "أفكر في الخيارات الرئيسية، ولكن لا أغوص في التفاصيل كثيرًا.", points: 3 },
      { text: "أميل إلى اتخاذ قرارات سريعة بناءً على حدسي.", points: 2 },
      { text: "أتخذ قراراتي بسرعة وبشكل تلقائي.", points: 1 },
    ],
  },
  {
    text: "هل تشعر بأن لديك حياة داخلية غنية ومعقدة؟",
    answers: [
      { text: "نعم، أقضي الكثير من الوقت في التفكير والتأمل الذاتي.", points: 4 },
      { text: "إلى حد ما، أفكر في الأمور من وقت لآخر.", points: 3 },
      { text: "لا، أنا شخص عملي وأركز على العالم الخارجي.", points: 2 },
      { text: "لا أفهم حقًا معنى 'حياة داخلية غنية'.", points: 1 },
    ],
  },
  // Overstimulation
  {
    text: "عندما يكون لديك الكثير من المهام للقيام بها في وقت قصير، كيف تشعر؟",
    answers: [
      { text: "أشعر بالارتباك والإرهاق الشديد، وأجد صعوبة في التركيز.", points: 4 },
      { text: "أشعر ببعض الضغط، ولكني أبدأ في تنظيم المهام.", points: 3 },
      { text: "أشعر بالتحفيز وأعمل بشكل أفضل تحت الضغط.", points: 2 },
      { text: "لا أشعر بالضغط، أتعامل مع كل مهمة على حدة.", points: 1 },
    ],
  },
  // Empathy
  {
    text: "هل يؤثر عليك الفن أو الموسيقى بعمق؟",
    answers: [
      { text: "نعم، يمكن لقطعة فنية أو موسيقية أن تثير فيّ مشاعر قوية جدًا.", points: 4 },
      { text: "أقدر الفن والموسيقى وأستمتع بهما.", points: 3 },
      { text: "أستمع للموسيقى أو أرى الفن كشكل من أشكال الترفيه.", points: 2 },
      { text: "لا أهتم كثيرًا بالفنون أو الموسيقى.", points: 1 },
    ],
  },
  {
    text: "هل وصفك الآخرون بأنك 'حساس' أو 'تأخذ الأمور على محمل شخصي'؟",
    answers: [
      { text: "نعم، كثيرًا.", points: 4 },
      { text: "في بعض الأحيان.", points: 3 },
      { text: "نادرًا.", points: 2 },
      { text: "أبدًا.", points: 1 },
    ],
  },
];

const getResult = (score: number, gender: Gender) => {
    const results = {
      level1: {
        title: "شخصية عملية ومرنة",
        text: "أنت شخص يتمتع بقدرة عالية على التكيف مع مختلف الظروف والبيئات. لا تتأثر بسهولة بالضوضاء أو الفوضى، وتتعامل مع الضغوط بهدوء وثبات. هذه المرونة العملية تجعلك قادرًا على التركيز على أهدافك وإنجاز المهام بكفاءة عالية."
      },
      level2: {
        title: "شخصية متوازنة",
        text: "لديك توازن جيد بين الحساسية والمرونة. أنت قادر على التعاطف مع الآخرين وتقدير التفاصيل، ولكنك في نفس الوقت لا تسمح للمحفزات الخارجية بأن ترهقك بسهولة. هذا التوازن يسمح لك بالتفاعل مع العالم بعمق دون أن تفقد هدوءك."
      },
      level3: {
        title: "شخصية ذات حساسية ملحوظة",
        text: "أنت تتمتع بحساسية أعلى من المتوسط، مما يجعلك شخصًا متعاطفًا، ومبدعًا، وشديد الملاحظة للتفاصيل الدقيقة. قد تشعر بالإرهاق أحيانًا بسبب المحفزات الزائدة، لكن هذه الحساسية هي أيضًا مصدر قوتك، حيث تمنحك فهمًا أعمق للعالم ولمشاعر الآخرين."
      },
      level4: {
        title: "شخصية شديدة الحساسية (HSP)",
        text: "أنت تنتمي على الأرجح إلى فئة الأشخاص ذوي الحساسية العالية. هذه ليست نقطة ضعف، بل هي سمة شخصية تتميز بالمعالجة العميقة للمعلومات، والتعاطف الشديد، والوعي الكبير بالتفاصيل الدقيقة. قد تحتاج إلى بيئة هادئة لإعادة شحن طاقتك، لكن حساسيتك تمنحك رؤى فريدة وقدرة استثنائية على الإبداع والتواصل الإنساني العميق."
      }
    };
  
    if (score <= 15) {
      return { level: results.level1.title, description: results.level1.text };
    }
    if (score <= 25) {
      return { level: results.level2.title, description: results.level2.text };
    }
    if (score <= 35) {
      return { level: results.level3.title, description: results.level3.text };
    }
    return { level: results.level4.title, description: results.level4.text };
};

type TestStage = 'gender' | 'questions' | 'result';

function shuffleArray<T>(array: T[]): T[] {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

export function SensitivityTestTool() {
  const [stage, setStage] = useState<TestStage>('gender');
  const [gender, setGender] = useState<Gender | null>(null);
  const [shuffledQuestions, setShuffledQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  
  const isFinished = stage === 'result';
  const totalScore = answers.reduce((sum, score) => sum + score, 0);
  const result = isFinished && gender ? getResult(totalScore, gender) : { level: '', description: '' };

  const startTest = (selectedGender: Gender) => {
    setGender(selectedGender);
    setAnswers([]);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setShuffledQuestions(shuffleArray(questions));
    setStage('questions');
  };

  const handleNextQuestion = () => {
    if (selectedAnswer === null) return;
    
    const newAnswers = [...answers, selectedAnswer];
    setAnswers(newAnswers);
    setSelectedAnswer(null);

    if (currentQuestionIndex < shuffledQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      setStage('result');
    }
  };

  const handleRestart = () => {
    setStage('gender');
    setGender(null);
  };
  
  const progress = isFinished ? 100 : ((currentQuestionIndex) / shuffledQuestions.length) * 100;
  
  const currentQuestion = shuffledQuestions[currentQuestionIndex];
  
  const shuffledAnswers = useMemo(() => {
    if (!currentQuestion) return [];
    return shuffleArray(currentQuestion.answers);
  }, [currentQuestion]);


  const renderContent = () => {
    switch(stage) {
      case 'gender':
        return (
          <div className="text-center space-y-6">
            <h3 className="text-lg font-semibold">قبل أن نبدأ، يرجى تحديد جنسك</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Button variant="outline" className="h-24 text-xl" onClick={() => startTest('male')}>
                <User className="ml-3 h-8 w-8" />
                ذكر
              </Button>
              <Button variant="outline" className="h-24 text-xl" onClick={() => startTest('female')}>
                <UserRound className="ml-3 h-8 w-8" />
                أنثى
              </Button>
            </div>
          </div>
        );
      
      case 'questions':
        if (!gender || !currentQuestion) return null;
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-center">{getQuestionText(currentQuestion, gender)}</h3>
            <RadioGroup
              key={currentQuestionIndex}
              value={selectedAnswer?.toString() || ""}
              onValueChange={(value) => setSelectedAnswer(parseInt(value, 10))}
              className="space-y-3"
            >
              {shuffledAnswers.map((answer, index) => (
                <Label 
                  key={index} 
                  dir="rtl"
                  className="flex items-center justify-between gap-x-3 p-4 border rounded-lg cursor-pointer hover:bg-muted/50 has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary"
                >
                  <span className="flex-1 text-right">{getAnswerText(answer, gender)}</span>
                  <RadioGroupItem 
                    value={String(answer.points)} 
                    id={`q${currentQuestionIndex}-a${index}`} 
                  />
                </Label>
              ))}
            </RadioGroup>
            <Button onClick={handleNextQuestion} disabled={selectedAnswer === null} className="w-full">
              {currentQuestionIndex === questions.length - 1 ? 'عرض النتيجة' : 'التالي'}
            </Button>
          </div>
        );
      case 'result':
        return (
          <div className="text-center space-y-4">
            <h3 className="text-2xl font-bold text-primary">{result.level}</h3>
            <p className="text-muted-foreground leading-relaxed">{result.description}</p>
            <p className="text-xs text-muted-foreground pt-4 border-t">إخلاء مسؤولية: هذا الاختبار هو لأغراض الترفيه والتأمل الذاتي، ولا يمثل تشخيصًا نفسيًا.</p>
            <Button onClick={handleRestart} variant="outline" className="w-full">
                <Repeat className="ml-2 h-4 w-4" />
                أعد الاختبار
            </Button>
          </div>
        );
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
            <Droplets className="h-6 w-6 text-primary" />
            اختبار الحساسية
        </CardTitle>
        <CardDescription>
            أجب عن الأسئلة التالية بصدق لاكتشاف مدى حساسيتك وتأثرك بالبيئة والمشاعر من حولك.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {stage !== 'gender' && (
             <div className="mb-6">
                <Progress value={progress} />
                <p className="text-sm text-muted-foreground mt-2 text-center">
                    {isFinished ? 'اكتمل الاختبار!' : `السؤال ${currentQuestionIndex + 1} من ${shuffledQuestions.length}`}
                </p>
            </div>
        )}
       
        {renderContent()}

      </CardContent>
    </Card>
  );
}
