
'use client';

import { useState, useEffect, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Repeat, CheckCircle, XCircle, BrainCircuit, Play, Trophy, ChevronLeft } from 'lucide-react';
import { cn } from '@/lib/utils';
import { kidsQuizQuestions, Question } from '@/lib/kids-quiz-questions';

type TestStage = 'start' | 'playing' | 'result';

const QUESTIONS_PER_SESSION = 15;

function shuffleArray<T>(array: T[]): T[] {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}

export function KidsQuizTool() {
  const [stage, setStage] = useState<TestStage>('start');
  const [sessionQuestions, setSessionQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [feedback, setFeedback] = useState<{ message: string; isCorrect: boolean } | null>(null);
  const [score, setScore] = useState({ correct: 0, incorrect: 0 });

  const startQuiz = () => {
    setScore({ correct: 0, incorrect: 0 });
    setFeedback(null);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setSessionQuestions(shuffleArray(kidsQuizQuestions).slice(0, QUESTIONS_PER_SESSION));
    setStage('playing');
  };
  
  const goToNextQuestion = () => {
    setFeedback(null);
    setSelectedAnswer(null);
    if (currentQuestionIndex < sessionQuestions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      setStage('result');
    }
  };

  const handleAnswerSubmit = () => {
    if (selectedAnswer === null) return;

    const currentQuestion = sessionQuestions[currentQuestionIndex];
    const answerObject = currentQuestion.answers.find(a => a.text === selectedAnswer);
    const correctAnswerObject = currentQuestion.answers.find(a => a.isCorrect);
    
    if (answerObject) {
      if (answerObject.isCorrect) {
        setScore(prev => ({ ...prev, correct: prev.correct + 1 }));
        setFeedback({ message: 'إجابة صحيحة! أحسنت!', isCorrect: true });
      } else {
        setScore(prev => ({ ...prev, incorrect: prev.incorrect + 1 }));
        setFeedback({ message: `للأسف! الإجابة الصحيحة هي: ${correctAnswerObject?.text}`, isCorrect: false });
      }
      setTimeout(() => {
        goToNextQuestion();
      }, 2500);
    }
  };
  
  const resetQuiz = () => {
    setStage('start');
  };

  const currentQuestion = sessionQuestions[currentQuestionIndex];

  const shuffledAnswers = useMemo(() => {
    if (!currentQuestion) return [];
    return shuffleArray(currentQuestion.answers);
  }, [currentQuestion]);

  const progress = (currentQuestionIndex / sessionQuestions.length) * 100;
  const isAnswered = feedback !== null;

  const renderContent = () => {
    switch(stage) {
      case 'start':
        return (
          <div className="text-center space-y-6 flex flex-col items-center">
            <BrainCircuit className="h-16 w-16 text-primary" />
            <h3 className="text-xl font-semibold">لعبة الأسئلة والأجوبة للأطفال</h3>
            <p className="text-muted-foreground">هل أنت مستعد لاختبار معلوماتك؟ هيا نبدأ مغامرة ممتعة من {QUESTIONS_PER_SESSION} سؤالاً!</p>
            <Button size="lg" onClick={startQuiz}>
              <Play className="ml-2 h-5 w-5" />
              ابدأ اللعبة
            </Button>
          </div>
        );

      case 'playing':
        if (!currentQuestion) return null;
        return (
          <div className="space-y-6">
            <div className="mb-4">
              <Progress value={progress} />
              <div className="flex justify-between items-center mt-2 text-sm text-muted-foreground">
                <p>السؤال {currentQuestionIndex + 1} من {sessionQuestions.length}</p>
                <div className="flex items-center gap-4">
                    <span className="flex items-center gap-1 text-green-600 font-bold"><CheckCircle size={16}/> {score.correct}</span>
                    <span className="flex items-center gap-1 text-red-600 font-bold"><XCircle size={16}/> {score.incorrect}</span>
                </div>
              </div>
            </div>
            <h3 className="text-lg font-semibold text-center leading-relaxed">{currentQuestion.text}</h3>
            <RadioGroup key={currentQuestionIndex} value={selectedAnswer || ""} onValueChange={setSelectedAnswer} className="space-y-3" disabled={isAnswered}>
              {shuffledAnswers.map((answer, index) => (
                <Label key={index} dir="rtl" className={cn("flex items-center gap-x-3 p-4 border rounded-lg transition-colors cursor-pointer hover:bg-muted/50 text-base", !isAnswered && "has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary", isAnswered && answer.isCorrect && "bg-green-100 border-green-400 text-green-800", isAnswered && !answer.isCorrect && selectedAnswer === answer.text && "bg-red-100 border-red-400 text-red-800", isAnswered && "cursor-not-allowed opacity-80")}>
                  <RadioGroupItem value={answer.text} id={`q${currentQuestionIndex}-a${index}`} className="border-primary h-5 w-5" />
                  <span className="flex-1">{answer.text}</span>
                  {isAnswered && answer.isCorrect && <CheckCircle className="h-5 w-5 text-green-600" />}
                  {isAnswered && !answer.isCorrect && selectedAnswer === answer.text && <XCircle className="h-5 w-5 text-red-600" />}
                </Label>
              ))}
            </RadioGroup>

            {!isAnswered ? (
              <Button onClick={handleAnswerSubmit} disabled={selectedAnswer === null} className="w-full h-12 text-lg">تأكيد الإجابة</Button>
            ) : (
                <div className={cn(
                    "flex items-center justify-center gap-2 p-3 rounded-md min-h-[52px]",
                    feedback.isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                )}>
                    {feedback.isCorrect ? <CheckCircle className="h-5 w-5" /> : <XCircle className="h-5 w-5" />}
                    <span className="font-medium">{feedback.message}</span>
                </div>
            )}
          </div>
        );
        
      case 'result':
        const percentage = Math.round((score.correct / sessionQuestions.length) * 100);
        let message;
        if (percentage >= 80) message = "رائع! أنت عبقري ولديك معلومات كثيرة.";
        else if (percentage >= 50) message = "أداء جيد جدًا! لقد تعلمت الكثير.";
        else message = "محاولة جيدة! استمر في التعلم والمحاولة مرة أخرى.";

        return (
          <div className="text-center space-y-4 flex flex-col items-center">
            <Trophy className="h-16 w-16 text-yellow-500" />
            <h3 className="text-2xl font-bold">انتهت اللعبة!</h3>
            <p className="text-lg">نتيجتك النهائية هي:</p>
            <p className="text-5xl font-bold font-mono text-primary">{score.correct} / {sessionQuestions.length}</p>
            <p className="text-muted-foreground font-semibold">{message}</p>
            <Button onClick={resetQuiz} variant="outline" className="w-full max-w-xs mt-4 h-12 text-lg">
              <Repeat className="ml-2 h-4 w-4" />
              العب مرة أخرى
            </Button>
          </div>
        );
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-center gap-2">
            <BrainCircuit className="h-6 w-6 text-primary" />
            لعبة أسئلة الأطفال
        </CardTitle>
        <CardDescription className="text-center">اختبر معلوماتك العامة في العلوم والجغرافيا والحيوانات!</CardDescription>
      </CardHeader>
      <CardContent>{renderContent()}</CardContent>
    </Card>
  );
}
