
'use client';

import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Repeat, Cat } from 'lucide-react';

type Answer = { text: string; points: number };
type Question = { text: string; answers: Answer[] };

const questions: Question[] = [
  {
    text: "عندما تذهبين للتسوق، من الذي يحمل الأكياس عادة؟",
    answers: [
      { text: "أنا أحمل أكياسي بنفسي، هذا طبيعي.", points: 1 },
      { text: "نتقاسم حملها أنا ومن معي.", points: 2 },
      { text: "أفضل أن يحملها شخص آخر عني.", points: 3 },
      { text: "لا أحمل أي شيء على الإطلاق، هناك من يقوم بذلك دائمًا.", points: 4 },
    ],
  },
  {
    text: "إذا كنتِ ترغبين في شيء ما، كيف تطلبينه؟",
    answers: [
      { text: "أشتريه بنفسي أو أعمل من أجله.", points: 1 },
      { text: "أطلبه مباشرة وبوضوح.", points: 2 },
      { text: "ألمح إليه وأتوقع من الآخرين أن يفهموا.", points: 3 },
      { text: "أشعر بالانزعاج إذا لم يحصل لي ما أريد دون أن أطلب.", points: 4 },
    ],
  },
  {
    text: "ما هو رد فعلك إذا لم يعجبكِ مطعم اختاره أصدقاؤك؟",
    answers: [
      { text: "أجرب شيئًا جديدًا وأستمتع بصحبتهم.", points: 1 },
      { text: "قد أكون صامتة قليلاً ولكني لن أفسد الأمسية.", points: 2 },
      { text: "أعبر عن عدم رضاي بشكل واضح وأقترح مكانًا آخر.", points: 3 },
      { text: "أرفض الذهاب أو أظل متجهمة طوال الوقت.", points: 4 },
    ],
  },
  {
    text: "كيف تتعاملين مع الأعمال المنزلية؟",
    answers: [
      { text: "أقوم بمسؤولياتي كاملة، فالمنزل مسؤولية مشتركة.", points: 1 },
      { text: "أقوم ببعض المهام، وأترك الباقي للآخرين.", points: 2 },
      { text: "أفضل أن يقوم شخص آخر بمعظم الأعمال المنزلية.", points: 3 },
      { text: "نادرًا ما أقوم بأي عمل منزلي.", points: 4 },
    ],
  },
  {
    text: "عندما تكونين مريضة، كيف تتصرفين؟",
    answers: [
      { text: "أعتني بنفسي وأحاول ألا أكون عبئًا على أحد.", points: 1 },
      { text: "أطلب بعض المساعدة الأساسية وأقدرها.", points: 2 },
      { text: "أتوقع اهتمامًا ورعاية كاملة من الجميع.", points: 3 },
      { text: "أشعر أن العالم يجب أن يتوقف ليعتني بي.", points: 4 },
    ],
  },
  {
    text: "ما هو تعريفك للهدية المثالية؟",
    answers: [
      { text: "أي شيء يأتي من القلب، حتى لو كان بسيطًا.", points: 1 },
      { text: "شيء فكر فيه الشخص جيدًا ويناسبني.", points: 2 },
      { text: "شيء باهظ الثمن أو من ماركة مشهورة.", points: 3 },
      { text: "أتوقع دائمًا هدايا فاخرة، وأصاب بخيبة أمل إذا لم تكن كذلك.", points: 4 },
    ],
  },
  {
    text: "إذا ارتكبتِ خطأ، كيف تتعاملين مع الموقف؟",
    answers: [
      { text: "أعترف بخطئي وأعتذر على الفور.", points: 1 },
      { text: "أشعر بالسوء وأحاول إصلاح الأمر.", points: 2 },
      { text: "أجد صعوبة في الاعتراف بالخطأ وأبرر تصرفي.", points: 3 },
      { text: "ألقي باللوم على شخص آخر أو على الظروف.", points: 4 },
    ],
  },
  {
    text: "كلمة 'لا' بالنسبة لكِ هي:",
    answers: [
      { text: "كلمة عادية، أتقبلها وأحترمها.", points: 1 },
      { text: "محبطة، لكن يمكنني التعامل معها.", points: 2 },
      { text: "بداية لمفاوضات جديدة للحصول على ما أريد.", points: 3 },
      { text: "غير مقبولة على الإطلاق وتثير غضبي.", points: 4 },
    ],
  },
  {
    text: "عندما تطلبين خدمة من شخص ما (مثل توصيلة)، كيف يكون شعورك؟",
    answers: [
      { text: "أشعر بالامتنان الشديد وأشكره بحرارة.", points: 1 },
      { text: "أعتبرها مساعدة لطيفة وأشكر الشخص.", points: 2 },
      { text: "أشعر أنه أمر طبيعي ومتوقع.", points: 3 },
      { text: "أشعر أنه من واجبه مساعدتي.", points: 4 },
    ],
  },
  {
    text: "كيف تصفين علاقتك بالمال؟",
    answers: [
      { text: "أنا مستقلة ماديًا وأدير أموالي بنفسي.", points: 1 },
      { text: "أدير أموالي، لكن لا أمانع تلقي المساعدة المالية.", points: 2 },
      { text: "أعتمد بشكل كبير على الآخرين لتغطية نفقاتي.", points: 3 },
      { text: "أتوقع أن يتم تلبية جميع احتياجاتي المالية من قبل الآخرين.", points: 4 },
    ],
  },
];

const getResult = (score: number) => {
    const results = {
      level1: {
        title: "شخصية مستقلة وعملية",
        text: "أنتِ شخصية تعتمد على نفسها، وتتحملين المسؤولية بشكل كامل. لا تنتظرين من الآخرين أن يقوموا بأشياء لكِ، وتقدرين قيمة الجهد والعمل. قوتك تكمن في استقلاليتك."
      },
      level2: {
        title: "تحبين الدلال ولكنكِ متوازنة",
        text: "أنتِ تعرفين كيف توازنين بين الاعتماد على النفس والاستمتاع بالاهتمام. تقدرين اللفتات اللطيفة وتستمتعين بالدلال، لكنكِ قادرة على تحمل المسؤولية والقيام بمهامك عند الحاجة. هذا توازن صحي وجميل."
      },
      level3: {
        title: "شخصية تميل إلى الدلع",
        text: "أنتِ تستمتعين حقًا بالاهتمام والرعاية، وتفضلين أن يقوم الآخرون بتدليلك. قد تجدين صعوبة في سماع كلمة 'لا' وتميلين إلى توقع الكثير من المحيطين بك. القليل من الاستقلالية قد يضيف بعدًا جديدًا لشخصيتك."
      },
      level4: {
        title: "شخصية مدللة بامتياز",
        text: "أنتِ معتادة على أن تكوني مركز الاهتمام وأن تُلبى جميع رغباتك. قد تشعرين بالاستياء الشديد إذا لم تسر الأمور كما تريدين. تذكري أن الاعتماد على النفس وتقدير جهود الآخرين هما أيضًا من صفات الجاذبية."
      }
    };
  
    if (score <= 15) {
      return { level: results.level1.title, description: results.level1.text };
    }
    if (score <= 25) {
      return { level: results.level2.title, description: results.level2.text };
    }
    if (score <= 35) {
      return { level: results.level3.title, description: results.level3.text };
    }
    return { level: results.level4.title, description: results.level4.text };
};

type TestStage = 'questions' | 'result';

// Helper to shuffle an array
function shuffleArray<T>(array: T[]): T[] {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

export function SpoiledTestTool() {
  const [stage, setStage] = useState<TestStage>('questions');
  const [shuffledQuestions, setShuffledQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  
  useEffect(() => {
    setShuffledQuestions(shuffleArray(questions));
  }, []);

  const isFinished = stage === 'result';
  const totalScore = answers.reduce((sum, score) => sum + score, 0);
  const result = isFinished ? getResult(totalScore) : { level: '', description: '' };

  const startTest = () => {
    setAnswers([]);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setShuffledQuestions(shuffleArray(questions));
    setStage('questions');
  };

  const handleNextQuestion = () => {
    if (selectedAnswer === null) return;
    
    const newAnswers = [...answers, selectedAnswer];
    setAnswers(newAnswers);
    setSelectedAnswer(null);

    if (currentQuestionIndex < shuffledQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      setStage('result');
    }
  };
  
  const progress = isFinished ? 100 : ((currentQuestionIndex) / shuffledQuestions.length) * 100;
  
  const currentQuestion = shuffledQuestions[currentQuestionIndex];
  
  const shuffledAnswers = useMemo(() => {
    if (!currentQuestion) return [];
    return shuffleArray(currentQuestion.answers);
  }, [currentQuestion]);


  const renderContent = () => {
    switch(stage) {
      case 'questions':
        if (!currentQuestion) return null;
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-center">{currentQuestion.text}</h3>
            <RadioGroup
              key={currentQuestionIndex}
              value={selectedAnswer?.toString() || ""}
              onValueChange={(value) => setSelectedAnswer(parseInt(value, 10))}
              className="space-y-3"
            >
              {shuffledAnswers.map((answer, index) => (
                <Label 
                  key={index} 
                  dir="rtl"
                  className="flex items-center gap-x-3 p-4 border rounded-lg cursor-pointer hover:bg-muted/50 has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary"
                >
                  <RadioGroupItem 
                    value={String(answer.points)} 
                    id={`q${currentQuestionIndex}-a${index}`} 
                  />
                  <span className="flex-1">{answer.text}</span>
                </Label>
              ))}
            </RadioGroup>
            <Button onClick={handleNextQuestion} disabled={selectedAnswer === null} className="w-full">
              {currentQuestionIndex === questions.length - 1 ? 'عرض النتيجة' : 'التالي'}
            </Button>
          </div>
        );
      case 'result':
        return (
          <div className="text-center space-y-4">
            <h3 className="text-2xl font-bold text-primary">{result.level}</h3>
            <p className="text-muted-foreground leading-relaxed">{result.description}</p>
            <p className="text-xs text-muted-foreground pt-4 border-t">إخلاء مسؤولية: هذا الاختبار هو لأغراض الترفيه فقط. "الدلع" مفهوم نسبي، والهدف هو الاستمتاع والمرح.</p>
            <Button onClick={startTest} variant="outline" className="w-full">
                <Repeat className="ml-2 h-4 w-4" />
                أعد الاختبار
            </Button>
          </div>
        );
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
            <Cat className="h-6 w-6 text-primary" />
            اختبار الدلع
        </CardTitle>
        <CardDescription>
            أجيبي عن الأسئلة التالية بصدق لاكتشاف درجة الدلع في شخصيتك.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6">
            <Progress value={progress} />
            <p className="text-sm text-muted-foreground mt-2 text-center">
                {isFinished ? 'اكتمل الاختبار!' : `السؤال ${currentQuestionIndex + 1} من ${shuffledQuestions.length}`}
            </p>
        </div>
       
        {renderContent()}

      </CardContent>
    </Card>
  );
}
