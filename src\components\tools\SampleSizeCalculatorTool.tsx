
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  confidenceLevel: z.enum(['90', '95', '99']).default('95'),
  marginOfError: requiredNumber().positive("هامش الخطأ يجب أن يكون موجبًا.").min(0.1).max(20).default(5),
  populationProportion: requiredNumber().positive("نسبة السكان يجب أن تكون موجبة.").default(50),
});

const zScores: { [key: string]: number } = {
  '90': 1.645,
  '95': 1.96,
  '99': 2.576,
};

export function SampleSizeCalculatorTool() {
  const [sampleSize, setSampleSize] = useState<number | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      confidenceLevel: '95',
      marginOfError: 5,
      populationProportion: 50,
    }
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const zScore = zScores[data.confidenceLevel];
    const p = data.populationProportion / 100;
    const e = data.marginOfError / 100;

    const size = (Math.pow(zScore, 2) * p * (1 - p)) / Math.pow(e, 2);
    setSampleSize(Math.ceil(size));
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>تحديد حجم العينة</CardTitle>
        <CardDescription>حدد حجم العينة المطلوب لدراستك أو بحثك بناءً على مستوى الثقة وهامش الخطأ.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField control={form.control} name="confidenceLevel" render={({ field }) => (
                  <FormItem>
                    <FormLabel>مستوى الثقة</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl><SelectTrigger><SelectValue /></SelectTrigger></FormControl>
                      <SelectContent>
                        <SelectItem value="90">90%</SelectItem>
                        <SelectItem value="95">95%</SelectItem>
                        <SelectItem value="99">99%</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
              )}/>
              <FormField control={form.control} name="marginOfError" render={({ field }) => (
                  <FormItem><FormLabel>هامش الخطأ (%)</FormLabel><FormControl>
                    <Input 
                      type="number" 
                      placeholder="مثال: 50"
                      min="1"
                      max="99"
                      step="1"
                      {...field} 
                    />
                  </FormControl><FormMessage /></FormItem>
              )}/>
            </div>
            <FormField control={form.control} name="populationProportion" render={({ field }) => (
                <FormItem>
                  <FormLabel>نسبة السكان المتوقعة (%)</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      placeholder="مثال: 50"
                      min="1"
                      max="99"
                      step="1"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
            )}/>
            <Button type="submit" className="w-full">احسب حجم العينة</Button>
          </form>
        </Form>
        {sampleSize !== null && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-2">حجم العينة المطلوب</h3>
            <div className="p-6 bg-primary/10 rounded-lg">
                <p className="text-5xl font-bold font-mono text-primary">{sampleSize.toLocaleString()}</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
