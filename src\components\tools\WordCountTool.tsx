'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { FileText, Type, Pilcrow, MessageSquare, Timer, Keyboard } from 'lucide-react';

const FormSchema = z.object({
  text: z.string(),
});

interface TextStats {
  words: number;
  characters: number;
  sentences: number;
  paragraphs: number;
  spaces: number;
  readingTime: string; // in seconds
}

export function WordCountTool() {
  const [stats, setStats] = useState<TextStats>({ words: 0, characters: 0, sentences: 0, paragraphs: 0, spaces: 0, readingTime: '0 s' });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      text: '',
    },
  });

  const textValue = form.watch('text');

  useEffect(() => {
    if (!textValue) {
        setStats({ words: 0, characters: 0, sentences: 0, paragraphs: 0, spaces: 0, readingTime: '0 s' });
        return;
    }

    // 1. حساب عدد الكلمات
    const words = textValue.match(/\b(\p{L}|\p{N})+\b/gu)?.length || 0;
    
    // 2. حساب عدد الأحرف
    const characters = textValue.length;

    // 3. حساب عدد الجمل
    const sentences = textValue.match(/[^.!?؟]+[.!?؟]+/g)?.length || (textValue.trim() ? 1 : 0);
    
    // 4. حساب عدد الفقرات
    const paragraphs = textValue.split(/\n+/).filter(p => p.trim() !== '').length || (textValue.trim() ? 1 : 0);
    
    // 5. حساب عدد المسافات
    const spaces = (textValue.match(/ /g) || []).length;
    
    // 6. حساب وقت القراءة (متوسط 225 كلمة في الدقيقة)
    const wordsPerMinute = 225;
    const minutes = words / wordsPerMinute;
    const readingTimeSeconds = Math.ceil(minutes * 60);
    
    let readingTimeFormatted: string;
    if (readingTimeSeconds < 60) {
      readingTimeFormatted = `${readingTimeSeconds} s`;
    } else {
      const formattedMinutes = Math.floor(readingTimeSeconds / 60);
      const formattedSeconds = readingTimeSeconds % 60;
      readingTimeFormatted = `${formattedMinutes} m ${formattedSeconds} s`;
    }

    setStats({ words, characters, sentences, paragraphs, spaces, readingTime: readingTimeFormatted });
  }, [textValue]);

  const StatCard = ({ icon: Icon, label, value }: { icon: LucideIcon, label: string, value: string | number }) => (
    <div className="flex items-center gap-3 p-3 bg-secondary rounded-lg">
        <Icon className="h-6 w-6 text-primary" />
        <div>
            <p className="text-sm text-muted-foreground">{label}</p>
            <p className="text-xl font-bold font-mono">{value.toLocaleString()}</p>
        </div>
    </div>
  );

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>عداد الكلمات</CardTitle>
        <CardDescription>أدخل النص في المربع أدناه لمعرفة عدد الكلمات والأحرف وغيرها من الإحصائيات.</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
          <StatCard icon={FileText} label="الحروف" value={stats.characters} />
          <StatCard icon={Type} label="الكلمات" value={stats.words} />
          <StatCard icon={MessageSquare} label="الجمل" value={stats.sentences} />
          <StatCard icon={Keyboard} label="المسافات" value={stats.spaces} />
          <StatCard icon={Pilcrow} label="الفقرات" value={stats.paragraphs} />
          <StatCard icon={Timer} label="وقت القراءة" value={stats.readingTime} />
        </div>
        <Form {...form}>
          <form>
            <FormField
              control={form.control}
              name="text"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="sr-only">النص</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="ابدأ بالكتابة أو الصق النص هنا..."
                      className="min-h-[250px] text-base"
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
