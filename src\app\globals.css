@tailwind base;
@tailwind components;
@tailwind utilities;



@layer base {
  :root {
    --background: 218 93% 95%;
    --foreground: 221 25% 25%;
    --card: 0 0% 100%;
    --card-foreground: 221 25% 25%;
    --popover: 0 0% 100%;
    --popover-foreground: 221 25% 25%;
    --primary: 221 44% 41%;
    --primary-foreground: 0 0% 100%;
    --secondary: 221 44% 90%;
    --secondary-foreground: 221 44% 31%;
    --muted: 221 30% 92%;
    --muted-foreground: 221 25% 55%;
    --accent: 120 60% 67%;
    --accent-foreground: 120 100% 15%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 221 20% 88%;
    --input: 221 30% 95%;
    --ring: 221 44% 41%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 221 44% 20%;
    --sidebar-foreground: 220 30% 95%;
    --sidebar-primary: 218 93% 95%;
    --sidebar-primary-foreground: 221 44% 20%;
    --sidebar-accent: 221 44% 30%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 221 44% 25%;
    --sidebar-ring: 218 93% 85%;
  }
  .dark {
    --background: 221 44% 10%;
    --foreground: 220 30% 95%;
    --card: 221 44% 15%;
    --card-foreground: 220 30% 95%;
    --popover: 221 44% 10%;
    --popover-foreground: 220 30% 95%;
    --primary: 218 93% 85%;
    --primary-foreground: 221 44% 15%;
    --secondary: 221 44% 25%;
    --secondary-foreground: 220 30% 95%;
    --muted: 221 44% 20%;
    --muted-foreground: 220 30% 75%;
    --accent: 120 60% 67%;
    --accent-foreground: 120 100% 15%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 221 44% 25%;
    --input: 221 44% 20%;
    --ring: 218 93% 85%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 221 44% 10%;
    --sidebar-foreground: 220 30% 95%;
    --sidebar-primary: 218 93% 85%;
    --sidebar-primary-foreground: 221 44% 15%;
    --sidebar-accent: 221 44% 20%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 221 44% 25%;
    --sidebar-ring: 218 93% 85%;
  }
}

@layer base {
  * {
    @apply border-border;
    box-sizing: border-box;
  }
  
  html {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Tajawal', sans-serif;
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
  }
  
  #__next {
    height: 100%;
    width: 100%;
  }
}

@layer utilities {
  /* إضافة فئات مخصصة للعرض الكامل */
  .w-screen-safe {
    width: 100vw;
    max-width: 100vw;
  }
  
  .container-full {
    width: 100% ;
    max-width: none ;
    padding-left: 1rem ;
    padding-right: 1rem ;
    margin-left: 0 ;
    margin-right: 0 ;
  }
  
  @media (min-width: 640px) {
    .container-full {
      padding-left: 2rem ;
      padding-right: 2rem ;
    }
  }
  
  @media (min-width: 1024px) {
    .container-full {
      padding-left: 3rem ;
      padding-right: 3rem ;
    }
  }
  
  @media (min-width: 1280px) {
    .container-full {
      padding-left: 4rem ;
      padding-right: 4rem ;
    }
  }
}
