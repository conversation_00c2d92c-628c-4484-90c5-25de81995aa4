
'use client';

import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Repeat, Shield, Swords } from 'lucide-react';

type Answer = { text: string; points: number };
type Question = { text: string; answers: Answer[] };

const questions: Question[] = [
  // Responsibility
  {
    text: "عندما ترتكب خطأً في العمل يؤثر على الفريق، ماذا تفعل؟",
    answers: [
      { text: "أعترف بالخطأ فورًا، وأتحمل المسؤولية كاملة، وأقترح حلاً.", points: 4 },
      { text: "أحاول إصلاح المشكلة بهدوء قبل أن يلاحظ أحد.", points: 3 },
      { text: "أشعر بالارتباك وأتمنى ألا يتم اكتشاف أنني السبب.", points: 2 },
      { text: "ألقي باللوم على الظروف أو على شخص آخر بشكل غير مباشر.", points: 1 },
    ],
  },
  {
    text: "وعدت صديقًا بمساعدته في أمر ما، ولكن ظهر لك أمر أكثر متعة في نفس الوقت. كيف تتصرف؟",
    answers: [
      { text: "ألتزم بوعدي لصديقي، فالكلمة عهد.", points: 4 },
      { text: "أحاول التنسيق بين الأمرين، حتى لو كان ذلك مرهقًا.", points: 3 },
      { text: "أعتذر لصديقي وأختار الأمر الأكثر متعة.", points: 2 },
      { text: "أتجاهل صديقي وأذهب للموعد الآخر دون إخباره.", points: 1 },
    ],
  },
  // Emotional Control
  {
    text: "شخص ما استفزك بشكل متعمد في مكان عام. ما هو رد فعلك؟",
    answers: [
      { text: "أتجاهله تمامًا أو أرد بهدوء وحزم، رافضًا الانجرار إلى مستواه.", points: 4 },
      { text: "أشعر بالغضب ولكن أكتمه وأغادر المكان.", points: 3 },
      { text: "أرد عليه بنفس الطريقة وأدخل معه في مشادة كلامية.", points: 2 },
      { text: "أفقد أعصابي وقد أرفع صوتي أو أستخدم كلمات حادة.", points: 1 },
    ],
  },
  {
    text: "تواجه ضغوطًا مالية كبيرة. كيف تتعامل مع الموقف؟",
    answers: [
      { text: "أقوم بتحليل الوضع بهدوء، وأضع خطة عمل لترتيب أولوياتي وإيجاد حلول.", points: 4 },
      { text: "أشعر بالقلق الشديد، لكنني أبحث عن حلول وأطلب المشورة إذا لزم الأمر.", points: 3 },
      { text: "أتجاهل المشكلة وأتصرف كأن كل شيء على ما يرام، على أمل أن تُحل من تلقاء نفسها.", points: 2 },
      { text: "أشعر باليأس وألوم الظروف أو الآخرين على وضعي.", points: 1 },
    ],
  },
  // Courage & Principles
  {
    text: "رأيت شخصًا يتعرض للظلم أو التنمر أمامك. ماذا تفعل؟",
    answers: [
      { text: "أتدخل للدفاع عن الشخص المظلوم، حتى لو كان ذلك يعرضني للخطر.", points: 4 },
      { text: "أبحث عن طريقة لمساعدته بشكل غير مباشر، مثل طلب المساعدة من شخص مسؤول.", points: 3 },
      { text: "أشعر بالسوء ولكن أفضل عدم التدخل لتجنب المشاكل.", points: 2 },
      { text: "أتظاهر بأنني لم أر شيئًا وأكمل طريقي.", points: 1 },
    ],
  },
  {
    text: "طلب منك مديرك القيام بأمر يتعارض مع مبادئك الأخلاقية. كيف ترد؟",
    answers: [
      { text: "أرفض بهدوء وحزم، مع شرح أن هذا الأمر يتعارض مع مبادئي.", points: 4 },
      { text: "أتردد وأحاول إيجاد طريقة للتهرب من تنفيذ الطلب دون مواجهة مباشرة.", points: 3 },
      { text: "أشكو لزملائي ولكن أنفذ الطلب في النهاية خوفًا من العواقب.", points: 2 },
      { text: "أنفذ الطلب دون تردد، فالوظيفة أهم.", points: 1 },
    ],
  },
  // Respect & Empathy
  {
    text: "تتحدث مع شخص لديه رأي مختلف تمامًا عن رأيك. كيف يكون الحوار؟",
    answers: [
      { text: "أستمع لوجهة نظره باحترام وأحاول فهمها، حتى لو لم أوافق عليها.", points: 4 },
      { text: "أناقشه وأحاول إقناعه بوجهة نظري، مع الحفاظ على الاحترام.", points: 3 },
      { text: "أقاطعه باستمرار وأسخر من رأيه.", points: 2 },
      { text: "أرفض الاستماع وأتهمه بالجهل أو الخطأ.", points: 1 },
    ],
  },
  {
    text: "كيف تتعامل مع كبار السن والأطفال؟",
    answers: [
      { text: "أتعامل معهم بصبر واحترام وتواضع، وأقدم المساعدة دائمًا.", points: 4 },
      { text: "أتعامل معهم بلطف بشكل عام.", points: 3 },
      { text: "أجد صعوبة في التواصل معهم وأفضل تجنب ذلك.", points: 2 },
      { text: "ليس لدي صبر للتعامل معهم.", points: 1 },
    ],
  },
  // Ambition & Self-Improvement
  {
    text: "كيف تنظر إلى الفشل؟",
    answers: [
      { text: "فرصة للتعلم والنمو، وخطوة ضرورية على طريق النجاح.", points: 4 },
      { text: "أمر محبط، لكنني أحاول النهوض والمحاولة مرة أخرى.", points: 3 },
      { text: "دليل على أنني لست جيدًا بما فيه الكفاية.", points: 2 },
      { text: "نهاية الطريق، وأستسلم بسهولة.", points: 1 },
    ],
  },
  {
    text: "هل تسعى لتطوير نفسك وتعلم مهارات جديدة باستمرار؟",
    answers: [
      { text: "نعم، أخصص وقتًا منتظمًا للقراءة وتعلم كل ما هو جديد ومفيد.", points: 4 },
      { text: "أحيانًا، إذا كان هناك شيء يثير اهتمامي بشدة.", points: 3 },
      { text: "نادرًا، أفضل البقاء في منطقة الراحة الخاصة بي.", points: 2 },
      { text: "لا، أعتقد أن ما أعرفه كافٍ.", points: 1 },
    ],
  },
];

const getResult = (score: number) => {
    const results = {
      level1: {
        title: "شخصية ذات نضج عالٍ ورجولة متزنة",
        text: "أنت تجسد المعنى الحقيقي للرجولة الإيجابية. تتمتع بالمسؤولية، والتحكم في النفس، وشجاعة المبدأ. أنت قائد بالفطرة ومصدر أمان وثقة لمن حولك. تحترم الآخرين، وتلتزم بكلمتك، وتنظر إلى التحديات كفرص للنمو. استمر في كونك هذا الشخص الملهم."
      },
      level2: {
        title: "شخصية قوية ذات مبادئ",
        text: "لديك أساس قوي من الصفات الرجولية. أنت مسؤول بشكل عام، وتحاول فعل الصواب، ولكن قد تتردد أحيانًا في المواقف الصعبة أو تفقد هدوءك تحت الضغط. لديك إمكانات كبيرة لتكون قائدًا، والتركيز على زيادة الثبات عند الشدائد سيجعلك أقوى."
      },
      level3: {
        title: "شخصية في مرحلة التطور",
        text: "أنت تظهر لمحات من الرجولة والمسؤولية، لكنك قد تتأثر بسهولة بالظروف الخارجية أو آراء الآخرين. قد تجد صعوبة في التحكم في الانفعالات أو الالتزام بالوعود عندما تتعارض مع رغباتك. هذه مرحلة طبيعية من النمو، والتركيز على تحمل المزيد من المسؤولية سيعزز شخصيتك بشكل كبير."
      },
      level4: {
        title: "تحتاج إلى صقل الشخصية",
        text: "قد تواجه تحديات في جوانب مهمة مثل تحمل المسؤولية، والتحكم في الغضب، والالتزام بالمبادئ. من المهم أن تدرك أن الرجولة الحقيقية ليست بالقوة الجسدية أو الصوت العالي، بل بالثبات، والحكمة، والقدرة على حماية ورعاية من حولك. ابدأ بخطوات صغيرة للعمل على هذه الجوانب."
      }
    };
  
    if (score >= 32) {
      return { level: results.level1.title, description: results.level1.text };
    }
    if (score >= 24) {
      return { level: results.level2.title, description: results.level2.text };
    }
    if (score >= 16) {
      return { level: results.level3.title, description: results.level3.text };
    }
    return { level: results.level4.title, description: results.level4.text };
};

type TestStage = 'questions' | 'result';

// Helper to shuffle an array
function shuffleArray<T>(array: T[]): T[] {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

export function MasculinityTestTool() {
  const [stage, setStage] = useState<TestStage>('questions');
  const [shuffledQuestions, setShuffledQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  
  useEffect(() => {
    setShuffledQuestions(shuffleArray(questions));
  }, []);

  const isFinished = stage === 'result';
  const totalScore = answers.reduce((sum, score) => sum + score, 0);
  const result = isFinished ? getResult(totalScore) : { level: '', description: '' };

  const startTest = () => {
    setAnswers([]);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setShuffledQuestions(shuffleArray(questions));
    setStage('questions');
  };

  const handleNextQuestion = () => {
    if (selectedAnswer === null) return;
    
    const newAnswers = [...answers, selectedAnswer];
    setAnswers(newAnswers);
    setSelectedAnswer(null);

    if (currentQuestionIndex < shuffledQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      setStage('result');
    }
  };
  
  const progress = isFinished ? 100 : ((currentQuestionIndex) / shuffledQuestions.length) * 100;
  
  const currentQuestion = shuffledQuestions[currentQuestionIndex];
  
  const shuffledAnswers = useMemo(() => {
    if (!currentQuestion) return [];
    return shuffleArray(currentQuestion.answers);
  }, [currentQuestion]);


  const renderContent = () => {
    switch(stage) {
      case 'questions':
        if (!currentQuestion) return null;
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-center">{currentQuestion.text}</h3>
            <RadioGroup
              key={currentQuestionIndex}
              value={selectedAnswer?.toString() || ""}
              onValueChange={(value) => setSelectedAnswer(parseInt(value, 10))}
              className="space-y-3"
            >
              {shuffledAnswers.map((answer, index) => (
                <Label 
                  key={index} 
                  dir="rtl"
                  className="flex items-center gap-x-3 p-4 border rounded-lg cursor-pointer hover:bg-muted/50 has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary"
                >
                  <RadioGroupItem 
                    value={String(answer.points)} 
                    id={`q${currentQuestionIndex}-a${index}`} 
                  />
                  <span className="flex-1">{answer.text}</span>
                </Label>
              ))}
            </RadioGroup>
            <Button onClick={handleNextQuestion} disabled={selectedAnswer === null} className="w-full">
              {currentQuestionIndex === questions.length - 1 ? 'عرض النتيجة' : 'التالي'}
            </Button>
          </div>
        );
      case 'result':
        return (
          <div className="text-center space-y-4">
            <h3 className="text-2xl font-bold text-primary">{result.level}</h3>
            <p className="text-muted-foreground leading-relaxed">{result.description}</p>
            <p className="text-xs text-muted-foreground pt-4 border-t">إخلاء مسؤولية: هذا الاختبار هو لأغراض الترفيه والتأمل الذاتي، ومفهوم الرجولة أعمق وأشمل من أي اختبار.</p>
            <Button onClick={startTest} variant="outline" className="w-full">
                <Repeat className="ml-2 h-4 w-4" />
                أعد الاختبار
            </Button>
          </div>
        );
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
            <Shield className="h-6 w-6 text-primary" />
            اختبار الرجولة
        </CardTitle>
        <CardDescription>
            أجب عن الأسئلة التالية بصدق لاكتشاف جوانب القوة والمسؤولية في شخصيتك.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6">
            <Progress value={progress} />
            <p className="text-sm text-muted-foreground mt-2 text-center">
                {isFinished ? 'اكتمل الاختبار!' : `السؤال ${currentQuestionIndex + 1} من ${shuffledQuestions.length}`}
            </p>
        </div>
       
        {renderContent()}

      </CardContent>
    </Card>
  );
}

