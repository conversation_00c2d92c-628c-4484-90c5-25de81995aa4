
'use client';

import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';

// Extend Window interface for gtag
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}

export interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  advertising: boolean;
  social: boolean;
}

const defaultPreferences: CookiePreferences = {
  necessary: true,
  analytics: false,
  advertising: false,
  social: false,
};

interface CookieConsentContextType {
  preferences: CookiePreferences;
  showBanner: boolean;
  isSettingsOpen: boolean;
  setSettingsOpen: (isOpen: boolean) => void;
  setShowBanner: (show: boolean) => void;
  savePreferences: (prefs: CookiePreferences) => void;
  updatePreference: (key: keyof Omit<CookiePreferences, 'necessary'>, value: boolean) => void;
  acceptAll: () => void;
  acceptNecessary: () => void;
}

const CookieConsentContext = createContext<CookieConsentContextType | undefined>(undefined);

export function CookieConsentProvider({ children }: { children: ReactNode }) {
  const [preferences, setPreferences] = useState<CookiePreferences>(defaultPreferences);
  const [showBanner, setShowBanner] = useState(false);
  const [isSettingsOpen, setSettingsOpen] = useState(false);

  useEffect(() => {
    const consent = localStorage.getItem('cookie-consent');
    if (!consent) {
      const timer = setTimeout(() => setShowBanner(true), 2000);
      return () => clearTimeout(timer);
    } else {
      try {
        const savedPreferences: CookiePreferences = JSON.parse(consent);
        setPreferences(savedPreferences);
        applyPreferences(savedPreferences); 
      } catch (error) {
        console.error('Error parsing cookie preferences:', error);
      }
    }
  }, []);

  const applyPreferences = useCallback((prefs: CookiePreferences) => {
    if (typeof window.gtag !== 'function') return;

    window.gtag('consent', 'update', {
      analytics_storage: prefs.analytics ? 'granted' : 'denied',
      ad_storage: prefs.advertising ? 'granted' : 'denied',
      ad_user_data: prefs.advertising ? 'granted' : 'denied',
      ad_personalization: prefs.advertising ? 'granted' : 'denied',
    });
  }, []);

  const savePreferences = useCallback((prefs: CookiePreferences) => {
    const newPrefs = { ...prefs, necessary: true };
    localStorage.setItem('cookie-consent', JSON.stringify(newPrefs));
    setPreferences(newPrefs);
    setShowBanner(false);
    setSettingsOpen(false);
    applyPreferences(newPrefs);
  }, [applyPreferences]);

  const updatePreference = useCallback((key: keyof Omit<CookiePreferences, 'necessary'>, value: boolean) => {
    setPreferences(prev => ({ ...prev, [key]: value }));
  }, []);

  const acceptAll = useCallback(() => {
    const allAccepted: CookiePreferences = { necessary: true, analytics: true, advertising: true, social: true };
    savePreferences(allAccepted);
  }, [savePreferences]);

  const acceptNecessary = useCallback(() => {
    savePreferences(defaultPreferences);
  }, [savePreferences]);

  const value = {
    preferences,
    showBanner,
    isSettingsOpen,
    setSettingsOpen,
    setShowBanner,
    savePreferences,
    updatePreference,
    acceptAll,
    acceptNecessary,
  };

  return (
    <CookieConsentContext.Provider value={value}>
      {children}
    </CookieConsentContext.Provider>
  );
}

export function useCookieConsent() {
  const context = useContext(CookieConsentContext);
  if (context === undefined) {
    throw new Error('useCookieConsent must be used within a CookieConsentProvider');
  }
  return context;
}
