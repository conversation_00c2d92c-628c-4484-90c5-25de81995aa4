
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Copy } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  number: z.string().min(1, { message: 'الرجاء إدخال رقم' })
    .refine(value => !isNaN(parseFloat(value.replace(/,/g, ''))), {
      message: 'الرجاء إدخال رقم صحيح',
    }),
  currency: z.string().optional(),
  type: z.enum(['standard', 'currency']),
  suffix: z.string().optional(),
});

interface Result {
  arabic: string;
  english: string;
  french: string;
}

interface HistoryItem {
  input: string;
  output: string;
  type: 'standard' | 'currency';
}

export function NumberToWordsTool() {
  const [result, setResult] = useState<Result | null>(null);
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const { toast } = useToast();

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      number: '',
      currency: 'SAR',
      type: 'currency',
      suffix: 'لا غير',
    },
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const numberString = data.number.replace(/,/g, '');
    const number = parseFloat(numberString);
    
    let arabicText = '';
    let englishText = '';
    let frenchText = '';
    
    if (data.type === 'standard') {
      arabicText = convertNumberToArabicWords(number);
      englishText = convertNumberToEnglishWords(number);
      frenchText = convertNumberToFrenchWords(number);
    } else {
      arabicText = convertCurrencyToArabicWords(number, data.currency || 'SAR', data.suffix);
      englishText = convertCurrencyToEnglishWords(number, data.currency || 'SAR');
      frenchText = convertCurrencyToFrenchWords(number, data.currency || 'SAR');
    }
    setResult({ arabic: arabicText, english: englishText, french: frenchText });
    
    // Add to history
    const newHistoryItem: HistoryItem = { 
      input: data.number, 
      output: arabicText,
      type: data.type
    };
    setHistory(prevHistory => [newHistoryItem, ...prevHistory.slice(0, 4)]);
  }
  
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: "تم النسخ",
        description: "تم نسخ النص إلى الحافظة",
      });
    });
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>تفقيط الأرقام</CardTitle>
        <CardDescription>تحويل الأرقام إلى كلمات باللغة العربية الفصحى والإنجليزية والفرنسية</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>نوع التفقيط</FormLabel>
                  <FormControl>
                    <Tabs 
                      defaultValue={field.value} 
                      className="w-full" 
                      onValueChange={(value) => {
                        field.onChange(value);
                        form.trigger('number');
                      }}
                    >
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="currency">عملة</TabsTrigger>
                        <TabsTrigger value="standard">رقم عادي</TabsTrigger>
                      </TabsList>
                    </Tabs>
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>الرقم</FormLabel>
                  <FormControl>
                    <Input {...field} dir="ltr" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
              
            {form.watch('type') === 'currency' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>العملة</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر العملة" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="SAR">ريال سعودي</SelectItem>
                          <SelectItem value="AED">درهم إماراتي</SelectItem>
                          <SelectItem value="QAR">ريال قطري</SelectItem>
                          <SelectItem value="KWD">دينار كويتي</SelectItem>
                          <SelectItem value="BHD">دينار بحريني</SelectItem>
                          <SelectItem value="OMR">ريال عماني</SelectItem>
                          <SelectItem value="JOD">دينار أردني</SelectItem>
                          <SelectItem value="EGP">جنيه مصري</SelectItem>
                          <SelectItem value="USD">دولار أمريكي</SelectItem>
                          <SelectItem value="EUR">يورو</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="suffix"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>اللاحقة (اختياري)</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            )}
            
            <Button type="submit" className="w-full">
              تفقيط
            </Button>
          </form>
        </Form>
        
        {result && (
          <div className="mt-6 space-y-4">
            <h3 className="text-lg font-medium">النتائج:</h3>
            <Card className="bg-secondary/20">
                <CardContent className="p-4">
                     <p className="text-xl text-right mb-4">{result.arabic}</p>
                     <Button variant="outline" size="sm" onClick={() => copyToClipboard(result.arabic)}>
                        <Copy className="h-4 w-4 ml-2" />
                        نسخ
                    </Button>
                </CardContent>
            </Card>
             <Card className="bg-secondary/20">
                <CardContent className="p-4" dir="ltr">
                     <p className="text-xl text-left mb-4">{result.english}</p>
                     <Button variant="outline" size="sm" onClick={() => copyToClipboard(result.english)}>
                        <Copy className="h-4 w-4 mr-2" />
                        Copy
                    </Button>
                </CardContent>
            </Card>
             <Card className="bg-secondary/20">
                <CardContent className="p-4" dir="ltr">
                     <p className="text-xl text-left mb-4">{result.french}</p>
                     <Button variant="outline" size="sm" onClick={() => copyToClipboard(result.french)}>
                        <Copy className="h-4 w-4 mr-2" />
                        Copier
                    </Button>
                </CardContent>
            </Card>
          </div>
        )}
        
        {history.length > 0 && (
          <div className="mt-8">
            <h3 className="text-lg font-semibold mb-4">آخر التحويلات (بالعربية)</h3>
            <div className="space-y-2">
              {history.map((item, index) => (
                <Card key={index} className="bg-muted/50 p-3">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <p className="text-sm text-muted-foreground">المدخل: {item.input}</p>
                      <p className="text-base">{item.output}</p>
                    </div>
                    <Button variant="ghost" size="icon" onClick={() => copyToClipboard(item.output)}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// دالة تحويل الرقم إلى كلمات عربية
function convertNumberToArabicWords(number: number): string {
  if (isNaN(number)) return "رقم غير صالح";
  // الأرقام من 0 إلى 19
  const ones = [
    'صفر', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة',
    'ستة', 'سبعة', 'ثمانية', 'تسعة', 'عشرة',
    'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر',
    'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
  ];
  
  // العشرات من 20 إلى 90
  const tens = [
    '', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون',
    'ستون', 'سبعون', 'ثمانون', 'تسعون'
  ];
  
  // المئات من 100 إلى 900
  const hundreds = [
    '', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة',
    'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'
  ];
  
  // الواحدات الكبيرة (ألف، مليون، مليار)
  const groups = [
    '', 'ألف', 'مليون', 'مليار', 'تريليون'
  ];

  // دالة مساعدة لتحويل رقم أقل من 1000 إلى كلمات
  function convertLessThanThousand(num: number): string {
    if (num === 0) {
      return '';
    }
    
    let result = '';
    
    // المئات
    const hundred = Math.floor(num / 100);
    if (hundred > 0) {
      result += hundreds[hundred] + ' ';
    }
    
    // العشرات والآحاد
    const remainder = num % 100;
    if (remainder > 0) {
      if (result.length > 0) {
         result += 'و'
      }
      if (remainder < 20) {
        result += ones[remainder];
      } else {
        const ten = Math.floor(remainder / 10);
        const one = remainder % 10;
        
        if (one > 0) {
          result += ones[one] + ' و';
        }
        result += tens[ten];
      }
    }
    
    return result.trim();
  }
  
  if (number === 0) {
    return ones[0];
  }
  
  const isNegative = number < 0;
  let num = Math.abs(number);
  
  // تحويل الرقم إلى سلسلة من الأرقام الثلاثية (مجموعات)
  const numStr = num.toString();
  const groups3 = [];
  
  for (let i = numStr.length; i > 0; i -= 3) {
    groups3.unshift(parseInt(numStr.substring(Math.max(0, i - 3), i), 10));
  }
  
  let result = '';
  
  // معالجة كل مجموعة
  for (let i = 0; i < groups3.length; i++) {
    const groupValue = groups3[i];
    if (groupValue === 0) continue;
    
    const groupIndex = groups3.length - 1 - i;
    let words = convertLessThanThousand(groupValue);
    
    // إضافة اسم المجموعة (ألف، مليون، مليار)
    if (groupIndex > 0) {
      // حالات خاصة للألف
      if (groupIndex === 1) {
        if (groupValue === 1) {
          words = 'ألف';
        } else if (groupValue === 2) {
          words = 'ألفان';
        } else if (groupValue >= 3 && groupValue <= 10) {
           words = convertLessThanThousand(groupValue) + ' آلاف'
        } else {
          words += ' ألفًا';
        }
      } 
      // حالات خاصة للمليون
      else if (groupIndex === 2) {
        if (groupValue === 1) {
          words = 'مليون';
        } else if (groupValue === 2) {
          words = 'مليونان';
        } else if (groupValue >= 3 && groupValue <= 10) {
           words = convertLessThanThousand(groupValue) + ' ملايين'
        } else {
          words += ' مليونًا';
        }
      }
      // حالات خاصة للمليار
      else if (groupIndex === 3) {
        if (groupValue === 1) {
          words = 'مليار';
        } else if (groupValue === 2) {
          words = 'ملياران';
        } else if (groupValue >= 3 && groupValue <= 10) {
          words = convertLessThanThousand(groupValue) + ' مليارات'
        } else {
          words += ' مليارًا';
        }
      }
    }
    
    if (result !== '') {
      result = words + ' و' + result;
    } else {
      result = words;
    }
  }
  
  return isNegative ? 'سالب ' + result : result;
}

// دالة تحويل العملة إلى كلمات عربية
function convertCurrencyToArabicWords(amount: number, currencyCode: string, suffix?: string): string {
  if (isNaN(amount)) return "مبلغ غير صالح";
  const currencies: Record<string, { singular: string, plural: string, fraction: string, fractionPlural: string }> = {
    'SAR': { singular: 'ريال سعودي', plural: 'ريالات سعودية', fraction: 'هللة', fractionPlural: 'هللات' },
    'AED': { singular: 'درهم إماراتي', plural: 'دراهم إماراتية', fraction: 'فلس', fractionPlural: 'فلوس' },
    'QAR': { singular: 'ريال قطري', plural: 'ريالات قطرية', fraction: 'درهم', fractionPlural: 'دراهم' },
    'KWD': { singular: 'دينار كويتي', plural: 'دنانير كويتية', fraction: 'فلس', fractionPlural: 'فلوس' },
    'BHD': { singular: 'دينار بحريني', plural: 'دنانير بحرينية', fraction: 'فلس', fractionPlural: 'فلوس' },
    'OMR': { singular: 'ريال عماني', plural: 'ريالات عمانية', fraction: 'بيسة', fractionPlural: 'بيسات' },
    'JOD': { singular: 'دينار أردني', plural: 'دنانير أردنية', fraction: 'قرش', fractionPlural: 'قروش' },
    'EGP': { singular: 'جنيه مصري', plural: 'جنيهات مصرية', fraction: 'قرش', fractionPlural: 'قروش' },
    'USD': { singular: 'دولار أمريكي', plural: 'دولارات أمريكية', fraction: 'سنت', fractionPlural: 'سنتات' },
    'EUR': { singular: 'يورو', plural: 'يورو', fraction: 'سنت', fractionPlural: 'سنتات' },
  };
  
  const currency = currencies[currencyCode] || { singular: 'وحدة', plural: 'وحدات', fraction: 'جزء', fractionPlural: 'أجزاء' };
  
  // فصل العدد الصحيح والكسري
  const parts = amount.toFixed(2).split('.');
  const wholePart = parseInt(parts[0]);
  const fractionPart = parseInt(parts[1]);
  
  // تحويل العدد الصحيح إلى كلمات
  let result = convertNumberToArabicWords(wholePart);
  
  // إضافة اسم العملة
  if (wholePart === 0) {
    // لا شيء
  } else if (wholePart === 1) {
    result = currency.singular;
  } else if (wholePart === 2) {
    result = currency.singular.replace(/[يةه]$/i, 'ان');
  } else if (wholePart >= 3 && wholePart <= 10) {
    result += ' ' + currency.plural;
  } else {
    result += ' ' + currency.singular;
  }
  
  // إضافة الكسور
  if (fractionPart > 0) {
    let fractionWords = convertNumberToArabicWords(fractionPart);
    
    if (wholePart > 0) {
      result += ' و';
    } else {
      result = '';
    }
    
    if (fractionPart === 1) {
      result += ' ' + currency.fraction + ' واحدة';
    } else if (fractionPart === 2) {
      result += ' ' + currency.fraction.replace(/[ة]$/i, 'تان');
    } else if (fractionPart >= 3 && fractionPart <= 10) {
      result += fractionWords + ' ' + currency.fractionPlural;
    } else {
      result += fractionWords + ' ' + currency.fraction;
    }
  }
  
  const finalSuffix = (suffix !== undefined && suffix.trim() !== '') ? ` ${suffix.trim()}` : '';
  return 'فقط ' + result + finalSuffix;
}


// --- ENGLISH CONVERTER ---
function convertNumberToEnglishWords(number: number): string {
    if (isNaN(number)) return "Invalid number";
    const ones = ['', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'];
    const teens = ['ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'];
    const tens = ['', '', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];
    const groups = ['', 'thousand', 'million', 'billion', 'trillion'];

    if (number === 0) return 'zero';
    if (number < 0) return 'negative ' + convertNumberToEnglishWords(Math.abs(number));

    let result = '';

    function convertLessThanThousand(num: number): string {
        if (num === 0) return '';
        if (num < 10) return ones[num];
        if (num < 20) return teens[num - 10];
        if (num < 100) {
            return tens[Math.floor(num / 10)] + (num % 10 !== 0 ? '-' + ones[num % 10] : '');
        }
        return ones[Math.floor(num / 100)] + ' hundred' + (num % 100 !== 0 ? ' ' + convertLessThanThousand(num % 100) : '');
    }

    let i = 0;
    while (number > 0) {
        if (number % 1000 !== 0) {
            result = convertLessThanThousand(number % 1000) + ' ' + groups[i] + ' ' + result;
        }
        number = Math.floor(number / 1000);
        i++;
    }

    return result.trim().replace(/\s+/g, ' ');
}

function convertCurrencyToEnglishWords(amount: number, currencyCode: string): string {
    if (isNaN(amount)) return "Invalid amount";
    const currencies: Record<string, { singular: string, plural: string, fraction: string, fractionPlural: string }> = {
        'SAR': { singular: 'Saudi Riyal', plural: 'Saudi Riyals', fraction: 'Halala', fractionPlural: 'Halalas' },
        'AED': { singular: 'UAE Dirham', plural: 'UAE Dirhams', fraction: 'Fils', fractionPlural: 'Fils' },
        'QAR': { singular: 'Qatari Riyal', plural: 'Qatari Riyals', fraction: 'Dirham', fractionPlural: 'Dirhams' },
        'KWD': { singular: 'Kuwaiti Dinar', plural: 'Kuwaiti Dinars', fraction: 'Fils', fractionPlural: 'Fils' },
        'BHD': { singular: 'Bahraini Dinar', plural: 'Bahraini Dinars', fraction: 'Fils', fractionPlural: 'Fils' },
        'OMR': { singular: 'Omani Rial', plural: 'Omani Rials', fraction: 'Baisa', fractionPlural: 'Baisas' },
        'JOD': { singular: 'Jordanian Dinar', plural: 'Jordanian Dinars', fraction: 'Piastre', fractionPlural: 'Piastres' },
        'EGP': { singular: 'Egyptian Pound', plural: 'Egyptian Pounds', fraction: 'Piastre', fractionPlural: 'Piastres' },
        'USD': { singular: 'US Dollar', plural: 'US Dollars', fraction: 'Cent', fractionPlural: 'Cents' },
        'EUR': { singular: 'Euro', plural: 'Euros', fraction: 'Cent', fractionPlural: 'Cents' },
    };
    
    const currency = currencies[currencyCode] || { singular: 'Unit', plural: 'Units', fraction: 'Part', fractionPlural: 'Parts' };
    const parts = amount.toFixed(2).split('.');
    const wholePart = parseInt(parts[0], 10);
    const fractionPart = parseInt(parts[1], 10);

    let result = convertNumberToEnglishWords(wholePart) + ' ' + (wholePart === 1 ? currency.singular : currency.plural);

    if (fractionPart > 0) {
        result += ' and ' + convertNumberToEnglishWords(fractionPart) + ' ' + (fractionPart === 1 ? currency.fraction : currency.fractionPlural);
    }
    
    return result;
}

// --- FRENCH CONVERTER ---
function convertNumberToFrenchWords(number: number): string {
    if (isNaN(number)) return "Nombre invalide";
    const ones = ['', 'un', 'deux', 'trois', 'quatre', 'cinq', 'six', 'sept', 'huit', 'neuf'];
    const teens = ['dix', 'onze', 'douze', 'treize', 'quatorze', 'quinze', 'seize', 'dix-sept', 'dix-huit', 'dix-neuf'];
    const tens = ['', 'dix', 'vingt', 'trente', 'quarante', 'cinquante', 'soixante', 'soixante-dix', 'quatre-vingt', 'quatre-vingt-dix'];
    const groups = ['', 'mille', 'million', 'milliard', 'trillion'];

    if (number === 0) return 'zéro';
    if (number < 0) return 'moins ' + convertNumberToFrenchWords(Math.abs(number));

    function convertLessThanThousand(num: number): string {
        if (num === 0) return '';
        if (num < 10) return ones[num];
        if (num < 20) return teens[num - 10];
        if (num < 70) {
            const ten = Math.floor(num / 10);
            const one = num % 10;
            if (one === 1 && ten !== 8) return tens[ten] + '-et-un';
            return tens[ten] + (one !== 0 ? '-' + ones[one] : '');
        }
        if (num < 80) return 'soixante-' + convertLessThanThousand(num - 60);
        if (num < 100) {
            if (num === 80) return 'quatre-vingts';
            return 'quatre-vingt-' + convertLessThanThousand(num - 80);
        }
        
        const hundred = Math.floor(num / 100);
        const remainder = num % 100;
        let hundredStr = hundred > 1 ? convertLessThanThousand(hundred) + ' cent' : 'cent';
        if (hundred > 1 && remainder === 0) hundredStr += 's';

        return hundredStr + (remainder !== 0 ? ' ' + convertLessThanThousand(remainder) : '');
    }

    let result = [];
    let i = 0;
    while (number > 0) {
        if (number % 1000 !== 0) {
            const groupValue = number % 1000;
            let groupStr = '';
            if (groupValue === 1 && i > 0) {
                groupStr = groups[i];
            } else {
                groupStr = convertLessThanThousand(groupValue) + ' ' + groups[i];
            }
             if (groupValue > 1 && (groups[i] === 'million' || groups[i] === 'milliard')) {
                groupStr += 's';
            }
            result.unshift(groupStr.trim());
        }
        number = Math.floor(number / 1000);
        i++;
    }

    return result.join(' ').trim().replace(/\s+/g, ' ');
}

function convertCurrencyToFrenchWords(amount: number, currencyCode: string): string {
     if (isNaN(amount)) return "Montant invalide";
     const currencies: Record<string, { singular: string, plural: string, fraction: string, fractionPlural: string }> = {
        'SAR': { singular: 'Riyal Saoudien', plural: 'Riyals Saoudiens', fraction: 'Halala', fractionPlural: 'Halalas' },
        'AED': { singular: 'Dirham des Émirats Arabes Unis', plural: 'Dirhams des Émirats Arabes Unis', fraction: 'Fils', fractionPlural: 'Fils' },
        'QAR': { singular: 'Riyal Qatari', plural: 'Riyals Qataris', fraction: 'Dirham', fractionPlural: 'Dirhams' },
        'KWD': { singular: 'Dinar Koweïtien', plural: 'Dinars Koweïtiens', fraction: 'Fils', fractionPlural: 'Fils' },
        'BHD': { singular: 'Dinar Bahreïni', plural: 'Dinars Bahreïnis', fraction: 'Fils', fractionPlural: 'Fils' },
        'OMR': { singular: 'Rial Omani', plural: 'Rials Omanis', fraction: 'Baisa', fractionPlural: 'Baisas' },
        'JOD': { singular: 'Dinar Jordanien', plural: 'Dinars Jordaniens', fraction: 'Piastre', fractionPlural: 'Piastres' },
        'EGP': { singular: 'Livre Égyptienne', plural: 'Livres Égyptiennes', fraction: 'Piastre', fractionPlural: 'Piastres' },
        'USD': { singular: 'Dollar Américain', plural: 'Dollars Américains', fraction: 'Cent', fractionPlural: 'Cents' },
        'EUR': { singular: 'Euro', plural: 'Euros', fraction: 'Cent', fractionPlural: 'Cents' },
    };

    const currency = currencies[currencyCode] || { singular: 'Unité', plural: 'Unités', fraction: 'Partie', fractionPlural: 'Parties' };
    const parts = amount.toFixed(2).split('.');
    const wholePart = parseInt(parts[0], 10);
    const fractionPart = parseInt(parts[1], 10);

    let result = convertNumberToFrenchWords(wholePart) + ' ' + (wholePart > 1 ? currency.plural : currency.singular);
    
    if (fractionPart > 0) {
        result += ' et ' + convertNumberToFrenchWords(fractionPart) + ' ' + (fractionPart > 1 ? currency.fractionPlural : currency.fraction);
    }
    
    return result;
}

    