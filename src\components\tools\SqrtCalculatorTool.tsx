
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  number: requiredNumber().nonnegative({ message: 'الرجاء إدخال رقم موجب أو صفر.' }),
});

export function SqrtCalculatorTool() {
  const [result, setResult] = useState<number | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    setResult(Math.sqrt(data.number));
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حساب الجذر التربيعي</CardTitle>
        <CardDescription>أدخل رقمًا لإيجاد جذره التربيعي.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>الرقم</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="مثال: 81" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full">
              احسب الجذر التربيعي
            </Button>
          </form>
        </Form>
        {result !== null && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-2">النتيجة</h3>
            <div className="p-6 bg-primary/10 rounded-lg">
                <p className="text-5xl font-bold font-mono text-primary">
                    {result.toLocaleString(undefined, { maximumFractionDigits: 10 })}
                </p>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
                الجذر التربيعي للرقم {form.getValues().number} هو {result.toLocaleString(undefined, { maximumFractionDigits: 10 })}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
