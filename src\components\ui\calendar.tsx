
"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { DayPicker, DropdownProps } from "react-day-picker"
import HijriDate from "hijri-date"
import { ar } from "date-fns/locale"

import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./select"
import { ScrollArea } from "./scroll-area"

// --- Hijri Helpers ---
const hijriMonths = [
  "محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأولى", "جمادى الآخرة",
  "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
];

function HijriDropdown({ name, value, onChange, fromYear, toYear, fromMonth, toMonth }: DropdownProps) {
  const currentHijriYear = new HijriDate().getFullYear();
  const startYear = fromYear || fromMonth?.getFullYear() || currentHijriYear - 100;
  const endYear = toYear || toMonth?.getFullYear() || currentHijriYear + 10;
  
  const selectedHijri = value ? new HijriDate(value) : new HijriDate();
  
  const handleMonthChange = (newMonthIndex: string) => {
    if (!onChange) return;
    const newDate = new HijriDate(selectedHijri.getFullYear(), parseInt(newMonthIndex) + 1, 1).toGregorian();
    onChange(newDate);
  };

  const handleYearChange = (newYear: string) => {
    if (!onChange) return;
    const newDate = new HijriDate(parseInt(newYear), selectedHijri.getMonth(), 1).toGregorian();
    onChange(newDate);
  };

  if (name === 'months') {
    return (
      <Select onValueChange={handleMonthChange} value={String(selectedHijri.getMonth() - 1)}>
        <SelectTrigger className="w-fit h-auto p-1 text-xs">
          <SelectValue>{hijriMonths[selectedHijri.getMonth() - 1]}</SelectValue>
        </SelectTrigger>
        <SelectContent>
          <ScrollArea className="h-64">
            {hijriMonths.map((month, i) => (
              <SelectItem key={i} value={String(i)}>{month}</SelectItem>
            ))}
          </ScrollArea>
        </SelectContent>
      </Select>
    );
  }

  if (name === 'years') {
    const years = Array.from({ length: endYear - startYear + 1 }, (_, i) => startYear + i);
    return (
      <Select onValueChange={handleYearChange} value={String(selectedHijri.getFullYear())}>
        <SelectTrigger className="w-fit h-auto p-1 text-xs">
          <SelectValue>{selectedHijri.getFullYear()}</SelectValue>
        </SelectTrigger>
        <SelectContent>
          <ScrollArea className="h-64">
            {years.map((year) => (
              <SelectItem key={year} value={String(year)}>{year}</SelectItem>
            ))}
          </ScrollArea>
        </SelectContent>
      </Select>
    );
  }
  return null;
}

function GregorianDropdown({ name, value, onChange, fromYear, toYear, fromMonth, toMonth, locale }: DropdownProps) {
    const dateValue = value instanceof Date ? value : new Date();

    const startYear = fromYear || fromMonth?.getFullYear() || new Date().getFullYear() - 100;
    const endYear = toYear || toMonth?.getFullYear() || new Date().getFullYear() + 10;
    
    if (name === "months") {
        const months = Array.from({ length: 12 }, (_, i) => ({
            value: i,
            label: new Date(2000, i).toLocaleDateString(locale, { month: 'long' })
        }));
        
        return (
            <Select
                value={String(dateValue.getMonth())}
                onValueChange={(newValue) => {
                    const newDate = new Date(dateValue);
                    newDate.setMonth(parseInt(newValue, 10));
                    onChange?.(newDate);
                }}
            >
                <SelectTrigger className="w-fit h-auto p-1 text-xs">
                    <SelectValue>{dateValue.toLocaleDateString(locale, { month: 'long' })}</SelectValue>
                </SelectTrigger>
                <SelectContent>
                    <ScrollArea className="h-64">
                        {months.map((month) => (
                            <SelectItem key={month.value} value={String(month.value)}>
                                {month.label}
                            </SelectItem>
                        ))}
                    </ScrollArea>
                </SelectContent>
            </Select>
        );
    }
    
    if (name === "years") {
        const years = Array.from({ length: endYear - startYear + 1 }, (_, i) => startYear + i);
        
        return (
            <Select
                value={String(dateValue.getFullYear())}
                onValueChange={(newValue) => {
                    const newDate = new Date(dateValue);
                    newDate.setFullYear(parseInt(newValue, 10));
                    onChange?.(newDate);
                }}
            >
                <SelectTrigger className="w-fit h-auto p-1 text-xs">
                    <SelectValue>{dateValue.getFullYear()}</SelectValue>
                </SelectTrigger>
                <SelectContent>
                    <ScrollArea className="h-64">
                        {years.map((year) => (
                            <SelectItem key={year} value={String(year)}>
                                {year}
                            </SelectItem>
                        ))}
                    </ScrollArea>
                </SelectContent>
            </Select>
        );
    }
    
    return null;
}

// --- Main Calendar Component ---
export type CalendarProps = React.ComponentProps<typeof DayPicker> & { calendarType?: 'gregorian' | 'hijri' };

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  calendarType = 'gregorian',
  ...props
}: CalendarProps) {
  const isHijri = calendarType === 'hijri';

  const formatters = isHijri ? {
    formatCaption: (date: Date) => {
      const hijri = new HijriDate(date);
      return `${hijriMonths[hijri.getMonth() - 1]} ${hijri.getFullYear()}`;
    },
    formatDay: (date: Date) => new HijriDate(date).getDate().toString(),
    formatWeekday: (date: Date) => new Intl.DateTimeFormat('ar-SA', { weekday: 'short' }).format(date),
  } : {
    formatWeekday: (date: Date) => new Intl.DateTimeFormat('ar-SA', { weekday: 'short' }).format(date),
  };
  
  const currentHijriYear = new HijriDate().getFullYear();
  const currentGregorianYear = new Date().getFullYear();

  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn("p-3", className)}
      classNames={{
        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
        month: "space-y-4",
        caption: "flex justify-center pt-1 relative items-center",
        caption_label: "text-sm font-medium hidden",
        caption_dropdowns: "flex justify-center gap-2",
        nav: "space-x-1 flex items-center",
        nav_button: cn(
          buttonVariants({ variant: "outline" }),
          "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
        ),
        nav_button_previous: "absolute right-1",
        nav_button_next: "absolute left-1",
        table: "w-full border-collapse space-y-1",
        head_row: "flex",
        head_cell: "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
        row: "flex w-full mt-2",
        cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
        day: cn(
          buttonVariants({ variant: "ghost" }),
          "h-9 w-9 p-0 font-normal aria-selected:opacity-100"
        ),
        day_range_end: "day-range-end",
        day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
        day_today: "bg-accent text-accent-foreground",
        day_outside: "day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground",
        day_disabled: "text-muted-foreground opacity-50",
        day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
        day_hidden: "invisible",
        ...classNames,
      }}
      components={{
        IconLeft: ({ ...props }) => <ChevronRight className="h-4 w-4" />,
        IconRight: ({ ...props }) => <ChevronLeft className="h-4 w-4" />,
        Dropdown: isHijri ? HijriDropdown : GregorianDropdown,
      }}
      captionLayout="dropdown-buttons"
      fromYear={props.fromYear || (isHijri ? currentHijriYear - 100 : currentGregorianYear - 100)}
      toYear={props.toYear || (isHijri ? currentHijriYear + 10 : currentGregorianYear + 10)}
      formatters={formatters}
      locale={ar}
      dir="rtl"
      {...props}
    />
  )
}
Calendar.displayName = "Calendar"

export { Calendar }
