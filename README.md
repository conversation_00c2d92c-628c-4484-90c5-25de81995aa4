# أدوات بالعربي - مجموعة أدوات عربية شاملة

موقع إلكتروني مجاني يوفر مجموعة شاملة من الأدوات والحاسبات والمحولات باللغة العربية.

## المميزات

- 🌐 **واجهة عربية كاملة** - تصميم متجاوب يدعم اللغة العربية
- 🔧 **أدوات متنوعة** - محولات التاريخ، حاسبات مالية، أدوات النصوص
- 📱 **متجاوب** - يعمل على جميع الأجهزة
- 🚀 **سريع وآمن** - معالجة البيانات محلياً
- 💰 **مجاني بالكامل** - جميع الأدوات متاحة مجاناً

## التقنيات المستخدمة

- **Next.js 15** - إط<PERSON>ر عمل React
- **TypeScript** - للكتابة الآمنة
- **Tailwind CSS** - للتصميم
- **Radix UI** - مكونات واجهة المستخدم
- **Firebase** - قاعدة البيانات والاستضافة

## الإعداد للتطوير

1. **استنساخ المشروع**
```bash
git clone https://github.com/Ridanotx/Jami3Eladawat.git
cd Jami3Eladawat
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **إعداد متغيرات البيئة**
أنشئ ملف `.env.local` وأضف:
```env
NEXT_PUBLIC_SITE_URL=https://adawat.org/
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
NEXT_PUBLIC_ADSENSE_CLIENT=ca-pub-XXXXXXXXXXXXXXXXX
```

4. **تشغيل الخادم المحلي**
```bash
npm run dev
```

افتح [http://localhost:9003](http://localhost:9003) في متصفحك.

## إعداد Google AdSense

### 1. إنشاء حساب AdSense
- اذهب إلى [Google AdSense](https://www.google.com/adsense/)
- أنشئ حساباً جديداً أو سجل دخولك
- أضف موقعك للمراجعة

### 2. تحديث ملف ads.txt
- افتح `public/ads.txt`
- استبدل `pub-XXXXXXXXXXXXXXXXX` برقم الناشر الخاص بك
- يمكنك العثور على رقم الناشر في حسابك AdSense

### 3. إضافة معرف AdSense
- أضف معرف AdSense إلى `.env.local`:
```env
NEXT_PUBLIC_ADSENSE_CLIENT=ca-pub-1234567890123456
```

### 4. تفعيل Auto Ads (اختياري)
Auto Ads مُفعل تلقائياً في الموقع. يمكنك التحكم فيه من لوحة تحكم AdSense.

## إعداد Google Analytics

1. **إنشاء خاصية GA4**
   - اذهب إلى [Google Analytics](https://analytics.google.com/)
   - أنشئ خاصية جديدة لموقعك

2. **إضافة معرف التتبع**
   - أضف معرف GA4 إلى `.env.local`:
   ```env
   NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
   ```

## البناء والنشر

```bash
# بناء المشروع للإنتاج
npm run build

# تشغيل النسخة المبنية
npm start

# فحص الأنواع
npm run typecheck
```

## هيكل المشروع

```
src/
├── app/                 # صفحات التطبيق (App Router)
│   ├── articles/       # صفحات المقالات
│   ├── categories/     # صفحات الفئات
│   ├── p/             # الصفحات الثابتة
│   ├── tools/         # صفحات الأدوات
│   └── year/          # صفحات السنوات
├── components/         # مكونات React
│   ├── ui/           # مكونات واجهة المستخدم
│   └── tools/        # مكونات الأدوات
├── lib/               # مكتبات ووظائف مساعدة
└── hooks/            # React Hooks مخصصة

public/
├── robots.txt        # إعدادات محركات البحث
├── ads.txt          # إعدادات AdSense
├── manifest.json    # إعدادات PWA
└── sitemap.xml      # خريطة الموقع
```

## متطلبات Google AdSense

### الصفحات المطلوبة ✅
- [x] سياسة الخصوصية (`/p/privacy-policy`)
- [x] شروط الخدمة (`/p/terms-of-service`)
- [x] صفحة اتصل بنا (`/p/contact`)
- [x] صفحة حول الموقع (`/p/about`)

### المتطلبات التقنية ✅
- [x] ملف `ads.txt` صحيح
- [x] ملف `robots.txt` محسن
- [x] خريطة موقع XML
- [x] تحسين SEO
- [x] تصميم متجاوب
- [x] سرعة تحميل جيدة

### المحتوى ✅
- [x] محتوى أصلي ومفيد
- [x] مقالات تعليمية
- [x] أدوات تفاعلية
- [x] تجربة مستخدم ممتازة

## نصائح لقبول AdSense

1. **المحتوى الأصلي**: تأكد من أن جميع المحتويات أصلية ومفيدة
2. **التنقل السهل**: اجعل الموقع سهل التنقل والاستخدام
3. **الصفحات المطلوبة**: تأكد من وجود جميع الصفحات المطلوبة
4. **حركة المرور**: احصل على زوار حقيقيين قبل التقديم
5. **الامتثال للسياسات**: التزم بجميع سياسات Google AdSense

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## التواصل

- الموقع: [adawat.org](https://adawat.org/)
- البريد الإلكتروني: <EMAIL>

---

**ملاحظة**: تأكد من تحديث معرفات Google Analytics و AdSense في ملف `.env.local` قبل النشر.
