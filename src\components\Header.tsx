'use client';

import Link from 'next/link';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';

export function Header() {
  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur-sm">
      <div className="w-full flex h-16 items-center justify-between px-4 md:px-6">
        <div className="flex items-center gap-2">
           <SidebarTrigger className="text-foreground" />
            <nav className="hidden md:flex items-center gap-2">
                <Button variant="ghost" asChild className="text-foreground">
                    <Link href="/">
                    الرئيسية
                    </Link>
                </Button>
                <Button variant="ghost" asChild className="text-foreground">
                    <Link href="/tools">
                    الأدوات
                    </Link>
                </Button>
            </nav>
            
        </div>
        <Link href="/" className="flex items-center gap-2 font-headline text-lg font-bold text-primary">
          أدوات بالعربي
        </Link>
      </div>
    </header>
  );
}
