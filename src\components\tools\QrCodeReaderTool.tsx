
'use client';

import { useState, useRef, useEffect } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Upload, Copy, ExternalLink, XCircle, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Html5Qrcode, Html5QrcodeScannerState } from 'html5-qrcode';

export function QrCodeReaderTool() {
  const { toast } = useToast();
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isScanning, setIsScanning] = useState(false);
  const scannerRef = useRef<Html5Qrcode | null>(null);

  useEffect(() => {
    // Cleanup function to stop the scanner when the component unmounts
    return () => {
      if (scannerRef.current && scannerRef.current.isScanning) {
        scannerRef.current.stop().catch(err => {
          console.error("Failed to stop scanner on cleanup:", err);
        });
      }
    };
  }, []);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    setResult(null);

    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const qrCode = new Html5Qrcode('qr-reader-placeholder');
      const decodedText = await qrCode.scanFile(file, false);
      setResult(decodedText);
      toast({ title: "تم العثور على الرمز!", description: "تم فك تشفير رمز QR بنجاح." });
    } catch (err) {
      console.error("QR Code scanning error:", err);
      setError('لم يتم العثور على رمز QR صالح في الصورة. يرجى التأكد من أن الصورة واضحة وأن الرمز كامل.');
    }
  };

  const copyToClipboard = () => {
    if (result) {
      navigator.clipboard.writeText(result);
      toast({ title: "تم النسخ إلى الحافظة" });
    }
  };

  const isUrl = (text: string | null): boolean => {
    if (!text) return false;
    try {
      new URL(text);
      const url = new URL(text);
      return url.protocol === "http:" || url.protocol === "https:";
    } catch (_) {
      return false;
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>قارئ رمز QR</CardTitle>
        <CardDescription>فك تشفير رموز QR عن طريق رفع صورة من جهازك.</CardDescription>
      </CardHeader>
      <CardContent>
        {/* Placeholder for the library */}
        <div id="qr-reader-placeholder" style={{ display: 'none' }}></div>
        
        <div className="mt-4 p-4 border-dashed border-2 rounded-lg text-center">
          <Button onClick={() => fileInputRef.current?.click()}>
            <Upload className="ml-2 h-4 w-4" />
            اختر صورة من جهازك
          </Button>
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept="image/*"
            onChange={handleFileChange}
          />
        </div>

        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>خطأ في قراءة الصورة</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {result && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-2">البيانات المستخرجة:</h3>
            <div className="relative">
              <Textarea
                readOnly
                value={result}
                className="min-h-[120px] bg-muted pr-12"
              />
              <div className="absolute top-2 left-2 flex flex-col gap-2">
                <Button variant="outline" size="icon" onClick={copyToClipboard} title="نسخ">
                  <Copy className="h-4 w-4" />
                </Button>
                {isUrl(result) && (
                  <Button variant="outline" size="icon" asChild title="فتح الرابط">
                    <a href={result!} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
         <div className="mt-6 text-center">
            <p className="text-xs text-muted-foreground">
                خصوصيتك مهمة: تتم معالجة الصور بالكامل داخل متصفحك. لا يتم رفع أي صور إلى خوادمنا.
            </p>
         </div>
      </CardContent>
    </Card>
  );
}
